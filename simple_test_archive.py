#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def simple_test():
    """اختبار مبسط للنظام"""
    print("🧪 اختبار مبسط لنظام الحسابات المرحلة")
    print("=" * 50)
    
    try:
        # فحص وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص جدول monthly_duties
        print("1️⃣ فحص جدول monthly_duties:")
        try:
            cursor.execute("SELECT COUNT(*) FROM monthly_duties")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {count}")
            
            if count > 0:
                cursor.execute("SELECT DISTINCT month, year FROM monthly_duties LIMIT 3")
                months = cursor.fetchall()
                print("   📅 عينة من الشهور:")
                for month, year in months:
                    print(f"      - {month}/{year}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # 2. فحص جدول الحسابات المرحلة
        print("\n2️⃣ فحص جدول الحسابات المرحلة:")
        try:
            cursor.execute("SELECT COUNT(*) FROM الحسابات_المرحلة")
            archived_count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات المرحلة: {archived_count}")
        except Exception as e:
            print(f"   ❌ الجدول غير موجود أو خطأ: {e}")
            print("   🔧 سيتم إنشاء الجدول...")
            
            # إنشاء الجدول
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الحسابات_المرحلة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    القسم TEXT NOT NULL,
                    اسم_الاستاذ TEXT,
                    المجموعة TEXT,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL DEFAULT 0,
                    amount_paid REAL DEFAULT 0,
                    amount_remaining REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'غير مدفوع',
                    payment_date TEXT,
                    notes TEXT,
                    ترحيل_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ترحيل_by TEXT DEFAULT 'النظام',
                    is_archived BOOLEAN DEFAULT 1,
                    UNIQUE(student_id, month, year)
                )
            """)
            conn.commit()
            print("   ✅ تم إنشاء الجدول")
        
        # 3. ترحيل شهر واحد للاختبار
        print("\n3️⃣ ترحيل شهر للاختبار:")
        try:
            # جلب أول شهر متاح
            cursor.execute("""
                SELECT month, year, COUNT(*) 
                FROM monthly_duties 
                GROUP BY month, year 
                LIMIT 1
            """)
            test_data = cursor.fetchone()
            
            if test_data:
                test_month, test_year, record_count = test_data
                print(f"   🎯 اختبار ترحيل: {test_month}/{test_year} ({record_count} سجل)")
                
                # حذف الترحيل السابق إن وجد
                cursor.execute("""
                    DELETE FROM الحسابات_المرحلة 
                    WHERE month = ? AND year = ?
                """, (test_month, test_year))
                
                # ترحيل البيانات
                cursor.execute("""
                    INSERT INTO الحسابات_المرحلة 
                    (student_id, اسم_التلميذ, رمز_التلميذ, القسم, اسم_الاستاذ, المجموعة,
                     month, year, amount_required, amount_paid, amount_remaining,
                     payment_status, payment_date, notes)
                    SELECT 
                        md.student_id,
                        jb.اسم_التلميذ,
                        jb.رمز_التلميذ,
                        COALESCE(md.القسم, jb.القسم) as القسم,
                        COALESCE(md.اسم_الاستاذ, 'غير محدد') as اسم_الاستاذ,
                        mat.المجموعة,
                        md.month,
                        md.year,
                        md.amount_required,
                        md.amount_paid,
                        md.amount_remaining,
                        md.payment_status,
                        md.payment_date,
                        md.notes
                    FROM monthly_duties md
                    JOIN جدول_البيانات jb ON md.student_id = jb.id
                    LEFT JOIN جدول_المواد_والاقسام mat ON COALESCE(md.القسم, jb.القسم) = mat.القسم
                    WHERE md.month = ? AND md.year = ?
                """, (test_month, test_year))
                
                archived_count = cursor.rowcount
                conn.commit()
                
                print(f"   ✅ تم ترحيل {archived_count} سجل")
                
                # 4. اختبار جلب البيانات المرحلة
                print("\n4️⃣ اختبار جلب البيانات المرحلة:")
                cursor.execute("""
                    SELECT اسم_التلميذ, القسم, amount_paid, اسم_الاستاذ
                    FROM الحسابات_المرحلة
                    WHERE month = ? AND year = ? AND القسم = 'قسم / 01'
                    LIMIT 3
                """, (test_month, test_year))
                
                sample_data = cursor.fetchall()
                if sample_data:
                    print(f"   📋 عينة من البيانات المرحلة (قسم / 01):")
                    for data in sample_data:
                        print(f"      - {data[0]} | {data[1]} | {data[2]:.2f} درهم | أستاذ: {data[3]}")
                else:
                    print("   ⚠️ لا توجد بيانات لقسم / 01")
                
                # 5. اختبار التقرير
                print("\n5️⃣ اختبار وظيفة التقرير:")
                try:
                    from print_section_monthly import get_monthly_duties_by_section_month
                    
                    report_data = get_monthly_duties_by_section_month('data.db', 'قسم / 01', test_month, test_year)
                    
                    if report_data:
                        print(f"   ✅ تم جلب {len(report_data)} سجل للتقرير")
                        print(f"   📝 عينة: {report_data[0][0]} - {report_data[0][2]:.2f} درهم")
                    else:
                        print("   ⚠️ لا توجد بيانات للتقرير")
                        
                except ImportError:
                    print("   ⚠️ لا يمكن استيراد وظيفة التقرير")
                except Exception as e:
                    print(f"   ❌ خطأ في اختبار التقرير: {e}")
            
            else:
                print("   ⚠️ لا توجد بيانات في monthly_duties")
        
        except Exception as e:
            print(f"   ❌ خطأ في الترحيل: {e}")
        
        conn.close()
        
        print(f"\n🎯 انتهى الاختبار!")
        print("💡 إذا نجح الاختبار، فالتقارير ستعتمد على البيانات المرحلة")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    simple_test()
