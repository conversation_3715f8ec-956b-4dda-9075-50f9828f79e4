#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_filter_data_sources():
    """اختبار مصادر بيانات التصفية المحدثة"""
    print("🔍 اختبار مصادر بيانات التصفية المحدثة")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول_المواد_والاقسام
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المواد_والاقسام'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول_المواد_والاقسام موجود")
            
            # جلب جميع السجلات
            cursor.execute("SELECT * FROM جدول_المواد_والاقسام")
            all_records = cursor.fetchall()
            print(f"📊 إجمالي السجلات: {len(all_records)}")
            
            if all_records:
                print("\n📋 عينة من السجلات:")
                for i, record in enumerate(all_records[:3], 1):  # أول 3 سجلات
                    print(f"   {i}. {record}")
            
            # اختبار مجموعات التصفية
            print("\n🎯 اختبار مجموعات التصفية:")
            cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
            filter_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"   📋 المجموعات المتاحة ({len(filter_groups)}):")
            for i, group in enumerate(filter_groups, 1):
                print(f"      {i}. {group}")
            
            # اختبار أقسام التصفية
            print("\n🎯 اختبار أقسام التصفية:")
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
            filter_sections = [row[0] for row in cursor.fetchall()]
            
            print(f"   📋 الأقسام المتاحة ({len(filter_sections)}):")
            for i, section in enumerate(filter_sections, 1):
                print(f"      {i}. {section}")
            
            # اختبار العلاقة بين المجموعات والأقسام
            print("\n🔗 اختبار العلاقة بين المجموعات والأقسام:")
            for group in filter_groups:
                cursor.execute("""
                    SELECT DISTINCT القسم 
                    FROM جدول_المواد_والاقسام 
                    WHERE المجموعة = ? AND القسم IS NOT NULL 
                    ORDER BY القسم
                """, (group,))
                sections_for_group = [row[0] for row in cursor.fetchall()]
                print(f"   📂 {group}: {sections_for_group}")
                
        else:
            print("❌ جدول_المواد_والاقسام غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_bulk_edit_groups():
    """اختبار مجموعات التعديل الجماعي"""
    print("\n🔄 اختبار مجموعات التعديل الجماعي")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول المجموعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # محاكاة استعلام التعديل الجماعي
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            bulk_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"🎯 مجموعات التعديل الجماعي ({len(bulk_groups)}):")
            for i, group in enumerate(bulk_groups, 1):
                print(f"   {i}. {group}")
                
        else:
            print("❌ جدول المجموعات غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def simulate_filter_workflow():
    """محاكاة سير عمل التصفية"""
    print("\n🔄 محاكاة سير عمل التصفية")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # الخطوة 1: تحميل المجموعات للتصفية
        print("1️⃣ تحميل المجموعات للتصفية...")
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        print(f"   ✅ تم تحميل {len(groups)} مجموعة")
        
        # الخطوة 2: اختيار مجموعة وتحميل الأقسام المرتبطة
        if groups:
            selected_group = groups[0]  # اختيار أول مجموعة
            print(f"2️⃣ اختيار المجموعة: {selected_group}")
            
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_المواد_والاقسام 
                WHERE المجموعة = ? AND القسم IS NOT NULL 
                ORDER BY القسم
            """, (selected_group,))
            sections = [row[0] for row in cursor.fetchall()]
            print(f"   ✅ تم تحميل {len(sections)} قسم للمجموعة المحددة")
            print(f"   📋 الأقسام: {sections}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لمصادر البيانات المحدثة")
    print("=" * 60)
    
    test_filter_data_sources()
    test_bulk_edit_groups()
    simulate_filter_workflow()
