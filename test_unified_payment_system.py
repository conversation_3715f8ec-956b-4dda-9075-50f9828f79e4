#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def test_unified_payment_system():
    """اختبار النظام الموحد للدفع"""
    print("🧪 اختبار النظام الموحد للدفع الجماعي")
    print("=" * 60)
    
    # فحص قاعدة البيانات
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        print("🔍 فحص الجداول الموجودة...")
        
        # فحص جدول البيانات الرئيسي
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        student_count = cursor.fetchone()[0]
        print(f"📊 عدد التلاميذ في النظام: {student_count}")
        
        # فحص جدول monthly_duties الجديد
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_duties'")
        monthly_duties_exists = cursor.fetchone()
        
        if monthly_duties_exists:
            print("✅ جدول monthly_duties موجود")
            cursor.execute("SELECT COUNT(*) FROM monthly_duties")
            monthly_count = cursor.fetchone()[0]
            print(f"📊 عدد الواجبات الشهرية: {monthly_count}")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(monthly_duties)")
            columns = cursor.fetchall()
            print("📋 أعمدة جدول monthly_duties:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
        else:
            print("⚠️ جدول monthly_duties غير موجود - سيتم إنشاؤه عند أول استخدام")
        
        # فحص جدول جدول_الاداءات القديم
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_الاداءات'")
        old_table_exists = cursor.fetchone()
        
        if old_table_exists:
            print("✅ جدول جدول_الاداءات موجود (للتوافق مع النظام السابق)")
            cursor.execute("SELECT COUNT(*) FROM جدول_الاداءات")
            old_count = cursor.fetchone()[0]
            print(f"📊 عدد السجلات في الجدول القديم: {old_count}")
        else:
            print("⚠️ جدول جدول_الاداءات غير موجود - سيتم إنشاؤه عند أول استخدام")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_payment_calculation():
    """اختبار حساب المدفوعات"""
    print("\n💰 اختبار حساب المدفوعات:")
    print("-" * 40)
    
    # حالات اختبار مختلفة
    test_cases = [
        {"amount_required": 500, "amount_paid": 500, "expected_status": "مدفوع كاملاً"},
        {"amount_required": 500, "amount_paid": 300, "expected_status": "مدفوع جزئياً"},
        {"amount_required": 500, "amount_paid": 0, "expected_status": "غير مدفوع"},
        {"amount_required": 500, "amount_paid": 600, "expected_status": "مدفوع كاملاً"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        amount_required = case["amount_required"]
        amount_paid = case["amount_paid"]
        expected_status = case["expected_status"]
        
        # حساب المبلغ المتبقي
        amount_remaining = max(0, amount_required - amount_paid)
        
        # تحديد حالة الدفع
        if amount_paid == 0:
            status = "غير مدفوع"
        elif amount_paid >= amount_required:
            status = "مدفوع كاملاً"
            amount_remaining = 0
        else:
            status = "مدفوع جزئياً"
        
        # التحقق من النتيجة
        result = "✅" if status == expected_status else "❌"
        
        print(f"اختبار {i}: {result}")
        print(f"   المطلوب: {amount_required} درهم")
        print(f"   المدفوع: {amount_paid} درهم")
        print(f"   المتبقي: {amount_remaining} درهم")
        print(f"   الحالة: {status}")
        print(f"   المتوقع: {expected_status}")
        print()

def show_system_features():
    """عرض مميزات النظام الجديد"""
    print("\n🎯 مميزات النظام الموحد الجديد:")
    print("=" * 50)
    
    print("📊 1. نظام دفع موحد:")
    print("   ✓ حفظ في جدول monthly_duties (النظام الجديد)")
    print("   ✓ حفظ في جدول_الاداءات (للتوافق مع النظام السابق)")
    print("   ✓ حساب تلقائي للمبلغ المتبقي")
    print("   ✓ تحديد تلقائي لحالة الدفع")
    
    print("\n💾 2. هيكل البيانات المحسن:")
    print("   ✓ معرف التلميذ (student_id)")
    print("   ✓ الشهر والسنة منفصلين")
    print("   ✓ المبلغ المطلوب والمدفوع والمتبقي")
    print("   ✓ تاريخ الدفع وحالة الدفع")
    print("   ✓ الملاحظات وتاريخ الإنشاء")
    
    print("\n🔄 3. التوافق مع الأنظمة:")
    print("   ✓ يعمل مع نافذة الدفع الجماعي (sub252_window.py)")
    print("   ✓ يعمل مع نافذة الواجبات الفردية (monthly_duties_window.py)")
    print("   ✓ يحافظ على البيانات السابقة")
    
    print("\n📋 4. خطوات الاستخدام:")
    print("   1. تحديد التلاميذ من الجدول")
    print("   2. الضغط على 'أداء الواجبات الشهرية لمجموعة'")
    print("   3. إدخال المبلغ المدفوع والشهر")
    print("   4. تاريخ الدفع وحالة الدفع")
    print("   5. الملاحظات (اختياري)")
    print("   6. تسجيل الدفع")
    
    print("\n🎉 5. النتائج المتوقعة:")
    print("   ✓ حفظ دقيق في قاعدة البيانات")
    print("   ✓ حساب تلقائي للمبالغ")
    print("   ✓ تتبع حالة الدفع")
    print("   ✓ إمكانية إنشاء تقارير مفصلة")

def create_test_payment():
    """إنشاء دفعة تجريبية للاختبار"""
    print("\n🧪 إنشاء دفعة تجريبية:")
    print("-" * 30)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # إنشاء الجداول إذا لم تكن موجودة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monthly_duties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                month TEXT NOT NULL,
                year INTEGER NOT NULL,
                amount_required REAL NOT NULL DEFAULT 0,
                amount_paid REAL NOT NULL DEFAULT 0,
                amount_remaining REAL NOT NULL DEFAULT 0,
                payment_date TEXT,
                payment_status TEXT NOT NULL DEFAULT 'غير مدفوع',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        
        # الحصول على أول تلميذ للاختبار
        cursor.execute("SELECT id, اسم_التلميذ, الواجب_الشهري FROM جدول_البيانات LIMIT 1")
        student = cursor.fetchone()
        
        if student:
            student_id = student[0]
            student_name = student[1]
            monthly_duty = student[2] if student[2] else 500
            
            # إنشاء دفعة تجريبية
            amount_paid = 300
            amount_remaining = max(0, monthly_duty - amount_paid)
            status = "مدفوع جزئياً" if amount_paid < monthly_duty else "مدفوع كاملاً"
            
            cursor.execute("""
                INSERT INTO monthly_duties 
                (student_id, month, year, amount_required, amount_paid, amount_remaining,
                 payment_date, payment_status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                student_id,
                "يناير",
                datetime.now().year,
                monthly_duty,
                amount_paid,
                amount_remaining,
                datetime.now().strftime('%Y-%m-%d'),
                status,
                "دفعة تجريبية من النظام الموحد"
            ))
            
            conn.commit()
            print(f"✅ تم إنشاء دفعة تجريبية للتلميذ: {student_name}")
            print(f"   المبلغ المطلوب: {monthly_duty} درهم")
            print(f"   المبلغ المدفوع: {amount_paid} درهم")
            print(f"   المبلغ المتبقي: {amount_remaining} درهم")
            print(f"   حالة الدفع: {status}")
        else:
            print("❌ لا يوجد تلاميذ في قاعدة البيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الدفعة التجريبية: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 اختبار النظام الموحد للدفع الجماعي")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_ok = test_unified_payment_system()
    
    # اختبار حساب المدفوعات
    test_payment_calculation()
    
    # عرض مميزات النظام
    show_system_features()
    
    # إنشاء دفعة تجريبية
    if db_ok:
        create_test_payment()
    
    print("\n" + "=" * 60)
    print("📋 ملخص التحديثات:")
    print("✅ تم تطبيق نظام monthly_duties على الدفع الجماعي")
    print("✅ تم الحفاظ على التوافق مع النظام السابق")
    print("✅ تم تحسين حساب المبالغ وحالة الدفع")
    print("✅ تم توحيد نظام حفظ البيانات")
    print("=" * 60)

if __name__ == "__main__":
    main()
