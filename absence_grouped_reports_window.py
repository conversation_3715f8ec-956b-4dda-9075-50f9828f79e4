#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QFrame,
    QMessageBox, QProgressDialog, QWidget
)
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtCore import Qt

class AbsenceGroupedReportsWindow(QDialog):
    """نافذة عرض سجلات الغياب مجمعة مع أزرار الحذف والطباعة"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.setupUI()
        self.load_grouped_absence_records()
        
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سجلات الغياب المجمعة - إدارة وطباعة")
        self.setMinimumSize(1200, 700)
        self.setStyleSheet("background-color: #f0f8ff;")
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # العنوان الرئيسي
        title_label = QLabel("سجلات الغياب المجمعة - إدارة وطباعة التقارير")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #0066cc;
                padding: 15px;
                border: 2px solid #0066cc;
                border-radius: 10px;
                background-color: white;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # شريط الأدوات
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # زر تحديث البيانات
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.setFont(QFont("Calibri", 13, QFont.Bold))
        refresh_button.setFixedHeight(40)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_button.clicked.connect(self.load_grouped_absence_records)
        toolbar_layout.addWidget(refresh_button)
        
        # زر حذف جميع السجلات
        delete_all_button = QPushButton("حذف جميع السجلات")
        delete_all_button.setFont(QFont("Calibri", 13, QFont.Bold))
        delete_all_button.setFixedHeight(40)
        delete_all_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_all_button.clicked.connect(self.delete_all_records)
        toolbar_layout.addWidget(delete_all_button)
        
        toolbar_layout.addStretch()
        
        # زر إغلاق النافذة
        close_button = QPushButton("إغلاق")
        close_button.setFont(QFont("Calibri", 13, QFont.Bold))
        close_button.setFixedHeight(40)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.close)
        toolbar_layout.addWidget(close_button)
        
        layout.addWidget(toolbar_frame)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_groups_label = QLabel("إجمالي المجموعات: 0")
        self.total_groups_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_groups_label.setStyleSheet("color: #0066cc;")
        stats_layout.addWidget(self.total_groups_label)
        
        stats_layout.addStretch()
        
        self.total_records_label = QLabel("إجمالي السجلات: 0")
        self.total_records_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.total_records_label.setStyleSheet("color: #0066cc;")
        stats_layout.addWidget(self.total_records_label)
        
        layout.addWidget(stats_frame)
        
        # جدول عرض البيانات المجمعة
        self.table = QTableWidget()
        self.table.setFont(QFont("Calibri", 12))
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        # تنسيق الجدول
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e6f3ff;
            }
            QHeaderView::section {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
            }
        """)
        
        layout.addWidget(self.table)
        
    def load_grouped_absence_records(self):
        """تحميل سجلات الغياب مجمعة من قاعدة البيانات"""
        try:
            # إنشاء مؤشر تقدم
            progress = QProgressDialog("جاري تحميل البيانات المجمعة...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("تحميل البيانات")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setValue(0)
            progress.show()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            progress.setValue(20)
            
            # التحقق من وجود جدول سجل_الغياب
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='سجل_الغياب'")
            if not cursor.fetchone():
                progress.close()
                QMessageBox.information(self, "معلومات", "لا توجد سجلات غياب في قاعدة البيانات.")
                return
            
            progress.setValue(40)
            
            # استعلام لتجميع البيانات حسب المادة وتاريخ التسجيل
            query = """
                SELECT 
                    المادة,
                    DATE(تاريخ_التسجيل) as تاريخ_التسجيل,
                    COUNT(*) as عدد_الغائبين,
                    GROUP_CONCAT(DISTINCT id) as معرفات_السجلات,
                    MIN(تاريخ_التسجيل) as أول_تسجيل,
                    MAX(تاريخ_التسجيل) as آخر_تسجيل
                FROM سجل_الغياب 
                WHERE المادة IS NOT NULL AND المادة != ''
                GROUP BY المادة, DATE(تاريخ_التسجيل)
                ORDER BY DATE(تاريخ_التسجيل) DESC, المادة
            """
            
            cursor.execute(query)
            grouped_records = cursor.fetchall()
            
            progress.setValue(60)
            
            # حساب إجمالي السجلات
            cursor.execute("SELECT COUNT(*) FROM سجل_الغياب WHERE المادة IS NOT NULL AND المادة != ''")
            total_records = cursor.fetchone()[0]
            
            progress.setValue(80)
            
            # إعداد الجدول
            headers = [
                "المادة", 
                "تاريخ التسجيل", 
                "عدد الغائبين",
                "طباعة التقرير",
                "حذف المجموعة"
            ]
            
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.table.setRowCount(len(grouped_records))
            
            # ملء الجدول بالبيانات
            for row_idx, record in enumerate(grouped_records):
                subject = record[0] if record[0] else "غير محدد"
                registration_date = record[1] if record[1] else "غير محدد"
                absent_count = record[2]
                record_ids = record[3] if record[3] else ""
                
                # إضافة البيانات الأساسية
                items_data = [subject, registration_date, str(absent_count)]
                
                for col_idx, data in enumerate(items_data):
                    item = QTableWidgetItem(str(data))
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # تعيين لون خاص للخلايا حسب عدد الغائبين
                    if col_idx == 2:  # عمود عدد الغائبين
                        if absent_count >= 10:
                            item.setBackground(QColor("#ffebee"))  # أحمر فاتح
                        elif absent_count >= 5:
                            item.setBackground(QColor("#fff3e0"))  # برتقالي فاتح
                        else:
                            item.setBackground(QColor("#e8f5e8"))  # أخضر فاتح
                    
                    self.table.setItem(row_idx, col_idx, item)
                
                # إضافة زر طباعة التقرير
                print_widget = QPushButton("طباعة التقرير")
                print_widget.setFont(QFont("Calibri", 10, QFont.Bold))
                print_widget.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border-radius: 3px;
                        padding: 5px 10px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #0056b3;
                    }
                """)
                
                # ربط الزر بدالة طباعة التقرير مع تمرير بيانات المجموعة
                print_widget.clicked.connect(
                    lambda checked, s=subject, d=registration_date, ids=record_ids: 
                    self.print_group_report(s, d, ids)
                )
                
                self.table.setCellWidget(row_idx, 3, print_widget)
                
                # إضافة زر حذف المجموعة
                delete_widget = QPushButton("حذف المجموعة")
                delete_widget.setFont(QFont("Calibri", 10, QFont.Bold))
                delete_widget.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border-radius: 3px;
                        padding: 5px 10px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                
                # ربط الزر بدالة حذف المجموعة مع تمرير معرفات السجلات
                delete_widget.clicked.connect(
                    lambda checked, s=subject, d=registration_date, ids=record_ids: 
                    self.delete_group_records(s, d, ids)
                )
                
                self.table.setCellWidget(row_idx, 4, delete_widget)
            
            progress.setValue(100)
            
            # تعيين عرض الأعمدة
            column_widths = [200, 150, 100, 150, 150]
            for col_idx, width in enumerate(column_widths):
                if col_idx < self.table.columnCount():
                    self.table.setColumnWidth(col_idx, width)
            
            # تعيين ارتفاع الصفوف
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 50)
            
            # تحديث الإحصائيات
            self.total_groups_label.setText(f"إجمالي المجموعات: {len(grouped_records)}")
            self.total_records_label.setText(f"إجمالي السجلات: {total_records}")
            
            conn.close()
            progress.close()
            
        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل سجلات الغياب المجمعة: {str(e)}")
    
    def print_group_report(self, subject, date, record_ids):
        """طباعة تقرير لمجموعة محددة"""
        try:
            # جلب بيانات السجلات في المجموعة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحويل معرفات السجلات إلى قائمة
            if not record_ids:
                QMessageBox.warning(self, "تنبيه", "لا توجد سجلات لطباعة التقرير.")
                return
            
            id_list = record_ids.split(',')
            placeholders = ','.join(['?' for _ in id_list])
            
            query = f"""
                SELECT رقم_الامتحان, الاسم_الكامل, المادة, تاريخ_الغياب, 
                       الفترة, ملاحظات
                FROM سجل_الغياب 
                WHERE id IN ({placeholders})
                ORDER BY رقم_الامتحان
            """
            
            cursor.execute(query, id_list)
            records = cursor.fetchall()
            conn.close()
            
            if not records:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على سجلات للطباعة.")
                return
            
            # تحويل البيانات إلى التنسيق المطلوب للطباعة
            selected_records = []
            absence_data = {
                'subject': subject,
                'date': date,
                'period': records[0][4] if records else "غير محدد",
                'notes': f"تقرير مجمع للغياب - تاريخ التسجيل: {date}"
            }
            
            for record in records:
                selected_records.append({
                    'رقم_الامتحان': record[0] if record[0] else '',
                    'الاسم_الكامل': record[1] if record[1] else ''
                })
            
            # استدعاء دالة الطباعة من print133.py
            try:
                from print133 import print_absence_report_single
                
                success, output_path, message = print_absence_report_single(
                    parent=self,
                    selected_records=selected_records,
                    absence_data=absence_data
                )
                
                if success:
                    QMessageBox.information(
                        self,
                        "نجح العملية", 
                        f"تم إنشاء التقرير بنجاح.\n\n"
                        f"المادة: {subject}\n"
                        f"التاريخ: {date}\n"
                        f"عدد الغائبين: {len(selected_records)}\n\n"
                        f"مسار الملف: {output_path}"
                    )
                else:
                    QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {message}")
                    
            except ImportError:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على ملف print133.py المطلوب للطباعة.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في جلب بيانات المجموعة: {str(e)}")
    
    def delete_group_records(self, subject, date, record_ids):
        """حذف جميع سجلات مجموعة محددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف جميع سجلات الغياب للمجموعة التالية؟\n\n"
                f"المادة: {subject}\n"
                f"التاريخ: {date}\n\n"
                f"لا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            if not record_ids:
                QMessageBox.warning(self, "تنبيه", "لا توجد سجلات للحذف.")
                return
            
            # حذف السجلات من قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحويل معرفات السجلات إلى قائمة
            id_list = record_ids.split(',')
            placeholders = ','.join(['?' for _ in id_list])
            
            # حذف السجلات
            query = f"DELETE FROM سجل_الغياب WHERE id IN ({placeholders})"
            cursor.execute(query, id_list)
            
            # حفظ التغييرات
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_grouped_absence_records()
            
            QMessageBox.information(
                self, 
                "تم الحذف", 
                f"تم حذف {affected_rows} سجل من مجموعة:\n"
                f"المادة: {subject}\n"
                f"التاريخ: {date}"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف سجلات المجموعة: {str(e)}")
    
    def delete_all_records(self):
        """حذف جميع سجلات الغياب"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد حذف الجميع",
                "هل أنت متأكد من حذف جميع سجلات الغياب؟\n\n"
                "سيتم حذف جميع السجلات نهائياً ولا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # حذف جميع السجلات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM سجل_الغياب")
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_grouped_absence_records()
            
            QMessageBox.information(
                self, 
                "تم الحذف", 
                f"تم حذف جميع السجلات بنجاح.\n"
                f"عدد السجلات المحذوفة: {affected_rows}"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف السجلات: {str(e)}")

# دالة لفتح النافذة من ملفات أخرى
def open_absence_grouped_reports_window(parent=None, db_path="data.db"):
    """فتح نافذة عرض سجلات الغياب المجمعة مع إمكانية الحذف والطباعة"""
    try:
        grouped_window = AbsenceGroupedReportsWindow(parent=parent, db_path=db_path)
        grouped_window.exec_()
    except Exception as e:
        if parent:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة سجلات الغياب المجمعة: {str(e)}")

# تشغيل النافذة بشكل مستقل للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    window = AbsenceGroupedReportsWindow()
    window.show()
    sys.exit(app.exec_())
