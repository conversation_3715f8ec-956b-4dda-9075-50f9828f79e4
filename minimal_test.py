#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أبسط لمشكلة مؤشر الماوس الدوار
"""
import sys
import os

# طباعة مباشرة إلى ملف
def log_message(message):
    with open("debug.log", "a", encoding="utf-8") as f:
        f.write(f"{message}\n")
        f.flush()
    print(message)
    sys.stdout.flush()

try:
    log_message("🚀 بدء الاختبار...")
    
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QDateEdit, QLabel
    from PyQt5.QtCore import Qt, QDate
    from PyQt5.QtGui import QFont
    
    log_message("✅ تم استيراد PyQt5")
    
    class MinimalTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            log_message("🔧 إنشاء النافذة...")
            self.init_ui()
            log_message("✅ تم إنشاء النافذة")
            
        def init_ui(self):
            self.setWindowTitle("اختبار مؤشر الماوس")
            self.setGeometry(400, 400, 400, 200)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # عنوان
            title = QLabel("اختبار تغيير التاريخ")
            title.setFont(QFont("Arial", 16))
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # تاريخ
            self.date_edit = QDateEdit()
            self.date_edit.setDate(QDate.currentDate())
            self.date_edit.setFont(QFont("Arial", 12))
            
            # ربط تغيير التاريخ
            self.date_edit.dateChanged.connect(self.on_date_changed)
            
            layout.addWidget(self.date_edit)
            
            # تعليمات
            instructions = QLabel("غير التاريخ ولاحظ عدم ظهور مؤشر دوار")
            instructions.setFont(QFont("Arial", 10))
            layout.addWidget(instructions)
            
        def on_date_changed(self):
            """معالج تغيير التاريخ الآمن"""
            selected_date = self.date_edit.date().toString("yyyy-MM-dd")
            log_message(f"📅 تم تغيير التاريخ إلى: {selected_date}")
            
            # تنظيف أي مؤشرات
            self.clear_cursors()
            
            # معالجة فورية بدون مؤشر تحميل
            QApplication.processEvents()
            
            log_message(f"✅ تم معالجة التاريخ بأمان: {selected_date}")
            
        def clear_cursors(self):
            """تنظيف المؤشرات"""
            try:
                for _ in range(10):
                    try:
                        QApplication.restoreOverrideCursor()
                    except:
                        break
                log_message("🔄 تم تنظيف المؤشرات")
            except Exception as e:
                log_message(f"❌ خطأ في تنظيف المؤشرات: {e}")
    
    def main():
        log_message("🔧 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        log_message("✅ تم إنشاء التطبيق")
        
        window = MinimalTestWindow()
        log_message("✅ تم إنشاء النافذة")
        
        window.show()
        log_message("✅ تم عرض النافذة - جاهزة للاختبار")
        log_message("🎯 غير التاريخ الآن لاختبار الحل!")
        
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        main()
        
except Exception as e:
    log_message(f"❌ خطأ عام: {e}")
    import traceback
    log_message(f"تفاصيل الخطأ: {traceback.format_exc()}")
