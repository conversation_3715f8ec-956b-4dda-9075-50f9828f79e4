from PyQt5.QtWidgets import (
    QD<PERSON>og, QVBoxLayout, QHBox<PERSON>ayout, QLabel, QCheckBox, QPushButton,
    QDialogButtonBox, QSpinBox, QApplication, QMessageBox, QProgressDialog
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt
import sys
import sqlite3

class ExamNumbersDialog(QDialog):
    """نافذة إضافة أرقام الامتحان"""

    def __init__(self, parent=None, start_number=1, available_columns=None, ordered_levels=None, db_path="data.db"):
        super().__init__(parent)
        self.start_number = start_number
        self.available_columns = available_columns or []
        self.ordered_levels = ordered_levels or []
        self.selected_columns = []
        self.db_path = db_path
        self.parent_window = parent
        self.initUI()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختيار طريقة ترتيب المترشحين")
        self.setFixedSize(800, 600)
        self.setStyleSheet("background-color: #33CCCC;")

        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # تقليل المسافة بين العناصر

        # إضافة عنوان
        title_label = QLabel("اختر الأعمدة المطلوبة للترتيب وحدد ترتيبها:")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        title_label.setFixedHeight(35)
        layout.addWidget(title_label)

        # إضافة حقل رقم الامتحان ابتداء من
        start_number_layout = QHBoxLayout()
        start_number_layout.setSpacing(10)

        start_number_label = QLabel("رقم الامتحان ابتداء من:")
        start_number_label.setFont(QFont("Calibri", 14, QFont.Bold))
        start_number_label.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        start_number_label.setFixedHeight(35)
        start_number_layout.addWidget(start_number_label)

        self.start_number_spin = QSpinBox()
        self.start_number_spin.setMinimum(1)
        self.start_number_spin.setMaximum(9999)
        self.start_number_spin.setValue(self.start_number)
        self.start_number_spin.setFont(QFont("Calibri", 13, QFont.Bold))
        self.start_number_spin.setStyleSheet("background-color: white;")
        self.start_number_spin.setFixedHeight(35)
        self.start_number_spin.setMinimumWidth(100)
        start_number_layout.addWidget(self.start_number_spin)

        layout.addLayout(start_number_layout)

        # إنشاء قائمة للأعمدة المحددة
        selected_columns_layout = QVBoxLayout()
        selected_columns_layout.setSpacing(5)

        # إنشاء قائمة للأعمدة المتاحة
        available_columns_layout = QVBoxLayout()
        available_columns_layout.setSpacing(5)

        # إضافة عنوان للأعمدة المحددة
        selected_title = QLabel("الأعمدة المحددة:")
        selected_title.setFont(QFont("Calibri", 14, QFont.Bold))
        selected_title.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        selected_title.setFixedHeight(35)
        selected_columns_layout.addWidget(selected_title)

        # إضافة عنوان للأعمدة المتاحة
        available_title = QLabel("الأعمدة المتاحة:")
        available_title.setFont(QFont("Calibri", 14, QFont.Bold))
        available_title.setStyleSheet("color: blue; border: 1.5px solid darkblue; padding: 5px; background-color: white;")
        available_title.setFixedHeight(35)
        available_columns_layout.addWidget(available_title)

        # إنشاء قائمة للأعمدة المحددة
        self.selected_columns_list = []

        # إنشاء قائمة للأعمدة المتاحة
        self.available_columns_list = []

        # إضافة الأعمدة المتاحة
        for col in self.available_columns:
            checkbox = QCheckBox(col)
            checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
            checkbox.setStyleSheet("color: black; background-color: white;")
            checkbox.setFixedHeight(35)
            available_columns_layout.addWidget(checkbox)
            self.available_columns_list.append(checkbox)

        # إضافة زر لإضافة الأعمدة المحددة
        add_button = QPushButton("إضافة الأعمدة المحددة >>")
        add_button.setFont(QFont("Calibri", 13, QFont.Bold))
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        add_button.setFixedHeight(35)

        # إضافة زر لإعادة ترتيب الأعمدة المحددة
        move_up_button = QPushButton("نقل لأعلى")
        move_up_button.setFont(QFont("Calibri", 13, QFont.Bold))
        move_up_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        move_up_button.setFixedHeight(35)

        move_down_button = QPushButton("نقل لأسفل")
        move_down_button.setFont(QFont("Calibri", 13, QFont.Bold))
        move_down_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        move_down_button.setFixedHeight(35)

        # إضافة زر لحذف الأعمدة المحددة
        remove_button = QPushButton("<< حذف الأعمدة المحددة")
        remove_button.setFont(QFont("Calibri", 13, QFont.Bold))
        remove_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        remove_button.setFixedHeight(35)

        # إنشاء تخطيط أفقي للأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(remove_button)

        # إنشاء تخطيط أفقي للأعمدة
        columns_layout = QHBoxLayout()
        columns_layout.setSpacing(20)
        columns_layout.addLayout(available_columns_layout, 1)

        # إنشاء تخطيط عمودي للأعمدة المحددة وأزرار الترتيب
        selected_with_buttons_layout = QVBoxLayout()
        selected_with_buttons_layout.setSpacing(10)
        selected_with_buttons_layout.addLayout(selected_columns_layout)

        # إضافة أزرار الترتيب
        order_buttons_layout = QHBoxLayout()
        order_buttons_layout.setSpacing(10)
        order_buttons_layout.addWidget(move_up_button)
        order_buttons_layout.addWidget(move_down_button)
        selected_with_buttons_layout.addLayout(order_buttons_layout)

        columns_layout.addLayout(selected_with_buttons_layout, 1)

        layout.addLayout(columns_layout)
        layout.addLayout(buttons_layout)

        # إضافة أزرار موافق وإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        # تنسيق أزرار موافق وإلغاء
        ok_button = button_box.button(QDialogButtonBox.Ok)
        ok_button.setText("موافق")
        ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        ok_button.setFixedHeight(35)

        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        cancel_button.setText("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #33CCCC;
                color: black;
                border: 1.5px solid darkblue;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #00FFFF;
            }
        """)
        cancel_button.setFixedHeight(35)

        layout.addWidget(button_box)

        # تعريف الدوال للأزرار
        def add_selected_columns():
            for checkbox in self.available_columns_list:
                if checkbox.isChecked():
                    # إنشاء زر للعمود المحدد
                    column_button = QPushButton(checkbox.text())
                    column_button.setFont(QFont("Calibri", 13, QFont.Bold))
                    column_button.setStyleSheet("""
                        QPushButton {
                            background-color: white;
                            color: black;
                            border: 1.5px solid darkblue;
                            border-radius: 5px;
                            text-align: right;
                        }
                        QPushButton:checked {
                            background-color: #00FFFF;
                        }
                    """)
                    column_button.setCheckable(True)
                    column_button.setFixedHeight(35)
                    selected_columns_layout.addWidget(column_button)
                    self.selected_columns_list.append(column_button)
                    checkbox.setChecked(False)

        def remove_selected_columns():
            for button in self.selected_columns_list[:]:
                if button.isChecked():
                    selected_columns_layout.removeWidget(button)
                    self.selected_columns_list.remove(button)
                    button.deleteLater()

        def move_up():
            for i, button in enumerate(self.selected_columns_list):
                if button.isChecked() and i > 0:
                    # تبديل الأزرار
                    self.selected_columns_list[i], self.selected_columns_list[i-1] = self.selected_columns_list[i-1], self.selected_columns_list[i]

                    # إعادة ترتيب الأزرار في التخطيط
                    for j, btn in enumerate(self.selected_columns_list):
                        selected_columns_layout.removeWidget(btn)

                    for j, btn in enumerate(self.selected_columns_list):
                        selected_columns_layout.insertWidget(j + 1, btn)  # +1 للعنوان

                    break

        def move_down():
            for i, button in enumerate(self.selected_columns_list):
                if button.isChecked() and i < len(self.selected_columns_list) - 1:
                    # تبديل الأزرار
                    self.selected_columns_list[i], self.selected_columns_list[i+1] = self.selected_columns_list[i+1], self.selected_columns_list[i]

                    # إعادة ترتيب الأزرار في التخطيط
                    for j, btn in enumerate(self.selected_columns_list):
                        selected_columns_layout.removeWidget(btn)

                    for j, btn in enumerate(self.selected_columns_list):
                        selected_columns_layout.insertWidget(j + 1, btn)  # +1 للعنوان

                    break

        # ربط الدوال بالأزرار
        add_button.clicked.connect(add_selected_columns)
        remove_button.clicked.connect(remove_selected_columns)
        move_up_button.clicked.connect(move_up)
        move_down_button.clicked.connect(move_down)

    def get_selected_columns(self):
        """الحصول على الأعمدة المحددة"""
        return [button.text() for button in self.selected_columns_list]

    def get_start_number(self):
        """الحصول على رقم البداية"""
        return self.start_number_spin.value()

    def accept(self):
        """تنفيذ العملية عند الضغط على موافق"""
        selected_columns = self.get_selected_columns()
        start_number = self.get_start_number()
        
        if not selected_columns:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار عمود واحد على الأقل للترتيب.")
            return
            
        # تأكيد العملية
        columns_text = "\n".join([f"{i+1}. {col}" for i, col in enumerate(selected_columns)])
        levels_text = "\n".join([f"{i+1}. {level}" for i, level in enumerate(self.ordered_levels)])

        reply = QMessageBox.question(
            self,
            "تأكيد إضافة أرقام الامتحان",
            f"هل تريد توليد أرقام امتحان للمستويات التالية بدءًا من الرقم {start_number}؟\n\n"
            f"المستويات المحددة:\n{levels_text}\n\n"
            f"سيتم ترتيب المترشحين حسب الأعمدة التالية:\n{columns_text}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success, message = self.generate_exam_numbers(start_number, selected_columns)
            if success:
                QMessageBox.information(self, "نجح", f"تم إنشاء أرقام الامتحان بنجاح.\n{message}")
                # تحديث البيانات في النافذة الرئيسية
                if self.parent_window and hasattr(self.parent_window, 'load_data'):
                    self.parent_window.load_data()
                super().accept()
            else:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء أرقام الامتحان:\n{message}")
        
    def generate_exam_numbers(self, start_number, sort_columns):
        """إنشاء أرقام الامتحان وحفظها في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_number = start_number
            total_students = 0
            
            # إنشاء مؤشر تقدم
            progress = QProgressDialog("جاري إنشاء أرقام الامتحان...", "إلغاء", 0, len(self.ordered_levels), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # معالجة كل مستوى حسب الترتيب المحدد
            for level_index, level in enumerate(self.ordered_levels):
                if progress.wasCanceled():
                    break
                    
                progress.setValue(level_index)
                progress.setLabelText(f"جاري معالجة: {level}")
                
                # بناء استعلام ORDER BY
                order_clause = ", ".join([f"`{col}`" for col in sort_columns])
                
                # جلب الطلاب للمستوى الحالي مرتبين حسب الأعمدة المحددة
                query = f"""
                    SELECT id FROM امتحانات 
                    WHERE المستوى = ? 
                    ORDER BY {order_clause}
                """
                
                cursor.execute(query, (level,))
                student_ids = cursor.fetchall()
                
                # تحديث أرقام الامتحان
                for student_id_tuple in student_ids:
                    student_id = student_id_tuple[0]
                    cursor.execute(
                        "UPDATE امتحانات SET رقم_الامتحان = ? WHERE id = ?",
                        (current_number, student_id)
                    )
                    current_number += 1
                    total_students += 1
            
            # حفظ التغييرات
            conn.commit()
            conn.close()
            progress.close()
            
            return True, f"تم إنشاء أرقام الامتحان لـ {total_students} مترشح.\nآخر رقم مستخدم: {current_number - 1}"
            
        except Exception as e:
            if 'progress' in locals():
                progress.close()
            if 'conn' in locals():
                conn.close()
            return False, str(e)

# للاختبار المستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    
    # بيانات تجريبية للاختبار
    available_columns = [
        "القسم",
        "ر.ت",
        "الجنس",
        "الاسم_الكامل",
        "تاريخ_الازدياد",
        "الرمز",
        "المؤسسة_الأصلية"
    ]
    
    ordered_levels = ["المستوى الأول", "المستوى الثاني", "المستوى الثالث"]
    
    window = ExamNumbersDialog(
        start_number=1,
        available_columns=available_columns,
        ordered_levels=ordered_levels
    )
    
    window.show()
    sys.exit(app.exec_())
