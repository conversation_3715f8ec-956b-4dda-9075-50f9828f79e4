#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTableWidget, 
                             QTableWidgetItem, QVBoxLayout, QWidget, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleTableTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار عرض الجدول")
        self.setGeometry(100, 100, 1000, 600)
        
        # إنشاء الويدجيت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # إنشاء الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeader<PERSON>abels(["اسم التلميذ", "القسم", "المجموعة"])
        layout.addWidget(self.table)
        
        # زر تحميل البيانات
        load_btn = QPushButton("تحميل البيانات")
        load_btn.clicked.connect(self.load_data)
        layout.addWidget(load_btn)
        
        # تحميل البيانات تلقائياً
        self.load_data()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        print("🔧 بدء تحميل البيانات...")
        
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # استعلام بسيط
            query = """
                SELECT اسم_التلميذ, القسم, اسم_المجموعة
                FROM جدول_البيانات
                WHERE اسم_التلميذ IS NOT NULL
                LIMIT 10
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            print(f"📊 تم جلب {len(results)} سجل")
            
            # عرض البيانات في الجدول
            self.table.setRowCount(len(results))
            
            for row, data in enumerate(results):
                print(f"   📋 الصف {row}: {data}")
                
                # اسم التلميذ
                name_item = QTableWidgetItem(str(data[0] or ""))
                name_item.setFont(QFont("Arial", 12))
                self.table.setItem(row, 0, name_item)
                
                # القسم
                section_item = QTableWidgetItem(str(data[1] or ""))
                self.table.setItem(row, 1, section_item)
                
                # المجموعة
                group_item = QTableWidgetItem(str(data[2] or ""))
                self.table.setItem(row, 2, group_item)
                
                print(f"      ✅ تم إضافة الصف {row}")
            
            conn.close()
            print(f"✅ تم عرض {len(results)} سجل في الجدول")
            
            # فحص محتوى الجدول
            print(f"\n🔍 فحص محتوى الجدول:")
            for row in range(min(3, self.table.rowCount())):
                name = self.table.item(row, 0).text() if self.table.item(row, 0) else "فارغ"
                section = self.table.item(row, 1).text() if self.table.item(row, 1) else "فارغ"
                group = self.table.item(row, 2).text() if self.table.item(row, 2) else "فارغ"
                print(f"   الصف {row}: {name} | {section} | {group}")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    window = SimpleTableTest()
    window.show()
    
    # تشغيل لمدة 10 ثوان للاختبار
    import time
    start_time = time.time()
    while time.time() - start_time < 10:
        app.processEvents()
        time.sleep(0.1)
    
    window.close()
    print("تم إغلاق النافذة")

if __name__ == "__main__":
    main()
