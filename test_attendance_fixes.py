#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import QApplication, QMessageBox

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔧 اختبار الاتصال بقاعدة البيانات...")
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار جدول البيانات
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        total_students = cursor.fetchone()[0]
        print(f"✅ إجمالي التلاميذ: {total_students}")
        
        # اختبار الأقسام
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL")
        sections = cursor.fetchall()
        print(f"✅ الأقسام المتاحة: {[s[0] for s in sections]}")
        
        # اختبار عينة من التلاميذ
        cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL LIMIT 5")
        sample_students = cursor.fetchall()
        print("✅ عينة من التلاميذ:")
        for student in sample_students:
            print(f"   - {student[0]} | {student[1]} | {student[2]}")
        
        # اختبار جدول المواد والأقسام
        try:
            cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
            sections_count = cursor.fetchone()[0]
            print(f"✅ جدول المواد والأقسام: {sections_count} سجل")
        except:
            print("⚠️ جدول المواد والأقسام غير موجود")
        
        # اختبار جدول الغياب
        try:
            cursor.execute("SELECT COUNT(*) FROM absence_records")
            absence_count = cursor.fetchone()[0]
            print(f"✅ سجلات الغياب: {absence_count}")
        except:
            print("⚠️ جدول الغياب غير موجود - سيتم إنشاؤه تلقائياً")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_attendance_window():
    """اختبار نافذة معالجة الغياب"""
    try:
        print("\n🔧 اختبار نافذة معالجة الغياب...")
        from attendance_processing_window import AttendanceProcessingWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = AttendanceProcessingWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # اختبار تحميل الأقسام
        window.load_sections()
        sections_count = window.section_combo.count()
        print(f"✅ تم تحميل {sections_count} قسم")
        
        # اختبار تحميل التلاميذ
        window.load_students_data()
        students_count = window.students_table.rowCount()
        print(f"✅ تم تحميل {students_count} تلميذ في الجدول")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نافذة معالجة الغياب: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_sheet():
    """اختبار ورقة متابعة الغياب"""
    try:
        print("\n🔧 اختبار ورقة متابعة الغياب...")
        from monthly_attendance_sheet import MonthlyAttendanceSheet
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = MonthlyAttendanceSheet()
        print("✅ تم إنشاء نافذة الورقة بنجاح")
        
        # اختبار تحميل الأقسام
        window.load_sections()
        sections_count = window.section_combo.count()
        print(f"✅ تم تحميل {sections_count} قسم للورقة")
        
        # اختبار حساب الأسابيع
        weeks = window.get_weeks_in_month(2024, 1)  # يناير 2024
        print(f"✅ تم حساب {len(weeks)} أسبوع لشهر يناير 2024")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ورقة متابعة الغياب: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات النظام المحدث...")
    print("=" * 50)
    
    # اختبار قاعدة البيانات
    db_ok = test_database_connection()
    
    if db_ok:
        # اختبار النوافذ
        attendance_ok = test_attendance_window()
        sheet_ok = test_monthly_sheet()
        
        print("\n" + "=" * 50)
        print("📊 ملخص النتائج:")
        print(f"   قاعدة البيانات: {'✅ نجح' if db_ok else '❌ فشل'}")
        print(f"   نافذة معالجة الغياب: {'✅ نجح' if attendance_ok else '❌ فشل'}")
        print(f"   ورقة متابعة الغياب: {'✅ نجح' if sheet_ok else '❌ فشل'}")
        
        if db_ok and attendance_ok and sheet_ok:
            print("\n🎯 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        else:
            print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
    else:
        print("\n❌ فشل في الاتصال بقاعدة البيانات. تأكد من وجود ملف data.db")

if __name__ == "__main__":
    main()
