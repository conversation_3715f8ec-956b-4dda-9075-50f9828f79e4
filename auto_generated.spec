# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # الأيقونة الرئيسية
    ('01.ico', '.'),
    
    ('absence_grouped_reports_window.py', '.'),
    ('copy_filenames.py', '.'),
    ('help_guide.py', '.'),
    ('main_window.py', '.'),
    ('monthly_duties_window.py', '.'),
    ('print10.py', '.'),
    ('print101.py', '.'),
    ('print11.py', '.'),
    ('print12.py', '.'),
    ('print13.py', '.'),
    ('print133.py', '.'),
    ('print14.py', '.'),
    ('print15.py', '.'),
    ('print16.py', '.'),
    ('professional_exam_table.py', '.'),
    ('sub01_window.py', '.'),
    ('sub100_window.py', '.'),
    ('sub1_window.py', '.'),
    ('sub202_window.py', '.'),
    ('sub20_window.py', '.'),
    ('sub212_window.py', '.'),
    ('sub222_window.py', '.'),
    ('sub22_window.py', '.'),
    ('sub232_window.py', '.'),
    ('sub23_window.py', '.'),
    ('sub242_window.py', '.'),
    ('sub24_window.py', '.'),
    ('sub252_window.py', '.'),
    ('sub25_window.py', '.'),
    ('sub26_window.py', '.'),
    ('sub27_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub40_window.py', '.'),
    ('sub8_window.py', '.'),
    ('__pycache__/', '__pycache__/'),
    ('build/', 'build/'),
    ('dist/', 'dist/'),
    ('fonts/', 'fonts/'),
    ('01.ico', '.'),
    ('auto_generated.spec', '.'),
    ('data.db', '.'),
    ('main_window.spec', '.'),
]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'absence_grouped_reports_window',
        'help_guide',
        'main_window',
        'monthly_duties_window',
        'print10',
        'print101',
        'print11',
        'print12',
        'print13',
        'print133',
        'print14',
        'print15',
        'print16',
        'professional_exam_table',
        'sub01_window',
        'sub100_window',
        'sub1_window',
        'sub202_window',
        'sub20_window',
        'sub212_window',
        'sub222_window',
        'sub22_window',
        'sub232_window',
        'sub23_window',
        'sub242_window',
        'sub24_window',
        'sub252_window',
        'sub25_window',
        'sub26_window',
        'sub27_window',
        'sub2_window',
        'sub40_window',
        'sub8_window',
        
        # مكتبات PyQt5
        'PyQt5.QtWidgets',
        'PyQt5.QtGui', 
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        
        # مكتبات أساسية
        'sqlite3',
        'pandas',
        'openpyxl',
        'fpdf',
        'arabic_reshaper',
        'bidi.algorithm',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='برنامج_إدارة_الامتحانات_كامل',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    name='برنامج_إدارة_الامتحانات_كامل'
)