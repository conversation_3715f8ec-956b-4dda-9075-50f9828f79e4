# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # الأيقونة الرئيسية
    ('01.ico', '.'),
    
    ('absence_reports_generator.py', '.'),
    ('archived_accounts_manager.py', '.'),
    ('archived_accounts_window.py', '.'),
    ('attendance_processing_window.py', '.'),
    ('attendance_sheet_report.py', '.'),
    ('check_actual_columns.py', '.'),
    ('check_database_columns.py', '.'),
    ('check_db.py', '.'),
    ('check_groups_table.py', '.'),
    ('check_student_table_structure.py', '.'),
    ('check_tables.py', '.'),
    ('copy_filenames.py', '.'),
    ('debug_absence_data.py', '.'),
    ('debug_group_payment.py', '.'),
    ('debug_test.py', '.'),
    ('default_settings_window.py', '.'),
    ('final_test_monthly_report.py', '.'),
    ('fix_absence_table.py', '.'),
    ('fix_absence_tables.py', '.'),
    ('fix_data_consistency.py', '.'),
    ('fix_database.py', '.'),
    ('help_guide.py', '.'),
    ('improved_attendance_report.py', '.'),
    ('main_window.py', '.'),
    ('monthly_attendance_sheet.py', '.'),
    ('monthly_duties_window.py', '.'),
    ('print10.py', '.'),
    ('print101.py', '.'),
    ('print11.py', '.'),
    ('print12.py', '.'),
    ('print13.py', '.'),
    ('print133.py', '.'),
    ('print14.py', '.'),
    ('print15.py', '.'),
    ('print16.py', '.'),
    ('print_section_monthly.py', '.'),
    ('print_section_yearly.py', '.'),
    ('professional_exam_table.py', '.'),
    ('quick_archive_tool.py', '.'),
    ('quick_diagnosis.py', '.'),
    ('run_attendance.py', '.'),
    ('simple_table_test.py', '.'),
    ('simple_test_archive.py', '.'),
    ('sub01_window.py', '.'),
    ('sub100_window.py', '.'),
    ('sub1_window.py', '.'),
    ('sub202_window.py', '.'),
    ('sub20_window.py', '.'),
    ('sub212_window.py', '.'),
    ('sub222_window.py', '.'),
    ('sub22_window.py', '.'),
    ('sub232_window.py', '.'),
    ('sub23_window.py', '.'),
    ('sub242_window.py', '.'),
    ('sub24_window.py', '.'),
    ('sub252_window.py', '.'),
    ('sub252_window_backup.py', '.'),
    ('sub25_window.py', '.'),
    ('sub262_window.py', '.'),
    ('sub26_window.py', '.'),
    ('sub27_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub40_window.py', '.'),
    ('sub8_window.py', '.'),
    ('sub9_window.py', '.'),
    ('test_absence_fixed.py', '.'),
    ('test_absence_window.py', '.'),
    ('test_archived_accounts_system.py', '.'),
    ('test_attendance_fixes.py', '.'),
    ('test_bulk_edit.py', '.'),
    ('test_bulk_edit_features.py', '.'),
    ('test_bulk_edit_groups.py', '.'),
    ('test_bulk_edit_improvements.py', '.'),
    ('test_calibri_fonts.py', '.'),
    ('test_current_filter.py', '.'),
    ('test_detailed_report.py', '.'),
    ('test_filter_groups_updated.py', '.'),
    ('test_filter_sections_updated.py', '.'),
    ('test_filter_workflow.py', '.'),
    ('test_filtering_fixed.py', '.'),
    ('test_final_fix.py', '.'),
    ('test_final_groups.py', '.'),
    ('test_final_report.py', '.'),
    ('test_final_update.py', '.'),
    ('test_fix_current_year.py', '.'),
    ('test_fixed_absence.py', '.'),
    ('test_fixed_archived_window.py', '.'),
    ('test_fixed_attendance.py', '.'),
    ('test_functionality.py', '.'),
    ('test_groups_final.py', '.'),
    ('test_groups_table_correct.py', '.'),
    ('test_improved_archived_window.py', '.'),
    ('test_improved_report.py', '.'),
    ('test_improved_system.py', '.'),
    ('test_main_window.py', '.'),
    ('test_month_field.py', '.'),
    ('test_monthly_duties_teacher_update.py', '.'),
    ('test_no_main_layout.py', '.'),
    ('test_registration_update.py', '.'),
    ('test_report_with_archive.py', '.'),
    ('test_section_filtering_comparison.py', '.'),
    ('test_section_monthly_report.py', '.'),
    ('test_section_only_filter.py', '.'),
    ('test_student_code.py', '.'),
    ('test_sub262.py', '.'),
    ('test_table1_archived_data.py', '.'),
    ('test_unified_payment_system.py', '.'),
    ('test_updated_buttons.py', '.'),
    ('test_updated_report.py', '.'),
    ('test_yearly_report_archived.py', '.'),
    ('update_groups_table.py', '.'),
    ('__pycache__/', '__pycache__/'),
    ('build/', 'build/'),
    ('fonts/', 'fonts/'),
    ('logs/', 'logs/'),
    ('مجلد جديد/', 'مجلد جديد/'),
    ('01.ico', '.'),
    ('auto_generated.spec', '.'),
    ('data.db', '.'),
    ('main_window.spec', '.'),
]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'absence_reports_generator',
        'archived_accounts_manager',
        'archived_accounts_window',
        'attendance_processing_window',
        'attendance_sheet_report',
        'check_actual_columns',
        'check_database_columns',
        'check_db',
        'check_groups_table',
        'check_student_table_structure',
        'check_tables',
        'debug_absence_data',
        'debug_group_payment',
        'debug_test',
        'default_settings_window',
        'final_test_monthly_report',
        'fix_absence_table',
        'fix_absence_tables',
        'fix_database',
        'fix_data_consistency',
        'help_guide',
        'improved_attendance_report',
        'main_window',
        'monthly_attendance_sheet',
        'monthly_duties_window',
        'print10',
        'print101',
        'print11',
        'print12',
        'print13',
        'print133',
        'print14',
        'print15',
        'print16',
        'print_section_monthly',
        'print_section_yearly',
        'professional_exam_table',
        'quick_archive_tool',
        'quick_diagnosis',
        'run_attendance',
        'simple_table_test',
        'simple_test_archive',
        'sub01_window',
        'sub100_window',
        'sub1_window',
        'sub202_window',
        'sub20_window',
        'sub212_window',
        'sub222_window',
        'sub22_window',
        'sub232_window',
        'sub23_window',
        'sub242_window',
        'sub24_window',
        'sub252_window',
        'sub252_window_backup',
        'sub25_window',
        'sub262_window',
        'sub26_window',
        'sub27_window',
        'sub2_window',
        'sub40_window',
        'sub8_window',
        'sub9_window',
        'test_absence_fixed',
        'test_absence_window',
        'test_archived_accounts_system',
        'test_attendance_fixes',
        'test_bulk_edit',
        'test_bulk_edit_features',
        'test_bulk_edit_groups',
        'test_bulk_edit_improvements',
        'test_calibri_fonts',
        'test_current_filter',
        'test_detailed_report',
        'test_filtering_fixed',
        'test_filter_groups_updated',
        'test_filter_sections_updated',
        'test_filter_workflow',
        'test_final_fix',
        'test_final_groups',
        'test_final_report',
        'test_final_update',
        'test_fixed_absence',
        'test_fixed_archived_window',
        'test_fixed_attendance',
        'test_fix_current_year',
        'test_functionality',
        'test_groups_final',
        'test_groups_table_correct',
        'test_improved_archived_window',
        'test_improved_report',
        'test_improved_system',
        'test_main_window',
        'test_monthly_duties_teacher_update',
        'test_month_field',
        'test_no_main_layout',
        'test_registration_update',
        'test_report_with_archive',
        'test_section_filtering_comparison',
        'test_section_monthly_report',
        'test_section_only_filter',
        'test_student_code',
        'test_sub262',
        'test_table1_archived_data',
        'test_unified_payment_system',
        'test_updated_buttons',
        'test_updated_report',
        'test_yearly_report_archived',
        'update_groups_table',
        
        # مكتبات PyQt5
        'PyQt5.QtWidgets',
        'PyQt5.QtGui', 
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        
        # مكتبات أساسية
        'sqlite3',
        'pandas',
        'openpyxl',
        'fpdf',
        'arabic_reshaper',
        'bidi.algorithm',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='برنامج_إدارة_الامتحانات_كامل',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    name='برنامج_إدارة_الامتحانات_كامل'
)