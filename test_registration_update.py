#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# إضافة المسار الحالي للـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_registration_functionality():
    """اختبار وظيفة التسجيل الجديدة مع الحالات الثلاث"""
    print("🚀 اختبار وظيفة التسجيل المحدثة...")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # استيراد النافذة الرئيسية
        from sub252_window_backup import DataViewWindow
        
        # إنشاء النافذة
        window = DataViewWindow(db_path="data.db")
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("\n🔍 اختبار الوظائف الجديدة:")
        
        # اختبار دالة الحصول على معلومات التلميذ
        print("\n1️⃣ اختبار دالة get_student_info:")
        try:
            student_info = window.get_student_info(1)  # افتراض وجود طالب بـ ID = 1
            if student_info:
                print(f"   ✅ معلومات الطالب: {student_info}")
            else:
                print("   ⚠️ لا يوجد طالب بـ ID = 1")
        except Exception as e:
            print(f"   ❌ خطأ في get_student_info: {str(e)}")
        
        # اختبار دالة إنشاء رمز الطالب
        print("\n2️⃣ اختبار دالة generate_next_student_code:")
        try:
            next_code = window.generate_next_student_code()
            print(f"   ✅ رمز الطالب التالي: {next_code}")
        except Exception as e:
            print(f"   ❌ خطأ في generate_next_student_code: {str(e)}")
        
        # اختبار الحصول على قائمة الطلاب المحددين
        print("\n3️⃣ اختبار دالة get_selected_student_id:")
        try:
            selected_ids = window.get_selected_student_id()
            print(f"   ✅ الطلاب المحددون: {selected_ids}")
        except Exception as e:
            print(f"   ❌ خطأ في get_selected_student_id: {str(e)}")
        
        # عرض النافذة لفترة قصيرة للاختبار البصري
        window.show()
        
        print("\n" + "="*60)
        print("✅ اكتمل الاختبار الأساسي!")
        print("📝 تم إضافة الوظائف التالية:")
        print("   • حوار اختيار نوع العملية (تعديل أو إضافة قسم)")
        print("   • وظيفة إضافة قسم جديد للطالب الموجود")
        print("   • تعبئة تلقائية للمعلومات الأساسية")
        print("⏰ سيتم إغلاق النافذة خلال 3 ثوان...")
        
        def close_window():
            window.close()
            app.quit()
        
        QTimer.singleShot(3000, close_window)
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        app.quit()
        return False

if __name__ == "__main__":
    test_registration_functionality()
