#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QFrame, QMessageBox, QFormLayout, QDateEdit, QLineEdit
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt, QDate
from datetime import datetime

class AbsenceDialog(QDialog):
    """نافذة حوارية لتسجيل الغياب"""
    
    def __init__(self, parent=None, selected_count=0):
        super().__init__(parent)
        self.selected_count = selected_count
        self.setupUI()
        
    def setupUI(self):
        self.setWindowTitle("تسجيل الغياب")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        self.setStyleSheet("background-color: #f0f8ff;")
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(f"تسجيل غياب {self.selected_count} مترشح")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # إطار البيانات
        data_frame = QFrame()
        data_frame.setStyleSheet("background-color: white; border: 1px solid #d0d0d0; border-radius: 5px; padding: 15px;")
        data_layout = QFormLayout(data_frame)
        
        # تاريخ الغياب
        date_label = QLabel("تاريخ الغياب:")
        date_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 13))
        self.date_edit.setFixedHeight(40)
        self.date_edit.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        self.date_edit.setCalendarPopup(True)
        
        data_layout.addRow(date_label, self.date_edit)
        
        # الفترة
        period_label = QLabel("الفترة:")
        period_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.period_combo = QComboBox()
        self.period_combo.addItems(["الفترة الصباحية", "الفترة المسائية"])
        self.period_combo.setFont(QFont("Calibri", 13))
        self.period_combo.setFixedHeight(40)
        self.period_combo.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        
        data_layout.addRow(period_label, self.period_combo)
        
        # المادة
        subject_label = QLabel("المادة:")
        subject_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.subject_combo = QComboBox()
        subjects = [
            "اللغة العربية", "اللغة الأجنبية الأولى", "اللغة الأجنبية الثانية",
            "التربية الإسلامية", "التاريخ والجغرافيا", "الرياضيات",
            "العلوم الفيزيائية", "علوم الحياة والأرض", "الفلسفة"
        ]
        
        self.subject_combo.addItem("اختر المادة...")
        self.subject_combo.addItems(subjects)
        self.subject_combo.setFont(QFont("Calibri", 13))
        self.subject_combo.setFixedHeight(40)
        self.subject_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #d0d0d0; 
                padding: 5px; 
                border-radius: 3px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 1px solid #d0d0d0;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 8px solid #666;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                selection-background-color: #e6f3ff;
                border: 1px solid #d0d0d0;
            }
        """)
        
        data_layout.addRow(subject_label, self.subject_combo)
        
        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 13, QFont.Bold))
        
        self.notes_edit = QLineEdit()
        self.notes_edit.setFont(QFont("Calibri", 13))
        self.notes_edit.setFixedHeight(40)
        self.notes_edit.setStyleSheet("border: 1px solid #d0d0d0; padding: 5px; border-radius: 3px;")
        self.notes_edit.setPlaceholderText("ملاحظات إضافية (اختياري)...")
        
        data_layout.addRow(notes_label, self.notes_edit)
        
        layout.addWidget(data_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("تسجيل الغياب وإنشاء التقرير")
        self.ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.ok_button.setFixedHeight(45)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setFixedHeight(45)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.ok_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
    def get_absence_data(self):
        """الحصول على بيانات الغياب المدخلة"""
        return {
            'date': self.date_edit.date().toString('yyyy-MM-dd'),
            'period': self.period_combo.currentText(),
            'subject': self.subject_combo.currentText() if self.subject_combo.currentIndex() > 0 else '',
            'notes': self.notes_edit.text().strip()
        }

def save_absence_records(selected_records, absence_data):
    """دالة مستقلة لحفظ سجلات الغياب في قاعدة البيانات"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول سجل الغياب إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS سجل_الغياب (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                رقم_الامتحان TEXT,
                الاسم_الكامل TEXT,
                المادة TEXT,
                تاريخ_الغياب DATE,
                الفترة TEXT,
                ملاحظات TEXT,
                تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج سجلات الغياب
        for record in selected_records:
            cursor.execute('''
                INSERT INTO سجل_الغياب 
                (رقم_الامتحان, الاسم_الكامل, المادة, تاريخ_الغياب, الفترة, ملاحظات)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                record.get('رقم_الامتحان', ''),
                record.get('الاسم_الكامل', ''),
                absence_data['subject'],
                absence_data['date'],
                absence_data['period'],
                absence_data['notes']
            ))
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"خطأ في حفظ سجلات الغياب: {str(e)}")
        return False

def print_absence_report(parent=None, selected_records=None, absence_data=None):
    """دالة لإنشاء تقرير الغياب باستخدام print13.py"""
    try:
        # استيراد دالة طباعة تقرير الغياب من print13.py
        from print13 import print_absence_report as create_absence_report
        
        # استخدام الدالة من print13.py مباشرة
        return create_absence_report(parent, selected_records, absence_data)
        
    except ImportError:
        return False, None, "ملف print13.py غير موجود."
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

# إضافة دالة لاستخدام نافذة الغياب من أي مكان آخر
def show_absence_dialog(parent=None, selected_count=0, selected_records=None):
    """عرض نافذة الغياب وإنشاء التقرير"""
    dialog = AbsenceDialog(parent, selected_count)
    
    if dialog.exec_() == QDialog.Accepted:
        absence_data = dialog.get_absence_data()
        
        # التحقق من إدخال المادة
        if not absence_data['subject']:
            if parent:
                QMessageBox.warning(parent, "تنبيه", "الرجاء اختيار المادة.")
            return False, None, "لم يتم اختيار المادة."
        
        # حفظ سجلات الغياب في قاعدة البيانات أولاً
        if selected_records:
            # حفظ البيانات في جدول سجل_الغياب
            save_success = save_absence_records(selected_records, absence_data)
            
            if not save_success:
                if parent:
                    QMessageBox.warning(parent, "تنبيه", "فشل في حفظ سجلات الغياب في قاعدة البيانات.")
                return False, None, "فشل في حفظ سجلات الغياب."
            
            # إنشاء التقرير بعد حفظ البيانات
            success, output_path, message = print_absence_report(parent, selected_records, absence_data)
            
            if success:
                return success, output_path, message
            else:
                return False, None, f"تم حفظ الغياب في قاعدة البيانات ولكن فشل في إنشاء التقرير: {message}"
        else:
            return False, None, "لا توجد سجلات محددة."
    
    return False, None, "تم إلغاء العملية."

# تشغيل النافذة كتطبيق مستقل للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # اختبار نافذة الغياب
    sample_records = [
        {
            'رقم_الامتحان': '1001', 
            'الاسم_الكامل': 'أحمد محمد علي', 
            'القسم': '3ASCG-1', 
            'القاعة': '1',
            'الرمز': 'CD12345',
            'المؤسسة_الأصلية': 'ثانوية الرازي'
        },
        {
            'رقم_الامتحان': '1002', 
            'الاسم_الكامل': 'فاطمة أحمد محمد', 
            'القسم': '3ASCG-1', 
            'القاعة': '1',
            'الرمز': 'CD12346',
            'المؤسسة_الأصلية': 'ثانوية ابن سينا'
        },
        {
            'رقم_الامتحان': '1003', 
            'الاسم_الكامل': 'محمد علي حسن', 
            'القسم': '3ASCG-2', 
            'القاعة': '2',
            'الرمز': 'CD12347',
            'المؤسسة_الأصلية': 'ثانوية الخوارزمي'
        },
    ]
    
    success, output_path, message = show_absence_dialog(
        parent=None, 
        selected_count=len(sample_records),
        selected_records=sample_records
    )
    
    if success:
        print(f"✅ تم إنشاء تقرير الغياب بنجاح: {output_path}")
        print("تم حفظ التقرير في مجلد 'تقارير الامتحانات' على سطح المكتب")
    else:
        print(f"❌ فشل في إنشاء التقرير: {message}")
    
    sys.exit(app.exec_())
