#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def update_groups_table():
    """تحديث جدول المجموعات ليطابق البيانات الصحيحة"""
    print("🔄 تحديث جدول المجموعات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # حذف البيانات القديمة
        print("🗑️ حذف البيانات القديمة...")
        cursor.execute("DELETE FROM جدول_المجموعات")
        
        # إدراج البيانات الصحيحة (من الصورة الثانية)
        correct_groups = [
            ("مجموعة مغلقة", "مجموعة مغلقة"),
            ("مجموعة مفتوحة", "مجموعة مفتوحة"),
            ("مجموعة الدراسات", "مجموعة الدراسات"),
            ("مجموعة خاصة", "مجموعة خاصة")
        ]
        
        print("📝 إدراج البيانات الصحيحة...")
        for group_name, description in correct_groups:
            cursor.execute("""
                INSERT INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)
                VALUES (?, ?)
            """, (group_name, description))
            print(f"   ✅ تم إدراج: {group_name}")
        
        conn.commit()
        
        # التحقق من النتيجة
        print("\n🔍 التحقق من النتيجة...")
        cursor.execute("SELECT * FROM جدول_المجموعات ORDER BY اسم_المجموعة")
        updated_groups = cursor.fetchall()
        
        print(f"📊 إجمالي المجموعات: {len(updated_groups)}")
        for i, group in enumerate(updated_groups, 1):
            print(f"   {i}. {group[1]}")
        
        conn.close()
        print("\n✅ تم تحديث جدول المجموعات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول المجموعات: {e}")

def test_updated_groups():
    """اختبار المجموعات المحدثة"""
    print("\n🧪 اختبار المجموعات المحدثة")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # محاكاة وظيفة load_groups_to_combo
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        
        print("📋 المجموعات التي ستظهر في التعديل الجماعي:")
        print("   1. اختر المجموعة الجديدة")
        for i, group in enumerate(groups, 2):
            print(f"   {i}. {group}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    update_groups_table()
    test_updated_groups()
