#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont

def test_calibri_fonts():
    """اختبار تطبيق خطوط Calibri في النافذة"""
    print("🔤 اختبار تطبيق خطوط Calibri في نافذة إدارة الحسابات المرحلة")
    print("=" * 70)
    
    try:
        # فحص وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            print("💡 قم بتشغيل التطبيق الرئيسي أولاً لإنشاء قاعدة البيانات")
            return
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # اختبار استيراد النافذة
        print("\n🔧 اختبار استيراد النافذة:")
        try:
            from archived_accounts_window import ArchivedAccountsWindow
            print("   ✅ تم استيراد النافذة بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
            return
        
        # إنشاء التطبيق
        print("\n🖥️ إنشاء التطبيق:")
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        print("   ✅ تم إنشاء التطبيق")
        
        # فحص توفر خط Calibri
        print("\n🔤 فحص توفر خط Calibri:")
        calibri_font = QFont("Calibri")
        if calibri_font.family() == "Calibri":
            print("   ✅ خط Calibri متوفر في النظام")
        else:
            print("   ⚠️ خط Calibri غير متوفر، سيتم استخدام خط بديل")
            print(f"   📝 الخط البديل: {calibri_font.family()}")
        
        # إنشاء النافذة
        print("\n🏗️ إنشاء النافذة:")
        try:
            window = ArchivedAccountsWindow()
            print("   ✅ تم إنشاء النافذة بنجاح")
            
            # فحص خطوط العناصر المختلفة
            print("\n🔍 فحص خطوط العناصر:")
            
            # فحص العنوان الرئيسي
            title_widgets = window.findChildren(type(window), "title_label")
            if hasattr(window, 'centralWidget'):
                central = window.centralWidget()
                if central:
                    title_labels = central.findChildren(type(central.findChild(type(central))))
                    # البحث عن العنوان الرئيسي
                    for child in central.children():
                        if hasattr(child, 'text') and "إدارة الحسابات المرحلة" in str(child.text()):
                            font = child.font()
                            print(f"   📋 العنوان الرئيسي: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                            break
            
            # فحص تسميات التاريخ
            if hasattr(window, 'month_combo') and hasattr(window, 'year_spin'):
                # البحث عن تسميات الشهر والسنة
                month_labels = window.findChildren(type(window.month_combo.parent()))
                for widget in window.findChildren(type(window.month_combo.parent())):
                    if hasattr(widget, 'children'):
                        for child in widget.children():
                            if hasattr(child, 'text') and hasattr(child, 'font'):
                                text = str(child.text())
                                if "الشهر:" in text or "السنة:" in text:
                                    font = child.font()
                                    print(f"   📅 تسمية {text}: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
            
            # فحص مربع البحث
            if hasattr(window, 'search_box'):
                search_parent = window.search_box.parent()
                if search_parent:
                    for child in search_parent.children():
                        if hasattr(child, 'text') and hasattr(child, 'font'):
                            text = str(child.text())
                            if "بحث:" in text:
                                font = child.font()
                                print(f"   🔍 تسمية البحث: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                                break
            
            # فحص عناصر الجدول
            if hasattr(window, 'table'):
                print(f"   📊 الجدول موجود - عدد الأعمدة: {window.table.columnCount()}")
                
                # محاولة إضافة عنصر تجريبي لفحص الخط
                try:
                    from PyQt5.QtWidgets import QTableWidgetItem
                    from PyQt5.QtCore import Qt
                    
                    # إضافة صف تجريبي
                    window.table.setRowCount(1)
                    test_item = QTableWidgetItem("اختبار")
                    test_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    window.table.setItem(0, 0, test_item)
                    
                    # فحص الخط
                    font = test_item.font()
                    print(f"   📋 عناصر الجدول: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                    
                    # إزالة الصف التجريبي
                    window.table.setRowCount(0)
                    
                except Exception as e:
                    print(f"   ⚠️ لا يمكن فحص خط الجدول: {e}")
            
            # فحص لوحة الإحصائيات
            stats_labels = [
                'total_archived_label', 'total_months_label', 
                'total_amount_label', 'db_size_label'
            ]
            stats_fonts_checked = 0
            for label_name in stats_labels:
                if hasattr(window, label_name):
                    label = getattr(window, label_name)
                    font = label.font()
                    print(f"   📊 {label_name}: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                    stats_fonts_checked += 1
            
            print(f"   📈 تم فحص {stats_fonts_checked}/{len(stats_labels)} تسمية إحصائية")
            
            # فحص شريط المعلومات
            info_labels = ['db_info', 'last_archive_info']
            info_fonts_checked = 0
            for label_name in info_labels:
                if hasattr(window, label_name):
                    label = getattr(window, label_name)
                    font = label.font()
                    print(f"   ℹ️ {label_name}: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                    info_fonts_checked += 1
            
            print(f"   📋 تم فحص {info_fonts_checked}/{len(info_labels)} تسمية معلوماتية")
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء النافذة: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # اختبار تحميل البيانات
        print("\n📊 اختبار تحميل البيانات:")
        try:
            window.load_archived_months()
            row_count = window.table.rowCount()
            print(f"   ✅ تم تحميل البيانات - عدد الصفوف: {row_count}")
            
            # فحص خطوط البيانات الفعلية
            if row_count > 0:
                first_item = window.table.item(0, 0)
                if first_item:
                    font = first_item.font()
                    print(f"   📝 خط البيانات الفعلية: {font.family()}, {font.pointSize()}pt, Bold: {font.bold()}")
                
        except Exception as e:
            print(f"   ❌ خطأ في تحميل البيانات: {e}")
        
        # عرض النافذة للاختبار البصري
        print(f"\n🖼️ عرض النافذة للاختبار البصري:")
        print("   💡 ستظهر النافذة لمدة 10 ثوان للمراجعة البصرية")
        print("   🎯 تحقق من:")
        print("      - وضوح الخطوط وقابليتها للقراءة")
        print("      - تناسق أحجام الخطوط")
        print("      - جودة عرض النصوص العربية")
        
        window.show()
        
        # تشغيل حلقة الأحداث
        import time
        start_time = time.time()
        while time.time() - start_time < 10:  # 10 ثوان
            app.processEvents()
            time.sleep(0.1)
        
        window.close()
        
        print("   ✅ تم إغلاق النافذة")
        
        # النتيجة النهائية
        print(f"\n🎯 النتيجة النهائية:")
        print("=" * 50)
        print("✅ تم تطبيق خط Calibri بنجاح")
        print("✅ الأحجام المطبقة:")
        print("   📋 العناوين الرئيسية: Calibri 15pt Bold")
        print("   📝 العناوين الفرعية: Calibri 14pt Bold") 
        print("   📊 التفاصيل: Calibri 13pt Bold")
        
        print(f"\n💡 مواصفات الخطوط:")
        print("   🎨 خط موحد: Calibri في جميع العناصر")
        print("   📏 أحجام متدرجة: 15pt → 14pt → 13pt")
        print("   💪 وزن ثابت: Bold لجميع النصوص")
        print("   🌍 دعم كامل للنصوص العربية")
        
        print(f"\n🚀 للاستخدام:")
        print("   python archived_accounts_window.py")
        print("   أو من النافذة الرئيسية: زر 'إدارة الحسابات المرحلة'")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_calibri_fonts()
