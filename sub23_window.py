#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import pandas as pd
import re
import difflib
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFileDialog, QInputDialog, QMessageBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QComboBox, QFrame, QScrollArea, QSizePolicy, QCheckBox,
    QListWidget, QListWidgetItem, QAbstractItemView, QDialog, QGroupBox
)
from PyQt5.QtCore import Qt, QMimeData, QPoint
from PyQt5.QtGui import QDrag, QFont, QIcon, QColor, QPixmap

class DraggableLabel(QLabel):
    """عنصر قابل للسحب يمثل عمود من أعمدة Excel"""

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #e6f2ff;
                border: 1px solid #99ccff;
                border-radius: 5px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
            }
        """)
        self.setAlignment(Qt.AlignCenter)
        self.setFixedHeight(35)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()

    def mouseMoveEvent(self, event):
        if not (event.buttons() & Qt.LeftButton):
            return

        if (event.pos() - self.drag_start_position).manhattanLength() < QApplication.startDragDistance():
            return

        drag = QDrag(self)
        mime_data = QMimeData()
        mime_data.setText(self.text())
        drag.setMimeData(mime_data)

        # إنشاء صورة للعنصر المسحوب
        pixmap = QPixmap(self.size())
        self.render(pixmap)
        drag.setPixmap(pixmap)
        drag.setHotSpot(event.pos())

        drag.exec_(Qt.CopyAction)

class DropTargetWidget(QLabel):
    """منطقة إفلات لعمود من قاعدة البيانات"""

    def __init__(self, db_column, parent=None):
        super().__init__(parent)
        self.db_column = db_column
        self.excel_column = None
        self.setAcceptDrops(True)
        self.setAlignment(Qt.AlignCenter)
        self.setFixedHeight(35)
        self.setMinimumWidth(200)
        self.update_display()

    def update_display(self):
        if self.excel_column:
            self.setText(f"{self.db_column} ← {self.excel_column}")
            self.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    background-color: #e6ffe6;
                    border: 1px solid #99cc99;
                    border-radius: 5px;
                    font-family: Calibri;
                    font-size: 13pt;
                    font-weight: bold;
                }
            """)
        else:
            self.setText(self.db_column)
            self.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    background-color: #f2f2f2;
                    border: 1px dashed #cccccc;
                    border-radius: 5px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
            """)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
            self.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    background-color: #ffffcc;
                    border: 2px dashed #ffcc00;
                    border-radius: 5px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
            """)

    def dragLeaveEvent(self, event):
        self.update_display()

    def dropEvent(self, event):
        if event.mimeData().hasText():
            self.excel_column = event.mimeData().text()
            self.update_display()
            event.acceptProposedAction()

class ExcelImportWindow(QWidget):
    """نافذة استيراد البيانات من ملف Excel إلى جدول البيانات"""

    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db_connection = db
        self.excel_file_path = None
        self.df = None
        self.selected_columns = []
        self.column_mapping = {}

        # تعيين خصائص النافذة
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        self.setAttribute(Qt.WA_DeleteOnClose)

        # تعيين اتجاه التخطيط من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # تعيين نمط الخلفية
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
            QLabel, QPushButton, QComboBox, QCheckBox, QTableWidget {
                font-family: Calibri;
            }
        """)

        self.init_ui()

    def init_ui(self):
        # إعداد النافذة
        self.setWindowTitle("استيراد بيانات من Excel إلى جدول البيانات")
        self.setGeometry(300, 30, 800, 700)
        self.setMinimumSize(800, 650)
        self.setWindowIcon(QIcon("01.ico"))

        # تعيين نمط الخط
        font_title = QFont("Calibri", 14)
        font_title.setBold(True)
        font_details = QFont("Calibri", 13)
        font_details.setBold(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)

        # إنشاء إطار للعنوان
        title_frame = QFrame()
        title_frame.setFrameShape(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #0066cc;
                border-radius: 10px;
                background-color: #e6f2ff;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)

        # إضافة عنوان
        title_label = QLabel("استيراد بيانات من Excel إلى جدول البيانات")
        title_label.setFont(font_title)
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        main_layout.addWidget(title_frame)

        # إنشاء أزرار الخطوات
        steps_layout = QHBoxLayout()

        self.btn_select_file = QPushButton("1. اختيار ملف Excel")
        self.btn_select_file.setFont(font_details)
        self.btn_select_file.setMinimumHeight(40)
        self.btn_select_file.setStyleSheet("""
            QPushButton {
                background-color: #0066cc;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0055aa;
            }
        """)
        self.btn_select_file.clicked.connect(self.select_excel_file)
        steps_layout.addWidget(self.btn_select_file)

        self.btn_map_columns = QPushButton("2. ربط الأعمدة")
        self.btn_map_columns.setFont(font_details)
        self.btn_map_columns.setMinimumHeight(40)
        self.btn_map_columns.setEnabled(False)
        self.btn_map_columns.setStyleSheet("""
            QPushButton {
                background-color: #999999;
                color: white;
                border-radius: 5px;
            }
            QPushButton:enabled {
                background-color: #0066cc;
            }
            QPushButton:enabled:hover {
                background-color: #0055aa;
            }
        """)
        self.btn_map_columns.clicked.connect(self.show_mapping_interface)
        steps_layout.addWidget(self.btn_map_columns)

        self.btn_import_data = QPushButton("3. استيراد البيانات")
        self.btn_import_data.setFont(font_details)
        self.btn_import_data.setMinimumHeight(40)
        self.btn_import_data.setEnabled(False)
        self.btn_import_data.setStyleSheet("""
            QPushButton {
                background-color: #999999;
                color: white;
                border-radius: 5px;
            }
            QPushButton:enabled {
                background-color: #009900;
            }
            QPushButton:enabled:hover {
                background-color: #008800;
            }
        """)
        self.btn_import_data.clicked.connect(self.import_data)
        steps_layout.addWidget(self.btn_import_data)

        # إضافة زر لتنظيف البيانات
        self.btn_clean_data = QPushButton("تنظيف البيانات")
        self.btn_clean_data.setFont(font_details)
        self.btn_clean_data.setMinimumHeight(40)
        self.btn_clean_data.setStyleSheet("""
            QPushButton {
                background-color: #ff9900;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #ff8800;
            }
        """)
        self.btn_clean_data.clicked.connect(self.clean_data)
        steps_layout.addWidget(self.btn_clean_data)

        main_layout.addLayout(steps_layout)

        # منطقة عرض المعلومات
        self.info_area = QLabel("الرجاء اختيار ملف Excel للبدء...")
        self.info_area.setFont(font_details)
        self.info_area.setAlignment(Qt.AlignCenter)
        self.info_area.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #dddddd;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        self.info_area.setMinimumHeight(100)
        main_layout.addWidget(self.info_area)

        # منطقة عرض الأعمدة والربط
        self.mapping_area = QFrame()
        self.mapping_area.setVisible(False)
        mapping_layout = QVBoxLayout(self.mapping_area)

        # عنوان منطقة الربط
        mapping_title = QLabel("اسحب أعمدة Excel وأفلتها على أعمدة قاعدة البيانات المناسبة:")
        mapping_title.setFont(font_details)
        mapping_title.setAlignment(Qt.AlignCenter)
        mapping_layout.addWidget(mapping_title)

        # تخطيط أفقي للجداول المتقابلة
        columns_layout = QHBoxLayout()

        # منطقة أعمدة Excel
        excel_group = QGroupBox("أعمدة ملف Excel")
        excel_group.setFont(font_details)
        excel_layout = QVBoxLayout(excel_group)
        excel_layout.setSpacing(2)  # تقليل المسافة بين العناصر

        self.excel_columns_area = QScrollArea()
        self.excel_columns_area.setWidgetResizable(True)
        self.excel_columns_widget = QWidget()
        self.excel_columns_layout = QVBoxLayout(self.excel_columns_widget)
        self.excel_columns_layout.setSpacing(2)  # تقليل المسافة بين العناصر
        self.excel_columns_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش
        self.excel_columns_area.setWidget(self.excel_columns_widget)
        excel_layout.addWidget(self.excel_columns_area)

        # منطقة أعمدة قاعدة البيانات
        db_group = QGroupBox("أعمدة جدول البيانات")
        db_group.setFont(font_details)
        db_layout = QVBoxLayout(db_group)
        db_layout.setSpacing(2)  # تقليل المسافة بين العناصر

        self.db_columns_area = QScrollArea()
        self.db_columns_area.setWidgetResizable(True)
        self.db_columns_widget = QWidget()
        self.db_columns_layout = QVBoxLayout(self.db_columns_widget)
        self.db_columns_layout.setSpacing(2)  # تقليل المسافة بين العناصر
        self.db_columns_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش
        self.db_columns_area.setWidget(self.db_columns_widget)
        db_layout.addWidget(self.db_columns_area)

        # إضافة المناطق إلى التخطيط الأفقي
        columns_layout.addWidget(excel_group)
        columns_layout.addWidget(db_group)

        # إضافة التخطيط الأفقي إلى منطقة الربط
        mapping_layout.addLayout(columns_layout)

        main_layout.addWidget(self.mapping_area)

    def detect_header_row(self, file_path):
        """الكشف التلقائي عن صف رؤوس الأعمدة في ملف Excel"""
        try:
            # قراءة أول 10 صفوف من الملف
            sample_df = pd.read_excel(file_path, nrows=10, header=None)

            # البحث عن الصف الذي يحتوي على أكبر عدد من القيم النصية
            # (غالبًا ما تكون رؤوس الأعمدة نصية)
            text_counts = []
            for i in range(min(10, len(sample_df))):
                row = sample_df.iloc[i]
                text_count = sum(1 for val in row if isinstance(val, str))
                text_counts.append(text_count)

            # إذا كان هناك صف يحتوي على عدد كبير من القيم النصية، فمن المحتمل أن يكون صف الرؤوس
            if text_counts:
                max_text_row = text_counts.index(max(text_counts))

                # التحقق من أن الصف يحتوي على قيم فريدة (غالبًا ما تكون رؤوس الأعمدة فريدة)
                unique_values = len(set(sample_df.iloc[max_text_row].dropna()))
                total_values = len(sample_df.iloc[max_text_row].dropna())

                if unique_values / total_values > 0.8:  # إذا كان أكثر من 80% من القيم فريدة
                    return max_text_row

            # إذا لم يتم العثور على صف مناسب، استخدم الصف الأول كافتراضي
            return 0
        except Exception:
            # في حالة حدوث أي خطأ، استخدم الصف الأول كافتراضي
            return 0

    def select_excel_file(self):
        """اختيار ملف Excel وقراءة بياناته"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف Excel", "", "Excel Files (*.xlsx *.xls)"
        )

        if not file_path:
            return

        try:
            # الكشف التلقائي عن صف رؤوس الأعمدة
            header_row = self.detect_header_row(file_path)

            # عرض رسالة تأكيد مخصصة مع إمكانية تغيير الصف المكتشف
            reply = self.show_custom_question("تأكيد صف الرؤوس", 
                                             f"تم اكتشاف صف رؤوس الأعمدة تلقائيًا: الصف رقم {header_row + 1}\n\n"
                                             "هل ترغب في استخدام هذا الصف كرؤوس للأعمدة؟")

            if not reply:
                # إذا اختار المستخدم "لا"، اطلب منه تحديد صف الرؤوس يدويًا
                input_dialog = QInputDialog(self)
                input_dialog.setInputMode(QInputDialog.IntInput)
                input_dialog.setWindowTitle("صف الرؤوس")
                input_dialog.setLabelText("أدخل رقم صف رؤوس الأعمدة:")
                input_dialog.setIntRange(1, 100)
                input_dialog.setIntValue(header_row + 1)
                input_dialog.setIntStep(1)

                # تعيين اتجاه النص من اليمين إلى اليسار
                input_dialog.setLayoutDirection(Qt.RightToLeft)

                # تعيين نمط CSS للنافذة
                input_dialog.setStyleSheet("""
                    QInputDialog {
                        background-color: #f0f0f0;
                    }
                    QLabel {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                        text-align: right;
                    }
                    QSpinBox {
                        font-family: Calibri;
                        font-size: 13pt;
                        min-height: 30px;
                        min-width: 100px;
                    }
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 100px;
                        min-height: 30px;
                    }
                """)

                ok = input_dialog.exec_()
                if ok:
                    header_row_plus_one = input_dialog.intValue()
                    header_row = header_row_plus_one - 1
                else:
                    return

            # قراءة ملف Excel باستخدام صف الرؤوس المكتشف
            self.excel_file_path = file_path
            self.df = pd.read_excel(file_path, header=header_row)

            # تحديث واجهة المستخدم
            self.info_area.setText(
                f"تم قراءة الملف: {os.path.basename(file_path)}\n"
                f"صف الرؤوس المستخدم: {header_row + 1}\n"
                f"عدد الأعمدة: {len(self.df.columns)}\n"
                f"عدد الصفوف: {len(self.df)}"
            )

            # تخطي مرحلة تحديد الأعمدة والانتقال مباشرة إلى مرحلة ربط الأعمدة
            self.selected_columns = list(self.df.columns)
            self.show_mapping_area()

            # تمكين زر ربط الأعمدة
            self.btn_map_columns.setEnabled(True)

        except Exception as e:
            self.show_custom_error("خطأ", f"حدث خطأ أثناء قراءة ملف Excel:\n{str(e)}")

    def show_mapping_interface(self):
        """عرض واجهة ربط الأعمدة"""
        self.show_mapping_area()

    def show_mapping_area(self):
        """عرض منطقة ربط الأعمدة"""
        # الحصول على أعمدة قاعدة البيانات
        db_columns = self.get_db_columns()

        # مسح منطقة أعمدة Excel
        self.clear_layout(self.excel_columns_layout)

        # مسح منطقة أعمدة قاعدة البيانات
        self.clear_layout(self.db_columns_layout)

        # إضافة أعمدة Excel
        for col in self.selected_columns:
            label = DraggableLabel(col)
            self.excel_columns_layout.addWidget(label)

        # إضافة أعمدة قاعدة البيانات
        self.drop_targets = {}
        for col in db_columns:
            if col != "id":  # استبعاد عمود المعرف
                drop_target = DropTargetWidget(col)
                self.db_columns_layout.addWidget(drop_target)
                self.drop_targets[col] = drop_target

        # تطبيق الربط التلقائي للأعمدة
        self.auto_map_columns()

        # إضافة مساحة فارغة في نهاية كل منطقة
        self.excel_columns_layout.addStretch()
        self.db_columns_layout.addStretch()

        # عرض منطقة الربط
        self.mapping_area.setVisible(True)

        # تمكين زر الاستيراد
        self.btn_import_data.setEnabled(True)

    def detect_header_row(self, file_path):
        """الكشف التلقائي عن صف رؤوس الأعمدة في ملف Excel"""
        try:
            # قراءة أول 10 صفوف من الملف
            sample_df = pd.read_excel(file_path, nrows=10, header=None)

            # البحث عن الصف الذي يحتوي على أكبر عدد من القيم النصية
            # (غالبًا ما تكون رؤوس الأعمدة نصية)
            text_counts = []
            for i in range(min(10, len(sample_df))):
                row = sample_df.iloc[i]
                text_count = sum(1 for val in row if isinstance(val, str))
                text_counts.append(text_count)

            # إذا كان هناك صف يحتوي على عدد كبير من القيم النصية، فمن المحتمل أن يكون صف الرؤوس
            if text_counts:
                max_text_row = text_counts.index(max(text_counts))

                # التحقق من أن الصف يحتوي على قيم فريدة (غالبًا ما تكون رؤوس الأعمدة فريدة)
                unique_values = len(set(sample_df.iloc[max_text_row].dropna()))
                total_values = len(sample_df.iloc[max_text_row].dropna())

                if unique_values / total_values > 0.8:  # إذا كان أكثر من 80% من القيم فريدة
                    return max_text_row

            # إذا لم يتم العثور على صف مناسب، استخدم الصف الأول كافتراضي
            return 0
        except Exception:
            # في حالة حدوث أي خطأ، استخدم الصف الأول كافتراضي
            return 0

    def select_excel_file(self):
        """اختيار ملف Excel وقراءة بياناته"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف Excel", "", "Excel Files (*.xlsx *.xls)"
        )

        if not file_path:
            return

        try:
            # الكشف التلقائي عن صف رؤوس الأعمدة
            header_row = self.detect_header_row(file_path)

            # عرض رسالة تأكيد مخصصة مع إمكانية تغيير الصف المكتشف
            reply = self.show_custom_question("تأكيد صف الرؤوس", 
                                             f"تم اكتشاف صف رؤوس الأعمدة تلقائيًا: الصف رقم {header_row + 1}\n\n"
                                             "هل ترغب في استخدام هذا الصف كرؤوس للأعمدة؟")

            if not reply:
                # إذا اختار المستخدم "لا"، اطلب منه تحديد صف الرؤوس يدويًا
                input_dialog = QInputDialog(self)
                input_dialog.setInputMode(QInputDialog.IntInput)
                input_dialog.setWindowTitle("صف الرؤوس")
                input_dialog.setLabelText("أدخل رقم صف رؤوس الأعمدة:")
                input_dialog.setIntRange(1, 100)
                input_dialog.setIntValue(header_row + 1)
                input_dialog.setIntStep(1)

                # تعيين اتجاه النص من اليمين إلى اليسار
                input_dialog.setLayoutDirection(Qt.RightToLeft)

                # تعيين نمط CSS للنافذة
                input_dialog.setStyleSheet("""
                    QInputDialog {
                        background-color: #f0f0f0;
                    }
                    QLabel {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                        text-align: right;
                    }
                    QSpinBox {
                        font-family: Calibri;
                        font-size: 13pt;
                        min-height: 30px;
                        min-width: 100px;
                    }
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 100px;
                        min-height: 30px;
                    }
                """)

                ok = input_dialog.exec_()
                if ok:
                    header_row_plus_one = input_dialog.intValue()
                    header_row = header_row_plus_one - 1
                else:
                    return

            # قراءة ملف Excel باستخدام صف الرؤوس المكتشف
            self.excel_file_path = file_path
            self.df = pd.read_excel(file_path, header=header_row)

            # تحديث واجهة المستخدم
            self.info_area.setText(
                f"تم قراءة الملف: {os.path.basename(file_path)}\n"
                f"صف الرؤوس المستخدم: {header_row + 1}\n"
                f"عدد الأعمدة: {len(self.df.columns)}\n"
                f"عدد الصفوف: {len(self.df)}"
            )

            # تخطي مرحلة تحديد الأعمدة والانتقال مباشرة إلى مرحلة ربط الأعمدة
            self.selected_columns = list(self.df.columns)
            self.show_mapping_area()

            # تمكين زر ربط الأعمدة
            self.btn_map_columns.setEnabled(True)

        except Exception as e:
            self.show_custom_error("خطأ", f"حدث خطأ أثناء قراءة ملف Excel:\n{str(e)}")

    def show_mapping_interface(self):
        """عرض واجهة ربط الأعمدة"""
        self.show_mapping_area()

    def show_mapping_area(self):
        """عرض منطقة ربط الأعمدة"""
        # الحصول على أعمدة قاعدة البيانات
        db_columns = self.get_db_columns()

        # مسح منطقة أعمدة Excel
        self.clear_layout(self.excel_columns_layout)

        # مسح منطقة أعمدة قاعدة البيانات
        self.clear_layout(self.db_columns_layout)

        # إضافة أعمدة Excel
        for col in self.selected_columns:
            label = DraggableLabel(col)
            self.excel_columns_layout.addWidget(label)

        # إضافة أعمدة قاعدة البيانات
        self.drop_targets = {}
        for col in db_columns:
            if col != "id":  # استبعاد عمود المعرف
                drop_target = DropTargetWidget(col)
                self.db_columns_layout.addWidget(drop_target)
                self.drop_targets[col] = drop_target

        # تطبيق الربط التلقائي للأعمدة
        self.auto_map_columns()

        # إضافة مساحة فارغة في نهاية كل منطقة
        self.excel_columns_layout.addStretch()
        self.db_columns_layout.addStretch()

        # عرض منطقة الربط
        self.mapping_area.setVisible(True)

        # تمكين زر الاستيراد
        self.btn_import_data.setEnabled(True)

    def auto_map_columns(self):
        """ربط الأعمدة تلقائياً بناءً على الأسماء المتشابهة"""
        # قاموس ربط أعمدة قاعدة البيانات بالأسماء المحتملة في Excel
        column_mappings = {
            'اسم_التلميذ': ['اسم التلميذ', 'الاسم الكامل', 'الاسم والنسب', 'النسب والاسم', 'اسم_التلميذ', 
                           'اسم كامل', 'nom complet', 'full name', 'nom', 'name'],
            'رمز_التلميذ': ['رقم التسجيل', 'الرقم الوطني', 'الرمز', 'رمز التلميذ', 'رمز_التلميذ', 'code', 'id', 'numero'],
            'النوع': ['الجنس', 'جنس', 'النوع', 'نوع', 'sexe', 'gender', 'sex'],
            'رقم_الهاتف_الأول': ['رقم الهاتف', 'الهاتف', 'رقم_الهاتف_الأول', 'هاتف', 'telephone', 'phone'],
            'رقم_الهاتف_الثاني': ['رقم الهاتف الثاني', 'الهاتف الثاني', 'رقم_الهاتف_الثاني', 'هاتف ثاني'],
            'ملاحظات': ['ملاحظات', 'ملاحظة', 'تعليقات', 'notes', 'comments'],
            'اسم_المجموعة': ['اسم المجموعة', 'المجموعة', 'مجموعة', 'اسم_المجموعة', 'groupe', 'group'],
            'القسم': ['القسم', 'قسم', 'الشعبة', 'شعبة', 'section', 'classe'],
            'المؤسسة_الاصلية': ['المؤسسة الأصلية', 'المؤسسة الاصلية', 'المؤسسة', 'مؤسسة', 'اسم المؤسسة', 'اسم المؤسسة الأصلية'],
            'اجمالي_مبلغ_التسجيل': ['إجمالي مبلغ التسجيل', 'مبلغ التسجيل', 'اجمالي_مبلغ_التسجيل', 'التسجيل'],
            'الواجب_الشهري': ['الواجب الشهري', 'واجب شهري', 'الواجب_الشهري', 'شهري']
        }

        # قائمة بالأعمدة التي يجب تجنب ربطها (أعمدة الرموز)
        excluded_mappings = {
            'اسم_المجموعة': ['رمز المجموعة', 'رمز_المجموعة', 'code groupe'],
            'المؤسسة_الاصلية': ['رمز المؤسسة', 'رمز_المؤسسة', 'code etablissement', 'code institution']
        }

        # قائمة بالأعمدة المربوطة لتجنب الربط المتكرر
        mapped_excel_columns = set()

        # تطبيق الربط التلقائي
        for db_column, possible_names in column_mappings.items():
            if db_column in self.drop_targets:
                # البحث عن تطابق في أعمدة Excel
                for excel_col in self.selected_columns:
                    if excel_col in mapped_excel_columns:
                        continue  # تجاهل الأعمدة المربوطة مسبقاً
                    
                    # تحويل اسم العمود إلى نص صغير للمقارنة
                    excel_col_lower = excel_col.lower().strip()
                    
                    # التحقق من عدم وجود العمود في قائمة الاستبعاد
                    is_excluded = False
                    if db_column in excluded_mappings:
                        for excluded_name in excluded_mappings[db_column]:
                            excluded_name_lower = excluded_name.lower().strip()
                            if (excel_col_lower == excluded_name_lower or 
                                excluded_name_lower in excel_col_lower or 
                                excel_col_lower in excluded_name_lower):
                                is_excluded = True
                                break
                    
                    # إذا كان العمود مستبعداً، تجاهله
                    if is_excluded:
                        continue
                    
                    # البحث عن تطابق مباشر أو جزئي
                    for possible_name in possible_names:
                        possible_name_lower = possible_name.lower().strip()
                        
                        # التحقق من التطابق المباشر أو الجزئي
                        if (excel_col_lower == possible_name_lower or 
                            possible_name_lower in excel_col_lower or 
                            excel_col_lower in possible_name_lower):
                            
                            # ربط العمود
                            self.drop_targets[db_column].excel_column = excel_col
                            self.drop_targets[db_column].update_display()
                            mapped_excel_columns.add(excel_col)
                            break
                    
                    if excel_col in mapped_excel_columns:
                        break  # انتقل إلى عمود قاعدة البيانات التالي

        # عرض رسالة إعلامية عن الربط التلقائي
        mapped_count = len(mapped_excel_columns)
        if mapped_count > 0:
            self.show_auto_mapping_info(mapped_count, len(self.selected_columns))

    def import_data(self):
        """استيراد البيانات إلى جدول البيانات"""
        if self.df is None or self.df.empty:
            self.show_custom_warning("تنبيه", "لا توجد بيانات للاستيراد. الرجاء اختيار ملف Excel أولاً.")
            return

        # جمع الربط بين الأعمدة
        column_mapping = {}
        for db_col, drop_target in self.drop_targets.items():
            if drop_target.excel_column:
                column_mapping[drop_target.excel_column] = db_col

        if not column_mapping:
            self.show_custom_warning("تنبيه", "لم يتم ربط أي أعمدة. الرجاء سحب أعمدة Excel وإفلاتها على أعمدة قاعدة البيانات.")
            return

        # عرض رسالة تأكيد
        reply = self.show_custom_question("تأكيد الاستيراد", 
                                         f"هل أنت متأكد من استيراد {len(self.df)} صف إلى جدول البيانات؟\n\n"
                                         "ملاحظة: سيتم إضافة البيانات إلى الجدول الحالي.\n"
                                         "سيتم حذف الصفوف الفارغة أو التي تحتوي على قيم NULL تلقائيًا بعد الاستيراد.")

        if not reply:
            return

        try:
            # إنشاء اتصال بقاعدة البيانات
            if not self.db_connection:
                conn = sqlite3.connect("data.db")
                is_temp_connection = True
            else:
                # التحقق من نوع الاتصال
                if hasattr(self.db_connection, 'cursor'):
                    # إذا كان اتصال sqlite3 مباشر
                    conn = self.db_connection
                    is_temp_connection = False
                elif hasattr(self.db_connection, 'databaseName'):
                    # إذا كان كائن QSqlDatabase، قم بإنشاء اتصال sqlite3 جديد
                    db_name = self.db_connection.databaseName()
                    print(f"تحويل اتصال QSqlDatabase إلى اتصال sqlite3 من المسار: {db_name}")
                    conn = sqlite3.connect(db_name)
                    is_temp_connection = True
                else:
                    # إذا كان نوع غير معروف، استخدم اتصال افتراضي
                    print(f"نوع اتصال قاعدة البيانات غير معروف: {type(self.db_connection)}")
                    conn = sqlite3.connect("data.db")
                    is_temp_connection = True

            cursor = conn.cursor()

            # بدء المعاملة
            conn.execute("BEGIN TRANSACTION")

            # استيراد البيانات
            imported_rows = 0
            for _, row in self.df.iterrows():
                # بناء استعلام الإدراج
                columns = []
                placeholders = []
                values = []

                for excel_col, db_col in column_mapping.items():
                    columns.append(db_col)
                    placeholders.append("?")
                    values.append(str(row[excel_col]) if pd.notna(row[excel_col]) else "")

                # بناء استعلام الإدراج
                sql = f"INSERT INTO جدول_البيانات ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

                # تنفيذ الاستعلام
                cursor.execute(sql, values)
                imported_rows += 1

            # حفظ التغييرات
            conn.commit()

            # حذف الصفوف الفارغة أو التي تحتوي على قيم NULL
            deleted_rows = self.delete_empty_rows(conn)

            # إغلاق الاتصال إذا كان مؤقتًا
            if is_temp_connection:
                conn.close()

            # عرض رسالة نجاح
            self.show_custom_success("تم الاستيراد", 
                                   f"تم استيراد {imported_rows} صف بنجاح إلى جدول البيانات.\n"
                                   f"تم حذف {deleted_rows} صف فارغ أو يحتوي على قيم NULL.")

            # إغلاق النافذة
            self.close()

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if 'conn' in locals():
                conn.rollback()
                if 'is_temp_connection' in locals() and is_temp_connection:
                    conn.close()

            self.show_custom_error("خطأ", f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}")

    def delete_empty_rows(self, conn):
        """حذف الصفوف الفارغة أو التي تحتوي على قيم NULL من جدول البيانات"""
        try:
            cursor = conn.cursor()

            # بناء استعلام لحذف الصفوف التي تحتوي على قيم فارغة أو NULL في الأعمدة المهمة
            # الأعمدة المهمة هي: اسم_التلميذ، رمز_التلميذ، اسم_المجموعة، القسم
            query = """
            DELETE FROM جدول_البيانات
            WHERE اسم_التلميذ IS NULL OR اسم_التلميذ = ''
               OR رمز_التلميذ IS NULL OR رمز_التلميذ = ''
               OR اسم_المجموعة IS NULL OR اسم_المجموعة = ''
               OR القسم IS NULL OR القسم = ''
            """

            # تنفيذ الاستعلام
            cursor.execute(query)

            # الحصول على عدد الصفوف المحذوفة
            deleted_rows = cursor.rowcount

            # حفظ التغييرات
            conn.commit()

            return deleted_rows

        except Exception as e:
            print(f"خطأ أثناء حذف الصفوف الفارغة: {str(e)}")
            return 0

    def show_custom_success(self, title, message):
        """عرض رسالة نجاح مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c8);
                border: 3px solid #28a745;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة النجاح
        icon_label = QLabel()
        icon_label.setText("✓")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #28a745; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #155724; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #155724; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
            QPushButton:pressed {
                background: #1e7e34;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

    def show_custom_warning(self, title, message):
        """عرض رسالة تحذير مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fff3cd, stop:1 #ffeaa7);
                border: 3px solid #ffc107;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة التحذير
        icon_label = QLabel()
        icon_label.setText("⚠")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #856404; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #856404; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #856404; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                color: #212529;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffcd39, stop:1 #ffc107);
            }
            QPushButton:pressed {
                background: #e0a800;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

    def show_custom_error(self, title, message):
        """عرض رسالة خطأ مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8d7da, stop:1 #f1b0b7);
                border: 3px solid #dc3545;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة الخطأ
        icon_label = QLabel()
        icon_label.setText("✗")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #721c24; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #721c24; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #721c24; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e4606d, stop:1 #dc3545);
            }
            QPushButton:pressed {
                background: #c82333;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

    def show_custom_question(self, title, message):
        """عرض رسالة سؤال مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #d4edda, stop:1 #c3e6cb);
                border: 3px solid #17a2b8;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة السؤال
        icon_label = QLabel()
        icon_label.setText("؟")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #17a2b8; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #0c5460; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #0c5460; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        
        yes_button = QPushButton("نعم")
        yes_button.setFont(QFont("Calibri", 14, QFont.Bold))
        yes_button.setFixedSize(120, 45)
        yes_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
            QPushButton:pressed {
                background: #1e7e34;
            }
        """)
        yes_button.clicked.connect(lambda: msg_dialog.done(1))
        
        no_button = QPushButton("لا")
        no_button.setFont(QFont("Calibri", 14, QFont.Bold))
        no_button.setFixedSize(120, 45)
        no_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e4606d, stop:1 #dc3545);
            }
            QPushButton:pressed {
                background: #c82333;
            }
        """)
        no_button.clicked.connect(lambda: msg_dialog.done(0))

        buttons_layout.addStretch()
        buttons_layout.addWidget(yes_button)
        buttons_layout.addWidget(no_button)
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        return msg_dialog.exec_() == 1

    def get_db_columns(self):
        """الحصول على أعمدة جدول البيانات من قاعدة البيانات"""
        try:
            # إنشاء اتصال مؤقت بقاعدة البيانات إذا لم يكن موجودًا
            if not self.db_connection:
                conn = sqlite3.connect("data.db")
                is_temp_connection = True
            else:
                # التحقق من نوع الاتصال
                if hasattr(self.db_connection, 'cursor'):
                    # إذا كان اتصال sqlite3 مباشر
                    conn = self.db_connection
                    is_temp_connection = False
                elif hasattr(self.db_connection, 'databaseName'):
                    # إذا كان كائن QSqlDatabase، قم بإنشاء اتصال sqlite3 جديد
                    db_name = self.db_connection.databaseName()
                    print(f"تحويل اتصال QSqlDatabase إلى اتصال sqlite3 من المسار: {db_name}")
                    conn = sqlite3.connect(db_name)
                    is_temp_connection = True
                else:
                    # إذا كان نوع غير معروف، استخدم اتصال افتراضي
                    print(f"نوع اتصال قاعدة البيانات غير معروف: {type(self.db_connection)}")
                    conn = sqlite3.connect("data.db")
                    is_temp_connection = True

            cursor = conn.cursor()

            # التحقق من وجود جدول البيانات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_البيانات'")
            if not cursor.fetchone():
                # إنشاء جدول البيانات إذا لم يكن موجودًا
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS جدول_البيانات (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        
                        -- معلومات الاتصال
                        اسم_التلميذ TEXT NOT NULL,
                        رمز_التلميذ TEXT UNIQUE,
                        النوع TEXT,
                        رقم_الهاتف_الأول TEXT,
                        رقم_الهاتف_الثاني TEXT,
                        ملاحظات TEXT,
                        
                        -- معلومات التمدرس
                        اسم_المجموعة TEXT,
                        القسم TEXT,
                        المؤسسة_الاصلية TEXT,
                        
                        -- واجبات التسجيل
                        اجمالي_مبلغ_التسجيل REAL,
                        عدد_الاقساط INTEGER,
                        مبلغ_القسط REAL,
                        
                        -- الواجبات الشهرية
                        الواجب_الشهري REAL,
                        الاشهر_المحددة TEXT,
                        المبلغ_النهائي_الشهري REAL,
                        
                        -- تواريخ النظام
                        تاريخ_الانشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                        تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()

            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(جدول_البيانات)")
            columns = [row[1] for row in cursor.fetchall()]

            # إغلاق الاتصال إذا كان مؤقتًا
            if is_temp_connection:
                conn.close()

            return columns

        except Exception as e:
            self.show_custom_error("خطأ", f"حدث خطأ أثناء الاتصال بقاعدة البيانات:\n{str(e)}")
            return []

    def clear_layout(self, layout):
        """مسح جميع العناصر من التخطيط"""
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    self.clear_layout(item.layout())

    def clean_data(self):
        """تنظيف البيانات وإزالة الأخطاء الشائعة"""
        if self.df is None or self.df.empty:
            self.show_custom_warning("تنبيه", "لا توجد بيانات للتنظيف. الرجاء اختيار ملف Excel أولاً.")
            return

        try:
            # تنظيف البيانات
            # 1. إزالة الفراغات الزائدة من البيانات النصية
            for col in self.df.select_dtypes(include=['object']).columns:
                self.df[col] = self.df[col].astype(str).str.strip()

            # 2. توحيد تنسيق التاريخ إذا كان هناك عمود للتاريخ
            date_columns = [col for col in self.df.columns if 'تاريخ' in col.lower()]
            for col in date_columns:
                try:
                    self.df[col] = pd.to_datetime(self.df[col], errors='coerce').dt.strftime('%Y-%m-%d')
                except:
                    pass

            # 3. توحيد قيم الجنس (ذكر/أنثى)
            gender_columns = [col for col in self.df.columns if 'جنس' in col.lower() or 'نوع' in col.lower()]
            for col in gender_columns:
                self.df[col] = self.df[col].apply(lambda x: self.standardize_gender(x))

            # عرض رسالة نجاح
            self.show_custom_success("تم التنظيف", "تم تنظيف البيانات بنجاح.")

            # تحديث معلومات الملف
            self.info_area.setText(
                f"تم قراءة الملف: {os.path.basename(self.excel_file_path)}\n"
                f"عدد الأعمدة: {len(self.df.columns)}\n"
                f"عدد الصفوف: {len(self.df)}\n"
                f"تم تنظيف البيانات بنجاح."
            )

        except Exception as e:
            self.show_custom_error("خطأ", f"حدث خطأ أثناء تنظيف البيانات:\n{str(e)}")

    def standardize_gender(self, value):
        """توحيد قيم الجنس"""
        if not value or pd.isna(value):
            return ""

        value = str(value).strip().lower()

        # قائمة بالكلمات التي تشير إلى الذكر
        male_words = ['ذكر', 'ذ', 'm', 'male', 'رجل', 'ولد', 'فتى']

        # قائمة بالكلمات التي تشير إلى الأنثى
        female_words = ['أنثى', 'انثى', 'ا', 'f', 'female', 'امرأة', 'بنت', 'فتاة']

        for word in male_words:
            if word in value:
                return "ذكر"

        for word in female_words:
            if word in value:
                return "أنثى"

        return value

    def show_auto_mapping_info(self, mapped_count, total_count):
        """عرض رسالة إعلامية عن نتائج الربط التلقائي"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle("الربط التلقائي للأعمدة")
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #d1ecf1, stop:1 #bee5eb);
                border: 3px solid #17a2b8;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة المعلومات
        icon_label = QLabel()
        icon_label.setText("ℹ")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #17a2b8; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel("الربط التلقائي للأعمدة")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #0c5460; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        message = f"تم ربط {mapped_count} عمود من أصل {total_count} عمود تلقائياً.\n\n"
        if mapped_count < total_count:
            message += "يمكنك ربط باقي الأعمدة يدوياً عن طريق السحب والإفلات.\n\n"
        message += "تحقق من صحة الربط قبل المتابعة للاستيراد."
        
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #0c5460; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #17a2b8);
            }
            QPushButton:pressed {
                background: #138496;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

# للاختبار المستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ExcelImportWindow()
    window.show()
    sys.exit(app.exec_())
