import sqlite3
import sys
import os
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QTextEdit, QHBoxLayout, QPushButton,
                           QFileDialog, QMessageBox, QFrame, QGraphicsDropShadowEffect, QProgressDialog)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
from help_guide import show_help_guide

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    # إنشاء كائن بديل للتعامل مع الخطأ بشكل أفضل
    class PandasSubstitute:
        def __getattr__(self, name):
            raise ImportError(
                "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر:\n"
                "pip install pandas --only-binary=:all:\n\n"
                "أو تثبيت نسخة بديلة مجمعة مسبقاً:\n"
                "pip install pandas-only-binary"
            )
    pd = PandasSubstitute()
    PANDAS_AVAILABLE = False

class DataImporter:
    def __init__(self, log_text, main_window=None):
        """فئة مخصصة لاستيراد البيانات من ملفات Excel إلى قاعدة البيانات"""
        self.log_text = log_text
        self.db_path = "data.db"
        self.main_window = main_window  # إضافة مرجع للنافذة الرئيسية للاستخدام في تحديث لون الخلفية
        self.parent_widget = main_window  # نخزن مرجعًا للنافذة الأب لاستخدامها في مربعات الحوار

        # التحقق من توفر pandas وإضافة تحذير إذا لم تكن متوفرة
        if not PANDAS_AVAILABLE:
            self.log("تنبيه: مكتبة pandas غير متوفرة. لن تعمل وظائف استيراد البيانات من Excel.", "warning")
            self.log("لتثبيت pandas، استخدم الأمر: pip install pandas --only-binary=:all:", "info")

    def log(self, message, status="info"):
        """إضافة رسالة إلى سجل العمليات مع لون مميز"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")

        # تحديد رمز الحالة
        status_icon = {
            "info": "ℹ️",
            "success": "✅",
            "error": "❌",
            "warning": "⚠️",
            "progress": "🔄"
        }.get(status, "ℹ️")

        self.log_text.append(f"{status_icon} {timestamp} - {message}")

    def update_background_color(self):
        """تحديث لون خلفية النافذة إذا كان هناك مرجع للنافذة الرئيسية"""
        if hasattr(self, 'main_window') and self.main_window and hasattr(self.main_window, 'background_color'):
            self.main_window.setStyleSheet(f"background-color: {self.main_window.background_color};")

class Sub1Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("استيراد اللوائح من منظومة مسار")
        self.setFixedSize(1000, 500)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج إلى النافذة
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # تعريف مسار قاعدة البيانات
        self.db_path = "data.db"

        # التحقق من توفر pandas
        self.pandas_available = PANDAS_AVAILABLE

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # إنشاء تخطيط أفقي للزر الرئيسي في الأعلى
        top_buttons_layout = QHBoxLayout()
        top_buttons_layout.setSpacing(10)

        # الزر الوحيد المطلوب - استيراد اللوائح من منظومة مسار
        masar_button = QPushButton("استيراد اللوائح من منظومة مسار باللغة العربية")
        masar_button.setFont(QFont('Calibri', 12, QFont.Bold))
        masar_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border-radius: 8px;
                padding: 15px;
                min-height: 50px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:pressed {
                background-color: #7d3c98;
            }
        """)
        masar_button.clicked.connect(self.import_masar_data)
        top_buttons_layout.addWidget(masar_button)

        # إضافة تخطيط الزر إلى التخطيط الرئيسي
        main_layout.addLayout(top_buttons_layout)

        # إنشاء إطار رئيسي مع تأثير الظل
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.Box)
        content_frame.setFrameShadow(QFrame.Raised)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #bdc3c7;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        content_frame.setGraphicsEffect(shadow)

        # تخطيط المحتوى داخل الإطار
        frame_layout = QVBoxLayout(content_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(15)

        # مربع السجلات
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont('Calibri', 12))
        self.log_text.setMinimumHeight(350)  # تقليل الارتفاع قليلاً لإفساح المجال للأزرار
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        # إضافة مربع السجلات إلى تخطيط الإطار
        frame_layout.addWidget(self.log_text)

        # إنشاء تخطيط أفقي للأزرار في الأسفل
        bottom_buttons_layout = QHBoxLayout()
        bottom_buttons_layout.setSpacing(10)

        # زر تحديث البيانات
        refresh_button = QPushButton("🔄 تحديث البيانات")
        refresh_button.setFont(QFont('Calibri', 12, QFont.Bold))
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 8px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        refresh_button.clicked.connect(self.manual_refresh)
        bottom_buttons_layout.addWidget(refresh_button)

        # زر تعليمات الاستخدام
        help_button = QPushButton("❓ تعليمات الاستخدام")
        help_button.setFont(QFont('Calibri', 12, QFont.Bold))
        help_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 8px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_button.clicked.connect(self.show_help_guide)
        bottom_buttons_layout.addWidget(help_button)

        # إضافة تخطيط الأزرار السفلية إلى تخطيط الإطار
        frame_layout.addLayout(bottom_buttons_layout)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(content_frame)

        # يمكن إضافة خاصية لون الخلفية هنا
        self.background_color = "#f5f5f5"  # لون افتراضي

        # إنشاء كائن المستورد
        self.importer = DataImporter(self.log_text, self)

        # رسالة ترحيبية
        self.importer.log("أهلاً بك في نافذة استيراد اللوائح من منظومة مسار", "info")
        self.importer.log("اضغط على الزر أعلاه لاستيراد البيانات من ملف Excel", "info")

    def show_message_box(self, title, message, icon=QMessageBox.Information):
        """عرض رسالة موحدة بتنسيق محدد"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)

        # إضافة أيقونة البرنامج إلى نافذة الرسالة
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            msg_box.setWindowIcon(QIcon(icon_path))

        # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
        msg_box.setText(f"<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>{message}</p>")
        msg_box.setIcon(icon)

        # تخصيص أنماط مربع الحوار
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
            }
            QLabel {
                font-family: 'Calibri';
                min-width: 300px;
            }
            QPushButton {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: white;
                background-color: #0D47A1;
                border: none;
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)

        # إضافة زر موافق فقط
        ok_button = msg_box.addButton("موافق", QMessageBox.AcceptRole)

        # تعيين الزر الافتراضي
        msg_box.setDefaultButton(ok_button)

        # عرض رسالة
        msg_box.exec_()

    def import_masar_data(self):
        """استدعاء وظيفة استيراد بيانات منظومة مسار"""
        # التحقق من توفر pandas
        if not PANDAS_AVAILABLE:
            self.show_pandas_not_installed()
            return

        # مسح مربع السجلات قبل البدء بالاستيراد
        self.log_text.clear()

        # إعادة عرض رسائل الترحيب
        self.importer.log("أهلاً بك في نافذة استيراد اللوائح من منظومة مسار", "info")
        self.importer.log("اضغط على الزر أعلاه لاستيراد البيانات من ملف Excel", "info")

        self.importer.log("جاري استيراد البيانات من منظومة مسار...", "progress")

        conn = None
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف السجلات الافتراضية قبل الاستيراد
            self.importer.log("جاري حذف السجلات الافتراضية قبل الاستيراد...", "progress")
            try:
                # حذف السجل الافتراضي من جدول السجل_العام
                cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    self.importer.log(f"تم حذف {deleted_count} سجل افتراضي من جدول السجل_العام", "info")

                # حذف السجل الافتراضي من جدول اللوائح
                cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    self.importer.log(f"تم حذف {deleted_count} سجل افتراضي من جدول اللوائح", "info")

                # تطبيق التغييرات
                conn.commit()
            except Exception as e:
                self.importer.log(f"حدث خطأ أثناء حذف السجلات الافتراضية: {str(e)}", "warning")
                # نستمر في العملية حتى لو فشل حذف السجلات الافتراضية

            # حذف جدول السجل الاولي فقط
            cursor.execute("DROP TABLE IF EXISTS 'السجل الاولي'")

            # فتح نافذة لتصفح الملفات لاختيار ملف الاكسل
            file_path, _ = QFileDialog.getOpenFileName(self, "اختر ملف الاكسل", "", "Excel Files (*.xlsx *.xls)")
            if not file_path:
                self.importer.log("تم إلغاء عملية الاستيراد", "warning")
                if conn:
                    conn.close()
                return

            # التحقق من اسم الملف إذا كان يحتوي على "ListEleve"
            file_name = file_path.split('/')[-1].split('\\')[-1]
            if "ListEleve" not in file_name:
                warning_message = f"الملف {file_name} لا يحتوي على العبارة 'ListEleve' في اسمه. قد لا يكون هذا ملف لوائح منظومة مسار."
                self.importer.log(warning_message, "warning")

                # عرض رسالة تأكيد للمستخدم بتصميم أنيق
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("تنبيه: ملف غير معروف")

                # إضافة أيقونة البرنامج إلى نافذة الرسالة
                icon_path = "01.ico"
                if os.path.exists(icon_path):
                    msg_box.setWindowIcon(QIcon(icon_path))

                # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
                msg_box.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>تنبيه: ملف غير معروف</p>")

                # إضافة نص توضيحي
                msg_box.setInformativeText(f"<p style='font-family: Calibri; font-size: 11pt;'>{warning_message}</p><p style='font-family: Calibri; font-size: 11pt;'>هل تريد الاستمرار في استيراد هذا الملف على أي حال؟</p>")

                # تعيين أيقونة التحذير
                msg_box.setIcon(QMessageBox.Warning)

                # إنشاء أزرار مخصصة
                continue_button = msg_box.addButton("نعم، استمر في الاستيراد", QMessageBox.YesRole)
                cancel_button = msg_box.addButton("لا، إلغاء الاستيراد", QMessageBox.NoRole)

                # تخصيص أنماط الأزرار
                continue_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: white;
                        background-color: #0D47A1;
                        border: none;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #1565C0;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)

                cancel_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: #0D47A1;
                        background-color: #E3F2FD;
                        border: 1px solid #0D47A1;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #BBDEFB;
                    }
                    QPushButton:pressed {
                        background-color: #E3F2FD;
                    }
                """)

                # تعيين الزر الافتراضي (زر الإلغاء)
                msg_box.setDefaultButton(cancel_button)

                # تعيين نمط مربع الحوار
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                    }
                    QLabel {
                        font-family: Calibri;
                        min-width: 300px;
                    }
                """)

                # عرض مربع الحوار وانتظار الرد
                msg_box.exec_()

                # معالجة الرد
                if msg_box.clickedButton() == cancel_button:
                    self.importer.log("تم إلغاء عملية الاستيراد بناءً على طلب المستخدم", "info")
                    if conn:
                        conn.close()
                    return
                else:
                    self.importer.log("تم اختيار الاستمرار في الاستيراد رغم التحذير", "info")

            # إنشاء نافذة شريط التقدم (بعد اختيار الملف)
            progress_dialog = QProgressDialog("جاري استيراد البيانات من منظومة مسار...", None, 0, 100, self)
            progress_dialog.setWindowTitle("استيراد البيانات")

            # إضافة أيقونة البرنامج إلى نافذة شريط التقدم
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                progress_dialog.setWindowIcon(QIcon(icon_path))

            # تخصيص مظهر شريط التقدم
            progress_dialog.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                    font-family: Calibri;
                    font-size: 13pt;
                    font-weight: bold;
                    color: #0D47A1;
                }
                QProgressBar {
                    border: 2px solid #0D47A1;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #E3F2FD;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #0D47A1;
                    width: 10px;
                    margin: 0.5px;
                }
            """)

            # تعيين خصائص شريط التقدم
            progress_dialog.setMinimumDuration(0)  # عرض شريط التقدم فوراً
            progress_dialog.setWindowModality(Qt.WindowModal)  # جعل النافذة مشروطة
            progress_dialog.setAutoClose(True)  # إغلاق النافذة تلقائياً عند الانتهاء
            progress_dialog.setAutoReset(True)  # إعادة تعيين شريط التقدم تلقائياً
            progress_dialog.setMinimumWidth(400)  # تعيين العرض الأدنى
            progress_dialog.setCancelButton(None)  # إزالة زر الإلغاء

            # عرض شريط التقدم
            progress_dialog.setValue(0)
            progress_dialog.show()
            QApplication.processEvents()

            # قراءة جميع الشيتات من ملف الاكسل
            sheets_dict = pd.read_excel(file_path, sheet_name=None)

            # استخراج السنة الدراسية من الملف (الصف السادس من العمود Unnamed: 6)
            current_academic_year = None

            for sheet_name, df in sheets_dict.items():
                if "Unnamed: 6" in df.columns and len(df) > 5:
                    current_academic_year = df.iloc[5]["Unnamed: 6"]
                    break

            # تحديث شريط التقدم - 10%
            progress_dialog.setValue(10)
            progress_dialog.setLabelText("جاري تحضير البيانات...")
            QApplication.processEvents()

            # تحضير البيانات
            total_sheets = len(sheets_dict)
            for i, (sheet_name, df) in enumerate(sheets_dict.items()):
                # تحديث شريط التقدم - من 10% إلى 30%
                progress_percent = 10 + int((i / total_sheets) * 20)
                progress_dialog.setValue(progress_percent)
                progress_dialog.setLabelText(f"جاري معالجة القسم {sheet_name}...")
                QApplication.processEvents()

                level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
                level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
                year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
                df["القسم"] = sheet_name
                df["المستوى"] = level_value
                df["السنة الدراسية"] = year_value

            # تحديث شريط التقدم - 30%
            progress_dialog.setValue(30)
            progress_dialog.setLabelText("جاري دمج البيانات وحفظها...")
            QApplication.processEvents()

            # دمج الشيتات وحفظها
            combined_df = pd.concat(sheets_dict.values(), ignore_index=True)
            combined_df.to_sql("السجل الاولي", conn, if_exists='append', index=False)

            # تحديث شريط التقدم - 50%
            progress_dialog.setValue(50)
            progress_dialog.setLabelText("جاري تحديث قواعد البيانات...")
            QApplication.processEvents()

            # تحديث قواعد البيانات بدون عرض تفاصيل
            self.process_data_silently(cursor, current_academic_year, progress_dialog)

            # تحديث شريط التقدم - 90%
            progress_dialog.setValue(90)
            progress_dialog.setLabelText("جاري إنهاء العملية...")
            QApplication.processEvents()

            conn.commit()
            conn.close()
            conn = None

            # تحديث شريط التقدم - 100%
            progress_dialog.setValue(100)
            progress_dialog.setLabelText("تم الانتهاء من استيراد البيانات بنجاح!")
            QApplication.processEvents()

            # عرض ملخص نتائج الاستيراد
            self.display_import_summary(current_academic_year)

            # تحديث ترتيب المستويات بعد استيراد اللوائح من منظومة مسار
            self.update_levels_order()

            # تعيين جميع الأقسام للسنة الدراسية الحالية إلى حراسة رقم 1
            self.assign_all_to_guard1()

            # تحديث نافذة إدارة الأقسام والحراسة كآخر مهمة
            self.update_sub11_window()

        except Exception as e:
            self.importer.log(f"حدث خطأ أثناء استيراد البيانات: {str(e)}", "error")
            self.show_message_box("خطأ", f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}", QMessageBox.Critical)
        finally:
            # التأكد من إغلاق الاتصال في جميع الحالات
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def process_data_silently(self, cursor, academic_year, progress_dialog=None):
        """معالجة البيانات بهدوء بدون عرض كل خطوة في السجل"""
        try:
            # تحديث بيانات المؤسسة
            if progress_dialog:
                progress_dialog.setValue(55)
                progress_dialog.setLabelText("جاري تحديث بيانات المؤسسة...")
                QApplication.processEvents()
            self.update_school_info(cursor)

            # إنشاء وتحديث اللوائح
            if progress_dialog:
                progress_dialog.setValue(60)
                progress_dialog.setLabelText("جاري إنشاء وتحديث اللوائح...")
                QApplication.processEvents()
            cursor.execute("CREATE TABLE IF NOT EXISTS 'اللوائح' ('السنة_الدراسية' TEXT, 'القسم' TEXT, 'المستوى' TEXT, 'الرمز' TEXT, 'رت' TEXT, 'مجموع التلاميذ' INTEGER DEFAULT 0, PRIMARY KEY('السنة_الدراسية', 'الرمز'))")

            # حذف السجلات الافتراضية من جدول السجل_العام
            cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")

            # حذف السجلات من جدول اللوائح بناءً على السنة الدراسية
            if academic_year:
                cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))

            # حذف السجلات الافتراضية من جدول اللوائح
            cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = '2024/2025'")

            # إضافة البيانات الجديدة
            if progress_dialog:
                progress_dialog.setValue(65)
                progress_dialog.setLabelText("جاري إضافة البيانات الجديدة...")
                QApplication.processEvents()
            cursor.execute("""
            INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
            SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
            FROM "السجل الاولي"
            """)

            # تنظيف البيانات
            if progress_dialog:
                progress_dialog.setValue(70)
                progress_dialog.setLabelText("جاري تنظيف البيانات...")
                QApplication.processEvents()
            cursor.execute("""
            DELETE FROM "اللوائح"
            WHERE "الرمز" IS NULL OR TRIM("الرمز") = '' OR "الرمز" = 'الرمز'
            """)

            # تحديث مجموع التلاميذ فقط للسنة الدراسية التي يتم تحديثها
            if progress_dialog:
                progress_dialog.setValue(75)
                progress_dialog.setLabelText("جاري تحديث مجموع التلاميذ...")
                QApplication.processEvents()

            # التأكد من أن لدينا السنة الدراسية
            if academic_year:
                cursor.execute("""
                UPDATE "اللوائح" as l1
                SET "مجموع التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l2
                    WHERE l2."القسم" = l1."القسم"
                    AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                )
                WHERE l1."السنة_الدراسية" = ?
                """, (academic_year,))

                # طباعة رسالة توضيحية
                self.importer.log(f"تم تحديث مجموع التلاميذ للسنة الدراسية {academic_year} فقط", "info")
            else:
                # إذا لم تكن السنة الدراسية متوفرة، نستخدم الاستعلام القديم
                cursor.execute("""
                UPDATE "اللوائح" as l1
                SET "مجموع التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l2
                    WHERE l2."القسم" = l1."القسم"
                    AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                )
                """)

                # طباعة رسالة تحذير
                self.importer.log("تحذير: تم تحديث مجموع التلاميذ لجميع السنوات الدراسية لأن السنة الدراسية غير محددة", "warning")

            # تحديث السجل العام والبنية التربوية
            if progress_dialog:
                progress_dialog.setValue(80)
                progress_dialog.setLabelText("جاري تحديث السجل العام والبنية التربوية...")
                QApplication.processEvents()
            self.update_structure_tables(cursor, academic_year, progress_dialog)

        except Exception as e:
            raise Exception(f"حدث خطأ أثناء معالجة البيانات: {str(e)}")

    def update_school_info(self, cursor):
        """تحديث بيانات المؤسسة"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )
        """)

        # الصف الرابع لاستخراج الأكاديمية والجماعة والمؤسسة
        cursor.execute("""
            SELECT "Unnamed: 2", "Unnamed: 2", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 3
        """)
        academy_row = cursor.fetchone()

        # الصف الخامس لاستخراج المديرية والمؤسسة
        cursor.execute("""
            SELECT "Unnamed: 2", "Unnamed: 2", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 4
        """)
        directorate_row = cursor.fetchone()

        # الصف السادس لاستخراج السنة الدراسية والأسدس
        cursor.execute("""
            SELECT "Unnamed: 6", "Unnamed: 6"
            FROM "السجل الاولي"
            LIMIT 1 OFFSET 5
        """)
        year_row = cursor.fetchone()

        # التحقق من وجود سجل على الأقل
        cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
        if cursor.fetchone()[0] == 0:
            cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")

        # تحديث البيانات مع التحقق من القيم الفارغة والحفاظ على القيم الموجودة
        if academy_row and directorate_row and year_row:
            # الحصول على القيم الحالية للحقول التي نريد الحفاظ عليها
            cursor.execute("""
                SELECT البلدة, المدير, الحارس_العام, السلك, رقم_الحراسة, رقم_التسجيل, ImagePath1
                FROM بيانات_المؤسسة
                WHERE rowid=1
            """)
            current_values = cursor.fetchone()

            # تعيين القيم الافتراضية إذا لم تكن هناك قيم موجودة
            current_town = current_values[0] if current_values and current_values[0] else ''
            current_director = current_values[1] if current_values and current_values[1] else ''
            current_guard = current_values[2] if current_values and current_values[2] else ''
            current_cycle = current_values[3] if current_values and current_values[3] else ''
            current_guard_number = current_values[4] if current_values and current_values[4] else ''
            current_reg_number = current_values[5] if current_values and current_values[5] else ''
            current_image_path = current_values[6] if current_values and current_values[6] else ''

            # تحديث البيانات مع الحفاظ على القيم الموجودة
            cursor.execute("""
                UPDATE بيانات_المؤسسة
                SET الأكاديمية=?,
                    المديرية=?,
                    الجماعة=?,
                    المؤسسة=?,
                    السنة_الدراسية=?,
                    الأسدس=?,
                    البلدة=?,
                    المدير=?,
                    الحارس_العام=?,
                    السلك=?,
                    رقم_الحراسة=?,
                    رقم_التسجيل=?,
                    ImagePath1=?
                WHERE rowid=1
            """, (
                academy_row[0] or '',     # الأكاديمية
                directorate_row[0] or '', # المديرية
                academy_row[2] or '',     # الجماعة
                directorate_row[2] or '', # المؤسسة
                year_row[0] or '',        # السنة الدراسية
                'الأول',                  # الأسدس (قيمة افتراضية)
                current_town,             # البلدة (الحفاظ على القيمة الموجودة)
                current_director,         # المدير (الحفاظ على القيمة الموجودة)
                current_guard,            # الحارس العام (الحفاظ على القيمة الموجودة)
                current_cycle,            # السلك (الحفاظ على القيمة الموجودة)
                current_guard_number,     # رقم الحراسة (الحفاظ على القيمة الموجودة)
                current_reg_number,       # رقم التسجيل (الحفاظ على القيمة الموجودة)
                current_image_path        # ImagePath1 (الحفاظ على القيمة الموجودة)
            ))

    def update_structure_tables(self, cursor, academic_year, progress_dialog=None):
        """تحديث جداول البنية التربوية والسجل العام"""
        # إنشاء جدول السجل_العام
        if progress_dialog:
            progress_dialog.setValue(82)
            progress_dialog.setLabelText("جاري تحديث جدول السجل_العام...")
            QApplication.processEvents()
        cursor.execute("""
INSERT OR IGNORE INTO "السجل_العام"
    ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
SELECT "Unnamed: 1",
       "Unnamed: 2" || ' ' || "Unnamed: 3",
       "Unnamed: 4",
       "Unnamed: 5",
       "Unnamed: 6"
FROM "السجل الاولي"
WHERE "Unnamed: 1" IS NOT NULL
  AND TRIM("Unnamed: 1") <> ''
  AND "Unnamed: 1" <> 'الرمز'
""")

        # نقل البيانات من جدول "السجل الاولي" إلى جدول "السجل_العام"
        if progress_dialog:
            progress_dialog.setValue(84)
            progress_dialog.setLabelText("جاري تحديث بيانات السجل العام...")
            QApplication.processEvents()
        cursor.execute("""
UPDATE "السجل_العام"
SET
    "الاسم_والنسب" = (
        SELECT "Unnamed: 2" || ' ' || "Unnamed: 3"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
    ),
    "النوع" = (
        SELECT "Unnamed: 4"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
    ),
    "تاريخ_الازدياد" = (
        SELECT "Unnamed: 5"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
    ),
    "مكان_الازدياد" = (
        SELECT "Unnamed: 6"
        FROM "السجل الاولي"
        WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
    )
WHERE EXISTS (
    SELECT 1
    FROM "السجل الاولي"
    WHERE "Unnamed: 1" = "السجل_العام"."الرمز"
)
""")

        # إدراج أو تحديث بيانات "البنية_التربوية"
        if progress_dialog:
            progress_dialog.setValue(86)
            progress_dialog.setLabelText("جاري تحديث البنية التربوية...")
            QApplication.processEvents()
        cursor.execute("""
INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
FROM "اللوائح"
""")
        # إضافة وتحديث عمود "مجموع التلاميذ" في جدول "البنية_التربوية"
        try:
            cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "مجموع_التلاميذ" INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            pass  # العمود موجود بالفعل

        if progress_dialog:
            progress_dialog.setValue(88)
            progress_dialog.setLabelText("جاري تحديث مجموع التلاميذ في البنية التربوية...")
            QApplication.processEvents()

        # تحديث مجموع التلاميذ في البنية التربوية فقط للسنة الدراسية التي يتم تحديثها
        if academic_year:
            cursor.execute("""
            UPDATE "البنية_التربوية"
            SET "مجموع_التلاميذ" = (
                SELECT COUNT(*)
                FROM "اللوائح" AS l
                WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                  AND l."القسم" = "البنية_التربوية"."القسم"
                  AND l."المستوى" = "البنية_التربوية"."المستوى"
            )
            WHERE "السنة_الدراسية" = ?
            """, (academic_year,))

            # طباعة رسالة توضيحية
            if progress_dialog:
                progress_dialog.setLabelText(f"تم تحديث مجموع التلاميذ في البنية التربوية للسنة الدراسية {academic_year} فقط")
                QApplication.processEvents()
        else:
            # إذا لم تكن السنة الدراسية متوفرة، نستخدم الاستعلام القديم
            cursor.execute("""
            UPDATE "البنية_التربوية"
            SET "مجموع_التلاميذ" = (
                SELECT COUNT(*)
                FROM "اللوائح" AS l
                WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                  AND l."القسم" = "البنية_التربوية"."القسم"
                  AND l."المستوى" = "البنية_التربوية"."المستوى"
            )
            """)

            # طباعة رسالة تحذير
            if progress_dialog:
                progress_dialog.setLabelText("تحذير: تم تحديث مجموع التلاميذ في البنية التربوية لجميع السنوات الدراسية")
                QApplication.processEvents()

        # حذف الأقسام من البنية التربوية التي لا توجد في جدول اللوائح للسنة الدراسية نفسها
        if academic_year:
            if progress_dialog:
                progress_dialog.setValue(89)
                progress_dialog.setLabelText("جاري تنظيف البنية التربوية...")
                QApplication.processEvents()
            # تحديد السجلات التي سيتم حذفها
            cursor.execute("""
            DELETE FROM "البنية_التربوية"
            WHERE "السنة_الدراسية" = ?
            AND NOT EXISTS (
                SELECT 1
                FROM "اللوائح"
                WHERE "اللوائح"."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                AND "اللوائح"."القسم" = "البنية_التربوية"."القسم"
            )
            """, (academic_year,))

        # تحديث شريط التقدم بعد الانتهاء من تحديث البنية التربوية
        if progress_dialog:
            progress_dialog.setValue(89)
            progress_dialog.setLabelText("اكتمل تحديث البيانات!")
            QApplication.processEvents()

    def display_import_summary(self, academic_year):
        """عرض ملخص لنتائج الاستيراد"""
        conn = None
        try:
            # إظهار رسالة نجاح
            self.importer.log("✅ تم استيراد البيانات بنجاح!", "success")
            self.importer.log("جاري إعداد ملخص النتائج...", "progress")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 1. عرض السنة الدراسية
            if academic_year:
                self.importer.log(f"📅 السنة الدراسية: {academic_year}", "info")

            # 2. عرض أسماء الأقسام المستوردة
            cursor.execute("""
            SELECT DISTINCT القسم
            FROM اللوائح
            WHERE السنة_الدراسية = ?
            ORDER BY القسم
            """, (academic_year,))
            sections = [row[0] for row in cursor.fetchall()]

            if sections:
                self.importer.log(f"📚 الأقسام المستوردة للسنة الدراسية {academic_year}:", "info")
                section_text = ", ".join(sections)
                self.importer.log(f"   {section_text}", "info")

                # 3. مجموع الأقسام المستوردة
                self.importer.log(f"🔢 مجموع الأقسام المستوردة: {len(sections)}", "info")

            # 4. تم تصحيح استعلام مجموع التلاميذ - نعد كل سجل فردي كتلميذ
            cursor.execute("""
            SELECT COUNT(*)
            FROM اللوائح
            WHERE السنة_الدراسية = ?
            """, (academic_year,))
            total_students = cursor.fetchone()[0] or 0
            self.importer.log(f"👨‍👩‍👧‍👦 مجموع التلاميذ: {total_students}", "info")

            # 5. المقارنة بين الجداول
            cursor.execute("""
            SELECT COUNT(*) FROM "البنية_التربوية"
            WHERE "السنة_الدراسية" = ?
            AND NOT EXISTS (
                SELECT 1
                FROM "اللوائح"
                WHERE "اللوائح"."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                AND "اللوائح"."القسم" = "البنية_التربوية"."القسم"
            )
            """, (academic_year,))
            removed_sections = cursor.fetchone()[0] or 0

            if removed_sections > 0:
                self.importer.log(f"🧹 تم حذف {removed_sections} من الأقسام غير المتطابقة من جدول البنية_التربوية", "info")

            conn.close()
            conn = None

            # 6. تمني النجاح والتوفيق
            self.importer.log("نتمنى لك التوفيق والنجاح في عملك! 🌟", "success")

            # تحديث البيانات المعروضة في النوافذ الأخرى من البرنامج
            self.refresh_data_in_app()

        except Exception as e:
            self.importer.log(f"خطأ في عرض ملخص الاستيراد: {str(e)}", "error")
        finally:
            # التأكد من إغلاق الاتصال في جميع الحالات
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def refresh_data_in_app(self):
        """تحديث البيانات في جميع نوافذ التطبيق"""
        try:
            # إذا كانت النافذة جزءًا من تطبيق أكبر، نبحث عن الكائن الرئيسي
            if self.parent() and hasattr(self.parent(), 'refresh_all_tabs'):
                self.importer.log("جاري تحديث واجهة البرنامج...", "progress")
                # استدعاء دالة التحديث في النافذة الرئيسية إن وجدت
                self.parent().refresh_all_tabs()
                self.importer.log("تم تحديث البيانات المعروضة في البرنامج", "success")
            else:
                # البحث عن جميع النوافذ المفتوحة في التطبيق
                self.importer.log("جاري البحث عن النوافذ المفتوحة لتحديثها...", "progress")

                # البحث عن النافذة الرئيسية
                main_window = None
                for widget in QApplication.topLevelWidgets():
                    if widget.objectName() == "MainWindow" or hasattr(widget, 'refresh_all_tabs'):
                        main_window = widget
                        break

                if main_window and hasattr(main_window, 'refresh_all_tabs'):
                    main_window.refresh_all_tabs()
                    self.importer.log("تم تحديث البيانات في النافذة الرئيسية", "success")
                else:
                    # البحث عن نوافذ SimpleSearchWindow
                    updated = False
                    for widget in QApplication.topLevelWidgets():
                        if widget.__class__.__name__ == "SimpleSearchWindow":
                            if hasattr(widget, 'update_lists_model'):
                                widget.update_lists_model()
                                updated = True
                            if hasattr(widget, 'update_levels_model'):
                                widget.update_levels_model()
                                updated = True
                            if hasattr(widget, 'update_classes_model'):
                                widget.update_classes_model()
                                updated = True

                    if updated:
                        self.importer.log("تم تحديث البيانات في نوافذ البحث المفتوحة", "success")
                    else:
                        self.importer.log("ملاحظة: لم يتم العثور على واجهة تحديث. يمكنك استخدام زر التحديث بعد الانتهاء.", "info")
        except Exception as e:
            self.importer.log(f"ملاحظة: تم حفظ البيانات لكن قد تحتاج لاستخدام زر التحديث لرؤية التغييرات. الخطأ: {str(e)}", "warning")

    def show_not_implemented(self, button_text):
        """رسالة لإظهار أن الوظيفة غير منفذة بعد"""
        self.importer.log(f"تم النقر على زر: {button_text}", "info")
        self.importer.log("هذه الوظيفة غير متاحة حالياً", "warning")

        self.show_message_box("غير متاح", f"وظيفة '{button_text}' غير متاحة حالياً", QMessageBox.Information)

    def update_levels_order(self):
        """🔹 تحديث ترتيب_المستويات في قاعدة البيانات"""
        self.importer.log("جاري تحديث ترتيب المستويات...", "progress")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة عمود ترتيب_المستويات إذا لم يكن موجوداً
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            # تعيين قيمة افتراضية لجميع المستويات
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")

            # تحديث ترتيب المستويات حسب المرحلة التعليمية
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 1 WHERE المستوى LIKE '%جذع مشترك%' OR المستوى LIKE '%الجذع%'")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 2 WHERE المستوى LIKE '%الأولى بكالوريا%' OR المستوى LIKE '%سنة أولى بكالوريا%'")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 3 WHERE المستوى LIKE '%الثانية بكالوريا%' OR المستوى LIKE '%سنة ثانية بكالوريا%'")

            # تحديث ترتيب المستويات الأخرى
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 10 WHERE المستوى LIKE '%الأولى%' AND ترتيب_المستويات = 99")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 11 WHERE المستوى LIKE '%الثانية%' AND ترتيب_المستويات = 99")
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 12 WHERE المستوى LIKE '%الثالثة%' AND ترتيب_المستويات = 99")

            conn.commit()
            conn.close()

            self.importer.log("✅ تم تحديث ترتيب المستويات بنجاح!", "success")
        except Exception as e:
            self.importer.log(f"❌ خطأ في تحديث ترتيب المستويات: {str(e)}", "error")

    def assign_all_to_guard1(self):
        """🔹 تعيين جميع الأقسام للسنة الدراسية الحالية إلى حراسة رقم 1"""
        try:
            # الحصول على السنة الدراسية الحالية
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود عمود الأقسام_المسندة
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN الأقسام_المسندة TEXT DEFAULT ''")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            # الحصول على السنة الدراسية الحالية
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
            result = cursor.fetchone()

            if result and result[0]:
                current_year = result[0]
                self.importer.log(f"جاري تعيين جميع أقسام السنة الدراسية {current_year} إلى حراسة رقم 1...", "progress")

                # تعيين جميع الأقسام للسنة الدراسية الحالية إلى حراسة رقم 1
                cursor.execute("UPDATE البنية_التربوية SET الأقسام_المسندة = 'حراسة رقم 1' WHERE السنة_الدراسية = ?", (current_year,))
                updated_count = cursor.rowcount
                conn.commit()

                self.importer.log(f"✅ تم تعيين {updated_count} قسم إلى حراسة رقم 1 بنجاح!", "success")
            else:
                self.importer.log("⚠️ لم يتم العثور على سنة دراسية حالية.", "warning")

            conn.close()
        except Exception as e:
            self.importer.log(f"❌ خطأ في تعيين الأقسام لحراسة رقم 1: {str(e)}", "error")

    def update_sub11_window(self):
        """🔹 تحديث نافذة إدارة الأقسام والحراسة (sub11_window.py) بالبيانات الجديدة"""
        self.importer.log("جاري تحديث نافذة إدارة الأقسام والحراسة...", "progress")
        try:
            # البحث عن نافذة Sub11Window المفتوحة
            sub11_window = None
            for widget in QApplication.topLevelWidgets():
                if widget.__class__.__name__ == "Sub11Window":
                    sub11_window = widget
                    break

            if sub11_window:
                # تحديث البيانات في النافذة
                if hasattr(sub11_window, 'load_years'):
                    sub11_window.load_years()  # تحديث قائمة السنوات الدراسية

                if hasattr(sub11_window, 'load_levels'):
                    sub11_window.load_levels()  # تحديث جدول المستويات

                if hasattr(sub11_window, 'filter_assigned_sections'):
                    sub11_window.filter_assigned_sections()  # تحديث جدول الأقسام المسندة

                self.importer.log("✅ تم تحديث نافذة إدارة الأقسام والحراسة بنجاح!", "success")
            else:
                self.importer.log("ℹ️ نافذة إدارة الأقسام والحراسة غير مفتوحة حالياً.", "info")
        except Exception as e:
            self.importer.log(f"⚠️ تعذر تحديث نافذة إدارة الأقسام والحراسة: {str(e)}", "warning")

    def display_database_statistics(self):
        """عرض معلومات إحصائية عن البيانات المستوردة"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التأكد من وجود الجداول المطلوبة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('اللوائح', 'البنية_التربوية')")
            tables = [row[0] for row in cursor.fetchall()]

            if 'اللوائح' in tables:
                # استخراج السنة الدراسية
                cursor.execute("SELECT DISTINCT السنة الدراسية FROM اللوائح ORDER BY السنة الدراسية DESC LIMIT 1")
                result = cursor.fetchone()
                if result and result[0]:
                    self.importer.log(f"📅 السنة الدراسية: {result[0]}", "info")

                # استخراج أسماء الأقسام المستوردة
                cursor.execute("SELECT DISTINCT القسم FROM اللوائح ORDER BY القسم")
                sections = [row[0] for row in cursor.fetchall()]
                if sections:
                    self.importer.log(f"👨‍👩‍👧‍👦 الأقسام المستوردة ({len(sections)}): {', '.join(sections)}", "info")
                    self.importer.log(f"📊 مجموع الأقسام: {len(sections)}", "info")

                # حساب مجموع التلاميذ
                cursor.execute("SELECT SUM(مجموع التلاميذ) FROM اللوائح")
                students_count = cursor.fetchone()[0]
                if students_count:
                    self.importer.log(f"👥 مجموع التلاميذ: {students_count}", "info")
            else:
                # إذا لم يتم استيراد أي بيانات سابقًا
                self.importer.log("لم يتم استيراد أي بيانات حتى الآن.", "warning")

            conn.close()
            conn = None
        except Exception as e:
            self.importer.log(f"تعذر عرض المعلومات الإحصائية: {str(e)}", "error")
        finally:
            # التأكد من إغلاق الاتصال في جميع الحالات
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def import_teachers_from_excel(self):
        """استيراد أسماء الأساتذة والمواد المدرسة من ملف Excel"""
        # التحقق من توفر pandas
        if not PANDAS_AVAILABLE:
            self.show_pandas_not_installed()
            return

        # مسح مربع السجلات قبل البدء بالاستيراد
        self.log_text.clear()

        # إعادة عرض رسائل الترحيب
        self.importer.log("أهلاً بك في نافذة استيراد البيانات", "info")
        self.importer.log("جاري استيراد أسماء الأساتذة والمواد المدرسة...", "progress")

        conn = None
        try:
            # فتح اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول الأساتذة وحذف محتوياته
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الأساتذة'")
            if cursor.fetchone():
                # حذف كافة السجلات من الجدول
                self.importer.log("جاري حذف البيانات السابقة من جدول الأساتذة...", "progress")
                cursor.execute("DELETE FROM الأساتذة")
                self.importer.log("تم حذف البيانات السابقة بنجاح", "success")
            else:
                # إنشاء جدول الأساتذة إذا لم يكن موجوداً
                self.importer.log("جاري إنشاء جدول الأساتذة...", "progress")
                cursor.execute('''
                    CREATE TABLE الأساتذة (
                        رقم_الأستاذ INTEGER PRIMARY KEY AUTOINCREMENT,
                        اسم_الأستاذ TEXT,
                        المادة TEXT,
                        الرمز TEXT
                    )
                ''')
                self.importer.log("تم إنشاء جدول الأساتذة بنجاح", "success")

            # فتح حوار اختيار الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف Excel",
                "",
                "Excel Files (*.xlsx *.xls)"
            )

            if not file_path:
                self.importer.log("تم إلغاء عملية الاستيراد", "warning")
                if conn:
                    conn.close()
                return

            # التحقق من اسم الملف إذا كان يحتوي على "SeancesEnseignants" أو "Book"
            file_name = file_path.split('/')[-1].split('\\')[-1]
            valid_keywords = ["SeancesEnseignants", "Book"]
            is_valid_file = any(keyword in file_name for keyword in valid_keywords)

            if not is_valid_file:
                warning_message = f"الملف {file_name} لا يحتوي على العبارة 'SeancesEnseignants' أو 'Book' في اسمه. قد لا يكون هذا ملف أسماء الأساتذة صحيح."
                self.importer.log(warning_message, "warning")

                # عرض رسالة تأكيد للمستخدم بتصميم أنيق
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("تنبيه: ملف غير معروف")

                # إضافة أيقونة البرنامج إلى نافذة الرسالة
                icon_path = "01.ico"
                if os.path.exists(icon_path):
                    msg_box.setWindowIcon(QIcon(icon_path))

                # تعيين النص الرئيسي بخط Calibri 13 أزرق غامق
                msg_box.setText("<p style='font-family: Calibri; font-size: 13pt; color: #0D47A1; font-weight: bold;'>تنبيه: ملف غير معروف</p>")

                # إضافة نص توضيحي
                msg_box.setInformativeText(f"<p style='font-family: Calibri; font-size: 11pt;'>{warning_message}</p><p style='font-family: Calibri; font-size: 11pt;'>هل تريد الاستمرار في استيراد هذا الملف على أي حال؟</p>")

                # تعيين أيقونة التحذير
                msg_box.setIcon(QMessageBox.Warning)

                # إنشاء أزرار مخصصة
                continue_button = msg_box.addButton("نعم، استمر في الاستيراد", QMessageBox.YesRole)
                cancel_button = msg_box.addButton("لا، إلغاء الاستيراد", QMessageBox.NoRole)

                # تخصيص أنماط الأزرار
                continue_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: white;
                        background-color: #0D47A1;
                        border: none;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #1565C0;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)

                cancel_button.setStyleSheet("""
                    QPushButton {
                        font-family: Calibri;
                        font-size: 13pt;
                        font-weight: bold;
                        color: #0D47A1;
                        background-color: #E3F2FD;
                        border: 1px solid #0D47A1;
                        border-radius: 5px;
                        padding: 5px 15px;
                        min-width: 140px;
                    }
                    QPushButton:hover {
                        background-color: #BBDEFB;
                    }
                    QPushButton:pressed {
                        background-color: #E3F2FD;
                    }
                """)

                # تعيين الزر الافتراضي (زر الإلغاء)
                msg_box.setDefaultButton(cancel_button)

                # تعيين نمط مربع الحوار
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                    }
                    QLabel {
                        font-family: Calibri;
                        min-width: 300px;
                    }
                """)

                # عرض مربع الحوار وانتظار الرد
                msg_box.exec_()

                # معالجة الرد
                if msg_box.clickedButton() == cancel_button:
                    self.importer.log("تم إلغاء عملية الاستيراد بناءً على طلب المستخدم", "info")
                    if conn:
                        conn.close()
                    return
                else:
                    self.importer.log("تم اختيار الاستمرار في الاستيراد رغم التحذير", "info")

            # قراءة ملف Excel - تخطي الصف الأول إذا كان عناوين
            self.importer.log(f"جاري قراءة الملف: {file_path}", "progress")
            df = pd.read_excel(file_path, header=None)

            if df.empty:
                self.importer.log("الملف فارغ. لا توجد بيانات لاستيرادها.", "warning")
                if conn:
                    conn.close()
                return

            # استخدام الأعمدة المطلوبة (B=1, C=2, D=3)
            if df.shape[1] < 4:
                self.importer.log("تنسيق الملف غير صحيح. عدد الأعمدة غير كافية.", "error")
                if conn:
                    conn.close()
                return

            # تسمية الأعمدة حسب المتطلبات
            # B (index 1) -> المادة
            # C (index 2) -> الرمز
            # D (index 3) -> اسم_الأستاذ
            df_selected = df[[1, 2, 3]].copy()
            df_selected.columns = ['المادة', 'الرمز', 'اسم_الأستاذ']

            # ملء الفراغات في عمود المادة بالمادة التي قبلها
            self.importer.log("جاري ملء الفراغات في عمود المادة...", "progress")
            previous_subject = None

            # ملء الفراغات في عمود المادة
            for index, row in df_selected.iterrows():
                current_subject = row['المادة']

                # تحقق مما إذا كانت القيمة الحالية فارغة (NaN أو فارغة)
                if pd.isna(current_subject) or (isinstance(current_subject, str) and current_subject.strip() == ''):
                    if previous_subject is not None:
                        df_selected.at[index, 'المادة'] = previous_subject
                else:
                    # تحديث المادة السابقة
                    previous_subject = current_subject

            # حذف الصفوف التي تحتوي على قيم فارغة في اسم_الأستاذ
            df_selected = df_selected.dropna(subset=['اسم_الأستاذ'])

            # حذف الصف الذي يحتوي على "المجموع الاجمالي"
            df_selected = df_selected[~df_selected['اسم_الأستاذ'].str.contains('المجموع الاجمالي')]

            # تنظيف البيانات
            df_selected['المادة'] = df_selected['المادة'].astype(str)
            df_selected['الرمز'] = df_selected['الرمز'].fillna('').astype(str)
            df_selected['اسم_الأستاذ'] = df_selected['اسم_الأستاذ'].astype(str)

            # إدراج البيانات في قاعدة البيانات
            self.importer.log(f"جاري إدراج {len(df_selected)} سجل في قاعدة البيانات...", "progress")

            # حساب عدد السجلات التي تم إدراجها
            records_inserted = 0

            for _, row in df_selected.iterrows():
                # التحقق من أن جميع القيم المطلوبة موجودة
                if row['اسم_الأستاذ'].strip() and row['المادة'].strip():
                    try:
                        cursor.execute(
                            "INSERT INTO الأساتذة (اسم_الأستاذ, المادة, الرمز) VALUES (?, ?, ?)",
                            (row['اسم_الأستاذ'], row['المادة'], row['الرمز'])
                        )
                        records_inserted += 1
                    except Exception as e:
                        self.importer.log(f"خطأ في إدراج السجل: {e}", "error")

            # حفظ التغييرات وإغلاق الاتصال
            conn.commit()
            conn.close()
            conn = None

            # عرض ملخص العملية
            self.importer.log(f"تم الانتهاء من استيراد أسماء الأساتذة والمواد المدرسة", "success")
            self.importer.log(f"تم إدراج {records_inserted} سجل بنجاح", "success")

            # تحديث البيانات المعروضة في النوافذ الأخرى
            self.refresh_data_in_app()

            # عرض رسالة نجاح
            self.show_message_box(
                "تمت عملية الاستيراد بنجاح",
                f"تم استيراد {records_inserted} من أسماء الأساتذة والمواد بنجاح.",
                QMessageBox.Information
            )

        except Exception as e:
            self.importer.log(f"خطأ في استيراد البيانات: {str(e)}", "error")
            self.show_message_box(
                "خطأ في الاستيراد",
                f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}",
                QMessageBox.Critical
            )
        finally:
            # التأكد من إغلاق الاتصال في جميع الحالات
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def import_secret_codes(self):
        """استيراد الرمز السري وتحيينه دفعة واحدة من ملفات Excel متعددة"""
        # التحقق من توفر pandas
        if not PANDAS_AVAILABLE:
            self.show_pandas_not_installed()
            return

        # مسح مربع السجلات قبل البدء بالاستيراد
        self.log_text.clear()

        # إعادة عرض رسائل الترحيب
        self.importer.log("أهلاً بك في نافذة استيراد البيانات", "info")
        self.importer.log("جاري استيراد الرمز السري وتحيينه دفعة واحدة...", "progress")

        conn = None
        try:
            # فتح اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول الرمز_السري وحذف محتوياته
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الرمز_السري'")
            if cursor.fetchone():
                # حذف كافة السجلات من الجدول
                self.importer.log("جاري حذف البيانات السابقة من جدول الرمز_السري...", "progress")
                cursor.execute("DELETE FROM الرمز_السري")
                self.importer.log("تم حذف البيانات السابقة بنجاح", "success")
            else:
                # إنشاء جدول الرمز_السري إذا لم يكن موجوداً
                self.importer.log("جاري إنشاء جدول الرمز_السري...", "progress")
                cursor.execute('''
                    CREATE TABLE الرمز_السري (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        الرمز TEXT UNIQUE,
                        الرمز_السري TEXT
                    )
                ''')
                self.importer.log("تم إنشاء جدول الرمز_السري بنجاح", "success")

            # فتح حوار اختيار ملفات متعددة
            file_paths, _ = QFileDialog.getOpenFileNames(
                self,
                "اختر ملفات Excel (يمكن اختيار حتى 100 ملف)",
                "",
                "Excel Files (*.xlsx *.xls)"
            )

            if not file_paths:
                self.importer.log("تم إلغاء عملية الاستيراد", "warning")
                if conn:
                    conn.close()
                return

            # التحقق من عدد الملفات المختارة
            if len(file_paths) > 100:
                self.importer.log("تم اختيار أكثر من 100 ملف. سيتم استيراد أول 100 ملف فقط.", "warning")
                file_paths = file_paths[:100]

            self.importer.log(f"تم اختيار {len(file_paths)} ملف للاستيراد", "info")

            # إنشاء نافذة شريط التقدم
            progress_dialog = QProgressDialog("جاري استيراد الرمز السري وتحيينه...", None, 0, 100, self)
            progress_dialog.setWindowTitle("استيراد الرمز السري")

            # إضافة أيقونة البرنامج إلى نافذة شريط التقدم
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                progress_dialog.setWindowIcon(QIcon(icon_path))

            # تخصيص مظهر شريط التقدم
            progress_dialog.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                    font-family: Calibri;
                    font-size: 13pt;
                    font-weight: bold;
                    color: #0D47A1;
                }
                QProgressBar {
                    border: 2px solid #0D47A1;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #E3F2FD;
                    min-height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #0D47A1;
                    width: 10px;
                    margin: 0.5px;
                }
            """)

            # تعيين خصائص شريط التقدم
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setAutoClose(True)
            progress_dialog.setAutoReset(True)
            progress_dialog.setMinimumWidth(400)
            progress_dialog.setCancelButton(None)

            # عرض شريط التقدم
            progress_dialog.setValue(0)
            progress_dialog.show()
            QApplication.processEvents()

            # عداد إجمالي السجلات المضافة
            total_records_imported = 0

            # معالجة كل ملف على حدة
            for file_index, file_path in enumerate(file_paths):
                try:
                    file_name = file_path.split('/')[-1].split('\\')[-1]
                    progress_percent = int((file_index / len(file_paths)) * 90)
                    progress_dialog.setValue(progress_percent)
                    progress_dialog.setLabelText(f"جاري معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}")
                    QApplication.processEvents()

                    self.importer.log(f"جاري معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}", "progress")

                    # قراءة ملف Excel
                    df = pd.read_excel(file_path)

                    if df.empty:
                        self.importer.log(f"الملف {file_name} فارغ. تم تخطيه.", "warning")
                        continue

                    # البحث عن الأعمدة المطلوبة بأسماء مختلفة
                    column_mapping = self.identify_columns(df.columns)

                    if not column_mapping:
                        self.importer.log(f"الملف {file_name} لا يحتوي على الأعمدة المطلوبة. تم تخطيه.", "warning")
                        continue

                    # تنظيف وإعداد البيانات
                    records_data = self.prepare_records_data(df, column_mapping, file_name)

                    # إدراج البيانات في قاعدة البيانات
                    if records_data:
                        cursor.executemany("""
                            INSERT INTO الرمز_السري (الرمز, الرمز_السري)
                            VALUES (?, ?)
                        """, records_data)
                        conn.commit()

                        total_records_imported += len(records_data)
                        self.importer.log(f"✅ تم استيراد {len(records_data)} سجل من الملف {file_name}", "success")
                    else:
                        self.importer.log(f"❌ لا توجد بيانات صالحة في الملف {file_name}", "warning")

                except Exception as file_error:
                    self.importer.log(f"❌ خطأ في معالجة الملف {file_name}: {str(file_error)}", "error")

            # تحديث جدول السجل_العام بالرموز السرية المستوردة
            self.importer.log("جاري تحديث جدول السجل_العام بالرموز السرية الجديدة...", "progress")

            # التحقق من وجود عمود الرمز_السري في جدول السجل_العام
            try:
                cursor.execute("PRAGMA table_info(السجل_العام)")
                columns = [col[1] for col in cursor.fetchall()]

                if "الرمز_السري" not in columns:
                    # إضافة عمود الرمز_السري إذا لم يكن موجوداً
                    cursor.execute("ALTER TABLE السجل_العام ADD COLUMN الرمز_السري TEXT")
                    self.importer.log("تم إضافة عمود الرمز_السري إلى جدول السجل_العام", "info")

                # تحديث جدول السجل_العام بقيم الرمز السري من جدول الرمز_السري
                cursor.execute("""
                    UPDATE السجل_العام
                    SET الرمز_السري = (
                        SELECT الرمز_السري.الرمز_السري
                        FROM الرمز_السري
                        WHERE الرمز_السري.الرمز = السجل_العام.الرمز
                    )
                    WHERE EXISTS (
                        SELECT 1
                        FROM الرمز_السري
                        WHERE الرمز_السري.الرمز = السجل_العام.الرمز
                    )
                """)

                # عدد السجلات التي تم تحديثها
                updated_records = cursor.rowcount
                self.importer.log(f"تم تحديث {updated_records} سجل في جدول السجل_العام", "success")

            except sqlite3.OperationalError as e:
                self.importer.log(f"خطأ في تحديث جدول السجل_العام: {str(e)}", "error")

            # حفظ التغييرات وإغلاق الاتصال
            conn.commit()
            conn.close()
            conn = None

            # عرض ملخص العملية
            self.importer.log(f"تم استيراد وتحديث الرمز السري بنجاح", "success")
            self.importer.log(f"إجمالي السجلات المستوردة: {total_records_imported}", "success")

            # تحديث البيانات المعروضة في النوافذ الأخرى
            self.refresh_data_in_app()

            # عرض رسالة نجاح
            self.show_message_box(
                "تمت عملية الاستيراد بنجاح",
                f"تم استيراد {total_records_imported} رمز سري وتحديث السجل العام بنجاح.",
                QMessageBox.Information
            )

        except Exception as e:
            self.importer.log(f"خطأ في استيراد البيانات: {str(e)}", "error")
            self.show_message_box(
                "خطأ في الاستيراد",
                f"حدث خطأ أثناء استيراد البيانات:\n{str(e)}",
                QMessageBox.Critical
            )
        finally:
            # التأكد من إغلاق الاتصال في جميع الحالات
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def manual_refresh(self):
        """تحديث البيانات يدوياً عند الضغط على زر التحديث"""
        self.importer.log("جاري تحديث البيانات...", "progress")
        try:
            # إعادة تحميل البيانات محلياً أولاً
            self.reload_local_data()

            # إجبار إعادة رسم الواجهة
            self.repaint()
            QApplication.processEvents()

            # إذا كان هناك نافذة أب أو علامات تبويب داخل التطبيق فحاول تحديثها أيضاً
            parent = self.parent()
            if parent:
                if hasattr(parent, 'refresh_all_tabs'):
                    parent.refresh_all_tabs()
                if hasattr(parent, 'refresh_current_tab'):
                    parent.refresh_current_tab()
                if hasattr(parent, 'update_interface'):
                    parent.update_interface()
                parent.repaint()
                QApplication.processEvents()

            # تحديث جميع النوافذ المفتوحة
            updated_windows = 0
            for widget in QApplication.topLevelWidgets():
                # تحديث نوافذ SimpleSearchWindow
                if widget.__class__.__name__ == "SimpleSearchWindow":
                    if hasattr(widget, 'update_lists_model'):
                        widget.update_lists_model()
                        updated_windows += 1
                    if hasattr(widget, 'update_levels_model'):
                        widget.update_levels_model()
                    if hasattr(widget, 'update_classes_model'):
                        widget.update_classes_model()

                # تحديث النافذة الرئيسية
                if widget.objectName() == "MainWindow" or hasattr(widget, 'refresh_all_tabs'):
                    if hasattr(widget, 'refresh_all_tabs'):
                        widget.refresh_all_tabs()
                        updated_windows += 1

            if updated_windows > 0:
                self.importer.log(f"تم تحديث {updated_windows} نافذة مفتوحة", "success")

            self.importer.log("تم تحديث البيانات بنجاح ✨", "success")
            self.show_message_box(
                "تم التحديث",
                "تم تحديث البيانات بنجاح في جميع النوافذ",
                QMessageBox.Information
            )
        except Exception as e:
            self.importer.log(f"خطأ في تحديث البيانات: {str(e)}", "error")
            self.show_message_box(
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}",
                QMessageBox.Critical
            )

    def reload_local_data(self):
        """إعادة تحميل البيانات المحلية في النافذة الحالية"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # عرض معلومات عن البيانات الحالية
            self.importer.log("معلومات عن البيانات المحددة:", "info")

            # استخراج السنة الدراسية الحالية
            cursor.execute("""
                SELECT DISTINCT السنة_الدراسية
                FROM اللوائح
                ORDER BY السنة_الدراسية DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            current_year = result[0] if result else "غير محدد"

            # إحصائيات البيانات
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT القسم) as sections_count,
                    COUNT(*) as students_count,
                    COUNT(DISTINCT المستوى) as levels_count
                FROM اللوائح
                WHERE السنة_الدراسية = ?
            """, (current_year,))

            stats = cursor.fetchone()
            if stats:
                sections_count, students_count, levels_count = stats

                self.importer.log(f"📅 السنة الدراسية: {current_year}", "info")
                self.importer.log(f"📚 عدد الأقسام: {sections_count}", "info")
                self.importer.log(f"👨‍👩‍👧‍👦 مجموع التلاميذ: {students_count}", "info")
                self.importer.log(f"📊 عدد المستويات: {levels_count}", "info")

            # تحديث الأقسام المعروضة (إذا كانت موجودة)
            if hasattr(self, 'update_sections_display'):
                self.update_sections_display()

            conn.close()
            conn = None

            # إعادة رسم الواجهة لتحديث العرض
            self.update()
            self.repaint()
            QApplication.processEvents()

        except Exception as e:
            if conn:
                try:
                    conn.close()
                except:
                    pass
            raise Exception(f"خطأ في تحميل البيانات: {str(e)}")

    def show_help_guide(self):
        """عرض دليل تعليمات استخدام نافذة استيراد البيانات"""
        show_help_guide(self)
        self.importer.log("تم عرض دليل استخدام النافذة", "info")

    def show_pandas_not_installed(self):
        """عرض رسالة خطأ عند عدم توفر pandas"""
        self.importer.log("خطأ: مكتبة pandas غير متوفرة. هذه المكتبة ضرورية لاستيراد ملفات Excel.", "error")
        self.importer.log("لتثبيت pandas، استخدم أحد الخيارات التالية:", "info")
        self.importer.log("1. pip install pandas --only-binary=:all: (الموصى به)", "info")
        self.importer.log("2. pip install pandas-only-binary", "info")
        self.importer.log("3. تثبيت Anaconda الذي يتضمن pandas", "info")

        self.show_message_box(
            "خطأ: pandas غير متوفرة",
            "مكتبة pandas غير متوفرة.\n\nهذه المكتبة ضرورية لاستيراد ملفات Excel.\n\nللتثبيت استخدم الأمر:\npip install pandas --only-binary=:all:",
            QMessageBox.Critical
        )

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = Sub1Window()
    window.show()
    sys.exit(app.exec_())

