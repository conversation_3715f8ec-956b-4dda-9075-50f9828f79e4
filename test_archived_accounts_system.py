#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime
from archived_accounts_manager import ArchivedAccountsManager

def test_archived_accounts_system():
    """اختبار شامل لنظام الحسابات المرحلة"""
    print("🧪 اختبار شامل لنظام الحسابات المرحلة")
    print("=" * 80)
    
    # إنشاء مدير الحسابات المرحلة
    manager = ArchivedAccountsManager()
    
    # 1. اختبار إنشاء الجدول
    print("1️⃣ اختبار إنشاء جدول الحسابات المرحلة:")
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='الحسابات_المرحلة'
        """)
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ جدول الحسابات المرحلة موجود")
            
            # فحص هيكل الجدول
            cursor.execute("PRAGMA table_info(الحسابات_المرحلة)")
            columns = cursor.fetchall()
            print(f"   📋 عدد الأعمدة: {len(columns)}")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
        else:
            print("   ❌ جدول الحسابات المرحلة غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الجدول: {e}")
    
    # 2. اختبار البيانات المتاحة للترحيل
    print(f"\n2️⃣ فحص البيانات المتاحة للترحيل:")
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص البيانات في monthly_duties
        cursor.execute("""
            SELECT DISTINCT month, year, COUNT(*) as count
            FROM monthly_duties
            GROUP BY month, year
            ORDER BY year DESC, 
                CASE month
                    WHEN 'يناير' THEN 1
                    WHEN 'فبراير' THEN 2
                    WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4
                    WHEN 'مايو' THEN 5
                    WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7
                    WHEN 'أغسطس' THEN 8
                    WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10
                    WHEN 'نوفمبر' THEN 11
                    WHEN 'ديسمبر' THEN 12
                    ELSE 13
                END DESC
        """)
        
        available_months = cursor.fetchall()
        print(f"   📊 الشهور المتاحة للترحيل: {len(available_months)}")
        for month_data in available_months:
            print(f"      📅 {month_data[0]}/{month_data[1]} - {month_data[2]} سجل")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص البيانات: {e}")
    
    # 3. اختبار الشهور المرحلة حالياً
    print(f"\n3️⃣ فحص الشهور المرحلة حالياً:")
    try:
        archived_months = manager.get_archived_months()
        print(f"   📋 الشهور المرحلة: {len(archived_months)}")
        
        if archived_months:
            for month_data in archived_months:
                print(f"      📅 {month_data[0]}/{month_data[1]} - {month_data[2]} سجل")
                print(f"         🕒 أول ترحيل: {month_data[3]}")
                print(f"         🕒 آخر تحديث: {month_data[4]}")
        else:
            print("   📝 لا توجد شهور مرحلة بعد")
        
    except Exception as e:
        print(f"   ❌ خطأ في جلب الشهور المرحلة: {e}")
    
    # 4. اختبار ترحيل شهر (إذا توفرت بيانات)
    print(f"\n4️⃣ اختبار ترحيل شهر:")
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # البحث عن شهر للاختبار
        cursor.execute("""
            SELECT month, year, COUNT(*) as count
            FROM monthly_duties
            WHERE month IS NOT NULL AND year IS NOT NULL
            GROUP BY month, year
            LIMIT 1
        """)
        
        test_month_data = cursor.fetchone()
        conn.close()
        
        if test_month_data:
            test_month, test_year, record_count = test_month_data
            print(f"   🎯 اختبار ترحيل: {test_month}/{test_year} ({record_count} سجل)")
            
            # محاولة الترحيل
            success, message = manager.archive_monthly_accounts(test_month, test_year, force_update=True)
            
            if success:
                print(f"   ✅ نجح الترحيل: {message}")
                
                # فحص النتيجة
                archived_data = manager.get_archived_data_by_section_month("قسم / 01", test_month, test_year)
                print(f"   📊 بيانات مرحلة لقسم / 01: {len(archived_data)} سجل")
                
            else:
                print(f"   ❌ فشل الترحيل: {message}")
        else:
            print("   ⚠️ لا توجد بيانات للاختبار")
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الترحيل: {e}")
    
    # 5. اختبار مقارنة البيانات
    print(f"\n5️⃣ مقارنة البيانات الحية مقابل المرحلة:")
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب شهر مرحل للمقارنة
        archived_months = manager.get_archived_months()
        if archived_months:
            test_month, test_year = archived_months[0][0], archived_months[0][1]
            
            print(f"   🔍 مقارنة بيانات {test_month}/{test_year}:")
            
            # البيانات الحية
            cursor.execute("""
                SELECT COUNT(*), SUM(amount_paid)
                FROM monthly_duties
                WHERE month = ? AND year = ?
            """, (test_month, test_year))
            live_data = cursor.fetchone()
            
            # البيانات المرحلة
            cursor.execute("""
                SELECT COUNT(*), SUM(amount_paid)
                FROM الحسابات_المرحلة
                WHERE month = ? AND year = ?
            """, (test_month, test_year))
            archived_data = cursor.fetchone()
            
            print(f"      📊 البيانات الحية: {live_data[0]} سجل، مجموع: {live_data[1] or 0:.2f}")
            print(f"      📋 البيانات المرحلة: {archived_data[0]} سجل، مجموع: {archived_data[1] or 0:.2f}")
            
            if live_data[0] == archived_data[0] and abs((live_data[1] or 0) - (archived_data[1] or 0)) < 0.01:
                print("      ✅ البيانات متطابقة!")
            else:
                print("      ⚠️ هناك اختلاف في البيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في المقارنة: {e}")
    
    # 6. اختبار التقارير
    print(f"\n6️⃣ اختبار التقارير مع البيانات المرحلة:")
    try:
        # محاولة استيراد وظائف التقرير
        from print_section_monthly import get_monthly_duties_by_section_month
        
        archived_months = manager.get_archived_months()
        if archived_months:
            test_month, test_year = archived_months[0][0], archived_months[0][1]
            
            print(f"   📋 اختبار تقرير {test_month}/{test_year}:")
            
            # اختبار جلب البيانات للتقرير
            report_data = get_monthly_duties_by_section_month('data.db', 'قسم / 01', test_month, test_year)
            print(f"      📊 بيانات التقرير: {len(report_data)} سجل")
            
            if report_data:
                print("      ✅ التقرير يعمل مع البيانات المرحلة")
                # عرض عينة من البيانات
                sample = report_data[0]
                print(f"      📝 عينة: {sample[0]} - {sample[2]:.2f} درهم")
            else:
                print("      ⚠️ لا توجد بيانات للتقرير")
        
    except ImportError:
        print("   ⚠️ لا يمكن اختبار التقارير (ملف التقرير غير متاح)")
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التقارير: {e}")

def demonstrate_workflow():
    """عرض سير العمل المقترح"""
    print(f"\n💡 سير العمل المقترح لنظام الحسابات المرحلة")
    print("=" * 70)
    
    print("🔄 العملية الشهرية:")
    print("   1️⃣ في نهاية كل شهر: تشغيل عملية الترحيل")
    print("   2️⃣ النظام يأخذ لقطة من البيانات الحالية")
    print("   3️⃣ يحفظها في جدول الحسابات المرحلة")
    print("   4️⃣ التقارير تعتمد على البيانات المرحلة")
    
    print(f"\n✅ المزايا:")
    print("   📊 دقة مطلقة في التقارير")
    print("   🔒 استقرار البيانات المالية")
    print("   📈 إمكانية المقارنة بين الفترات")
    print("   🚀 أداء أفضل للتقارير")
    
    print(f"\n🛠️ الإدارة:")
    print("   🖥️ واجهة مخصصة لإدارة الترحيل")
    print("   🔄 إمكانية إعادة ترحيل الشهور")
    print("   🗑️ حذف الترحيلات الخاطئة")
    print("   📋 مراقبة حالة النظام")

if __name__ == "__main__":
    print("🏦 نظام الحسابات المرحلة - اختبار شامل")
    print("=" * 90)
    
    test_archived_accounts_system()
    demonstrate_workflow()
    
    print(f"\n🎯 النظام جاهز للاستخدام!")
    print("💡 لتشغيل واجهة الإدارة: python archived_accounts_window.py")
    print("📋 لترحيل شهر: manager.archive_monthly_accounts('يناير', 2024)")
    print("📊 التقارير ستعتمد تلقائياً على البيانات المرحلة")
