# -*- coding: utf-8 -*-
"""
وحدة دليل المساعدة - تحتوي على دالة عرض نافذة التعليمات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextBrowser, QFrame)
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt
import os

def show_help_guide(parent_window):
    """
    عرض نافذة دليل المساعدة
    
    المعلمات:
        parent_window: النافذة الأم التي سيتم عرض نافذة المساعدة فيها
    """
    # إنشاء نافذة حوار جديدة
    help_dialog = QDialog(parent_window)
    help_dialog.setWindowTitle("دليل استخدام البرنامج")
    help_dialog.setMinimumSize(900, 700)
    help_dialog.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق التصميم على النافذة
    help_dialog.setStyleSheet("""
        QDialog { background-color: white; }
        QTextBrowser {
            border: 1px solid #E0E0E0;
            border-radius: 5px;
            padding: 10px;
            font-family: Calibri;
            font-size: 14pt;
            line-height: 1.5;
        }
    """)
    
    # إضافة أيقونة البرنامج
    try:
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            help_dialog.setWindowIcon(QIcon(icon_path))
    except Exception as e:
        print(f"خطأ في تحميل أيقونة البرنامج: {e}")
    
    # إنشاء التخطيط الرئيسي
    main_layout = QVBoxLayout(help_dialog)
    
    # إنشاء إطار للعنوان
    title_frame = QFrame()
    title_frame.setStyleSheet("""
        QFrame {
            background-color: #f5f5f5;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
    """)
    title_layout = QVBoxLayout(title_frame)
    
    # إضافة تخطيط أفقي للعنوان والشعار
    header_layout = QHBoxLayout()
    
    # محاولة إضافة شعار البرنامج
    try:
        logo_label = QLabel()
        logo_pixmap = QPixmap("01.ico")
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            header_layout.addWidget(logo_label, 0)
    except Exception as e:
        print(f"خطأ في تحميل شعار البرنامج: {e}")
    
    # إضافة عنوان النافذة
    title_label = QLabel("دليل استخدام البرنامج")
    title_label.setFont(QFont("Calibri", 20, QFont.Bold))
    title_label.setStyleSheet("color: #0066cc; font-weight: bold;")
    title_label.setAlignment(Qt.AlignCenter)
    header_layout.addWidget(title_label, 1)
    
    title_layout.addLayout(header_layout)
    main_layout.addWidget(title_frame)
    
    # إنشاء متصفح النص
    text_browser = QTextBrowser()
    text_browser.setOpenExternalLinks(True)
    
    # محتوى التعليمات
    help_text = """
    <h2 style="color: #1976d2; text-align: center;">دليل استخدام البرنامج</h2>
    
    <h3 style="color: #2196f3;">نظرة عامة</h3>
    <p>هذا البرنامج يساعدك على إدارة بيانات المؤسسة التعليمية بطريقة منظمة وسهلة. يتكون البرنامج من عدة نوافذ وتبويبات تساعدك على تنظيم وإدارة مختلف جوانب العمل.</p>
    
    <h3 style="color: #2196f3;">النافذة الرئيسية</h3>
    <p>تحتوي النافذة الرئيسية على التبويبات التالية:</p>
    <ul>
        <li><b>استيراد البيانات:</b> لاستيراد البيانات من مصادر خارجية وإدخالها في قاعدة البيانات.</li>
        <li><b>بيانات المؤسسة:</b> لإدارة معلومات المؤسسة الأساسية.</li>
        <li><b>إحصائيات:</b> لعرض إحصائيات وتقارير عن البيانات المخزنة.</li>
        <li><b>الإعدادات الافتراضية:</b> لضبط إعدادات البرنامج الأساسية.</li>
        <li><b>عناوين الأوراق والملاحظات:</b> لتخصيص عناوين الأوراق والملاحظات المطبوعة.</li>
        <li><b>إعدادات الطابعة:</b> لضبط إعدادات الطباعة.</li>
    </ul>
    
    <h3 style="color: #2196f3;">استيراد البيانات</h3>
    <p>تتيح لك هذه النافذة استيراد البيانات من ملفات خارجية وإدخالها في قاعدة البيانات. يمكنك:</p>
    <ul>
        <li>استيراد بيانات التلاميذ من ملفات Excel.</li>
        <li>استيراد بيانات الأساتذة والموظفين.</li>
        <li>استيراد بيانات المواد والأقسام.</li>
        <li>عرض إحصائيات عن البيانات المستوردة.</li>
    </ul>
    
    <h3 style="color: #2196f3;">بيانات المؤسسة</h3>
    <p>تتيح لك هذه النافذة إدارة معلومات المؤسسة الأساسية مثل:</p>
    <ul>
        <li>اسم المؤسسة وعنوانها.</li>
        <li>معلومات الاتصال.</li>
        <li>الشعار والختم.</li>
        <li>معلومات المدير والإدارة.</li>
    </ul>
    
    <h3 style="color: #2196f3;">إحصائيات</h3>
    <p>تعرض هذه النافذة إحصائيات وتقارير عن البيانات المخزنة في قاعدة البيانات، مثل:</p>
    <ul>
        <li>عدد التلاميذ حسب المستوى والقسم.</li>
        <li>إحصائيات الغياب والتأخر.</li>
        <li>إحصائيات المخالفات.</li>
        <li>إمكانية طباعة التقارير الإحصائية.</li>
    </ul>
    
    <h3 style="color: #2196f3;">الإعدادات الافتراضية</h3>
    <p>تتيح لك هذه النافذة ضبط إعدادات البرنامج الأساسية، مثل:</p>
    <ul>
        <li>السنة الدراسية الحالية.</li>
        <li>الأسدس الحالي.</li>
        <li>عتبات الغياب والتأخر.</li>
        <li>إعدادات النظام الأخرى.</li>
    </ul>
    
    <h3 style="color: #2196f3;">عناوين الأوراق والملاحظات</h3>
    <p>تتيح لك هذه النافذة تخصيص عناوين الأوراق والملاحظات المطبوعة، مثل:</p>
    <ul>
        <li>عناوين الشهادات والإشعارات.</li>
        <li>نصوص الملاحظات القياسية.</li>
        <li>تخصيص محتوى الأوراق المطبوعة.</li>
    </ul>
    
    <h3 style="color: #2196f3;">إعدادات الطابعة</h3>
    <p>تتيح لك هذه النافذة ضبط إعدادات الطباعة، مثل:</p>
    <ul>
        <li>اختيار الطابعة الافتراضية.</li>
        <li>ضبط هوامش الطباعة.</li>
        <li>تخصيص إعدادات الطباعة المختلفة.</li>
    </ul>
    
    <h3 style="color: #2196f3;">ملاحظات هامة</h3>
    <ul>
        <li>تأكد من حفظ التغييرات قبل الانتقال بين التبويبات.</li>
        <li>يمكنك استخدام زر "تحديث" في كل نافذة لتحديث البيانات المعروضة.</li>
        <li>تأكد من وجود نسخة احتياطية من قاعدة البيانات قبل إجراء تغييرات كبيرة.</li>
        <li>للحصول على مساعدة إضافية، يمكنك الضغط على زر المساعدة في كل نافذة.</li>
    </ul>
    """
    
    text_browser.setHtml(help_text)
    main_layout.addWidget(text_browser)
    
    # إضافة أزرار في الأسفل
    buttons_layout = QHBoxLayout()
    buttons_layout.addStretch()
    
    # زر إغلاق
    close_btn = QPushButton("إغلاق")
    close_btn.setFixedHeight(35)
    close_btn.setMinimumWidth(120)
    close_btn.clicked.connect(help_dialog.accept)
    close_btn.setStyleSheet("""
        QPushButton { 
            background-color: #1976d2; 
            color: white; 
            border: none; 
            border-radius: 4px;
            padding: 0 15px; 
            font-family: Calibri; 
            font-size: 13pt; 
            font-weight: bold; 
        }
        QPushButton:hover { 
            background-color: #1565c0; 
        }
    """)
    
    buttons_layout.addWidget(close_btn)
    main_layout.addLayout(buttons_layout)
    
    # عرض النافذة
    help_dialog.exec_()
