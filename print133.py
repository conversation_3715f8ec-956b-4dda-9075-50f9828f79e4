import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات خاصة بتقارير الغياب =================
# الجدول الأول: صفان، 4 أعمدة
COL_WIDTHS_TABLE1 = [70, 30, 60, 35]
# الجدول الثاني: جدول المترشحين مع البيانات المالية من جدولين
COL_WIDTHS_TABLE2 = [15, 45, 25, 40, 20, 20, 20, 10]  # تعديل عرض الأعمدة للبيانات الجديدة
# عناوين الجدول المحدثة مع البيانات المالية
TABLE2_HEADERS = ['ID', 'اسم التلميذ', 'رمز التلميذ', 'المؤسسة الأصلية', 'المطلوب', 'المدفوع', 'المتبقي', 'ر.ت']

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 8  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_TABLE2 = 6   # ارتفاع صفوف الجدول الثاني (جدول المترشحين الغائبين)
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول

# إعدادات الهوامش
PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT = 380, 170
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_subject_info_from_db(db_path, subject_name, section_name=None):
    """
    جلب معلومات المادة والقسم من جدول_المواد_والاقسام
    
    المعاملات:
        db_path: مسار قاعدة البيانات
        subject_name: اسم المادة
        section_name: اسم القسم (اختياري)
    
    العوائد:
        قاموس يحتوي على: القسم، المادة، اسم_الاستاذ، نسبة_الواجبات
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # بناء الاستعلام
        query = """
            SELECT القسم, المادة, اسم_الاستاذ, نسبة_الواجبات
            FROM جدول_المواد_والاقسام
            WHERE المادة = ?
        """
        params = [subject_name]
        
        # إضافة شرط القسم إذا تم تحديده
        if section_name:
            query += " AND القسم = ?"
            params.append(section_name)
        
        # ترتيب النتائج وأخذ أول نتيجة
        query += " ORDER BY id LIMIT 1"
        
        cursor.execute(query, params)
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'القسم': result[0] or '',
                'المادة': result[1] or '',
                'اسم_الاستاذ': result[2] or '',
                'نسبة_الواجبات': result[3] or ''
            }
        else:
            # إذا لم تُوجد النتيجة، إرجاع بيانات افتراضية
            return {
                'القسم': section_name or '',
                'المادة': subject_name or '',
                'اسم_الاستاذ': '',
                'نسبة_الواجبات': ''
            }
            
    except Exception as e:
        print(f"خطأ في جلب معلومات المادة من قاعدة البيانات: {e}")
        # في حالة الخطأ، إرجاع بيانات افتراضية
        return {
            'القسم': section_name or '',
            'المادة': subject_name or '',
            'اسم_الاستاذ': '',
            'نسبة_الواجبات': ''
        }

def generate_absence_report(logo_path, records, output_path, absence_data):
    """إنشاء تقرير الغياب"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # ترتيب جميع السجلات حسب ID
    sorted_records = sorted(records, key=lambda x: int(x.get('id', '0')) if str(x.get('id', '0')).isdigit() else 999999)

    # تحديد ارتفاع صفوف جدول المترشحين بناءً على عدد الصفوف
    global ROW_HEIGHT_TABLE2
    total_records = len(sorted_records)

    # تعديل ارتفاع الصفوف بناءً على عدد المترشحين الغائبين
    if total_records <= 15:
        ROW_HEIGHT_TABLE2 = 9
    elif 15 < total_records <= 20:
        ROW_HEIGHT_TABLE2 = 8
    elif 20 < total_records <= 25:
        ROW_HEIGHT_TABLE2 = 7
    else:
        ROW_HEIGHT_TABLE2 = 6

    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 5

    # عنوان التقرير - محضر غياب المترشحين
    pdf.set_draw_color(220, 20, 60)  # لون أحمر للغياب
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    x = margin
    # عنوان تقرير الغياب المحدث
    title_text = f"محضر غياب المترشحين - المادة : {absence_data.get('subject', 'المادة')} - {absence_data.get('period', 'الفترة')}"
    
    pdf.set_text_color(220, 20, 60)  # لون أحمر للنص

    # مربع العنوان يمتد على كامل العرض
    pdf.set_xy(x, y)
    pdf.set_font('Arial','B', 14)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')
    pdf.set_text_color(0, 0, 0)  # إعادة اللون للأسود
    y += FIXED_BOX_HEIGHT + 5

    # جلب معلومات المادة والقسم من جدول_المواد_والاقسام
    db_path = os.path.join(os.path.dirname(__file__), 'data.db')
    subject_info = get_subject_info_from_db(
        db_path, 
        absence_data.get('subject', ''),
        absence_data.get('section', '')
    )

    # الجدول الأول: معلومات الغياب من جدول_المواد_والاقسام
    cols1 = COL_WIDTHS_TABLE1
    
    # صفوف معلومات الغياب المحدثة
    # الصف الأول: القسم (العمود الأول) + المادة (العمود الثالث)
    row1 = [
        subject_info.get('القسم', ''),           # العمود الأول - القسم
        'القسم',                                  # العمود الثاني - تسمية
        subject_info.get('المادة', ''),           # العمود الثالث - المادة  
        'المادة'                                  # العمود الرابع - تسمية
    ]
    
    # الصف الثاني: نسبة_الواجبات (العمود الأول) + اسم_الاستاذ (العمود الثالث)
    row2 = [
        subject_info.get('نسبة_الواجبات', ''),    # العمود الأول - نسبة الواجبات
        'نسبة الواجبات',                          # العمود الثاني - تسمية
        subject_info.get('اسم_الاستاذ', ''),      # العمود الثالث - اسم الأستاذ
        'اسم الأستاذ'                             # العمود الرابع - تسمية
    ]
    
    pdf.set_font('Arial','B',12)
    pdf.set_fill_color(255, 230, 230)  # خلفية وردية فاتحة
    
    # رسم الصف الأول
    x = margin
    for i, cell in enumerate(row1):
        pdf.set_xy(x, y)
        fill = i % 2 == 1
        align = 'C' if i % 2 == 1 else 'R'
        pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
        x += cols1[i]
        
    # رسم الصف الثاني
    y += ROW_HEIGHT_TABLE1
    x = margin
    for i, cell in enumerate(row2):
        pdf.set_xy(x, y)
        fill = i % 2 == 1
        align = 'C' if i % 2 == 1 else 'R'
        pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
        x += cols1[i]
        
    y += ROW_HEIGHT_TABLE1 + 5

    # الجدول الثاني: قائمة المترشحين الغائبين مع البيانات المالية
    cols2 = COL_WIDTHS_TABLE2
    
    pdf.set_font('Arial','B',10)  # تصغير الخط لاستيعاب البيانات الإضافية
    pdf.set_fill_color(255, 200, 200)  # خلفية حمراء فاتحة للرأس

    # رسم رأس الجدول
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_TABLE_HEADER
    pdf.set_font('Arial','',9)  # خط أصغر للمحتوى

    # محتوى الجدول - جميع المترشحين الغائبين مرتبين حسب ID مع البيانات المالية
    for i, rec in enumerate(sorted_records):
        x = margin

        # بيانات المترشح الغائب من جدول_البيانات مع البيانات المالية
        data = [
            rec.get('id',''),  # ID
            rec.get('اسم_التلميذ',''), # اسم التلميذ
            rec.get('رمز_التلميذ',''), # رمز التلميذ
            rec.get('المؤسسة_الاصلية',''),  # المؤسسة الأصلية
            f"{rec.get('amount_required', 0):.2f}",  # المبلغ المطلوب
            f"{rec.get('amount_paid', 0):.2f}",      # المبلغ المدفوع
            f"{rec.get('amount_remaining', 0):.2f}", # المبلغ المتبقي
            str(i+1)  # الرقم التسلسلي
        ]

        # تلوين صفوف الغائبين بلون وردي فاتح
        pdf.set_fill_color(255, 245, 245)

        # رسم خلايا الصف
        for j, cell in enumerate(data):
            pdf.set_xy(x, y)
            # تلوين عمود ID بلون مميز
            if j == 0:  # عمود ID
                pdf.set_fill_color(255, 235, 235)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                pdf.set_fill_color(255, 245, 245)
            # تلوين الأعمدة المالية بألوان مختلفة
            elif j in [4, 5, 6]:  # أعمدة المبالغ المالية
                if j == 4:  # المطلوب - لون أزرق فاتح
                    pdf.set_fill_color(230, 245, 255)
                elif j == 5:  # المدفوع - لون أخضر فاتح
                    pdf.set_fill_color(230, 255, 230)
                else:  # المتبقي - لون أحمر فاتح
                    pdf.set_fill_color(255, 230, 230)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                pdf.set_fill_color(255, 245, 245)
            else:
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols2[j]

        y += ROW_HEIGHT_TABLE2

        # الانتقال إلى صفحة جديدة عند الحاجة
        if y > pdf.h - 50:  # ترك مساحة أكبر للتوقيع
            pdf.add_page()
            y = pdf.get_y()

    # إضافة ملخص المبالغ المالية
    y += 10
    pdf.set_font('Arial','B',12)
    
    # حساب الإجماليات
    total_required = sum(rec.get('amount_required', 0) for rec in sorted_records)
    total_paid = sum(rec.get('amount_paid', 0) for rec in sorted_records)
    total_remaining = sum(rec.get('amount_remaining', 0) for rec in sorted_records)
    
    # مربع الإجماليات
    summary_width = usable_w / 3
    x = margin
    
    pdf.set_fill_color(230, 245, 255)  # خلفية زرقاء فاتحة
    pdf.set_xy(x, y)
    pdf.cell(summary_width, 12, pdf.ar_text(f"إجمالي المطلوب: {total_required:.2f} درهم"), border=1, align='C', fill=True)
    
    x += summary_width
    pdf.set_fill_color(230, 255, 230)  # خلفية خضراء فاتحة
    pdf.set_xy(x, y)
    pdf.cell(summary_width, 12, pdf.ar_text(f"إجمالي المدفوع: {total_paid:.2f} درهم"), border=1, align='C', fill=True)
    
    x += summary_width
    pdf.set_fill_color(255, 230, 230)  # خلفية حمراء فاتحة
    pdf.set_xy(x, y)
    pdf.cell(summary_width, 12, pdf.ar_text(f"إجمالي المتبقي: {total_remaining:.2f} درهم"), border=1, align='C', fill=True)
    
    y += 25

    # إضافة ملاحظات الغياب في النهاية
    if absence_data.get('notes'):
        pdf.set_font('Arial','B',10)
        pdf.set_xy(margin, y)
        pdf.cell(0, 8, pdf.ar_text(f"ملاحظات: {absence_data['notes']}"), border=1, align='R')
        y += 15

    # إضافة خاتم وتوقيع رئيس المركز في النهاية
    y += 20
    signature_width = usable_w / 2  # تقسيم إلى نصفين
    pdf.set_font('Arial','B',12)
    
    # إضافة مساحة للخاتم والتوقيع في المنتصف
    x = margin + usable_w / 4  # وضع في المنتصف
    
    # مربع الخاتم والتوقيع
    pdf.set_xy(x, y)
    pdf.cell(signature_width, 20, '', border=1, align='C')
    
    # النص أسفل المربع
    pdf.set_xy(x, y + 22)
    pdf.cell(signature_width, 8, pdf.ar_text('خاتم وتوقيع السيد(ة) رئيس(ة) المركز'), border=0, align='C')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء تقرير الغياب: {output_path}")

def print_absence_report_single(parent=None, selected_records=None, absence_data=None):
    """
    دالة لإنشاء تقرير الغياب من النافذة المجمعة
    
    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        selected_records: قائمة السجلات المحددة للغياب
        absence_data: بيانات الغياب (التاريخ، المادة، الفترة، إلخ)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not selected_records:
            return False, None, "لا توجد سجلات محددة للغياب."

        if not absence_data:
            return False, None, "بيانات الغياب غير متوفرة."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        
        # جلب بيانات كاملة للمترشحين من قاعدة البيانات مع البيانات المالية
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # جلب شعار المؤسسة
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            
            # بناء البيانات الكاملة للمترشحين من جدول_البيانات مع البيانات المالية
            complete_records = []
            for record in selected_records:
                # يمكن أن يحتوي record على id أو اسم_التلميذ أو رقم_الامتحان
                student_id = record.get('id', '')
                student_name = record.get('اسم_التلميذ', '') or record.get('الاسم_الكامل', '')
                exam_number = record.get('رقم_الامتحان', '')
                
                # الاستعلام المحدث للربط بين الجدولين
                query = """
                    SELECT 
                        j.id, 
                        j.اسم_التلميذ, 
                        j.رمز_التلميذ, 
                        j.المؤسسة_الاصلية,
                        COALESCE(m.amount_required, 0) as amount_required,
                        COALESCE(m.amount_paid, 0) as amount_paid,
                        COALESCE(m.amount_remaining, 0) as amount_remaining
                    FROM جدول_البيانات j
                    LEFT JOIN monthly_duties m ON j.id = m.student_id
                    WHERE 1=1
                """
                params = []
                
                # تحديد شرط البحث حسب البيانات المتوفرة
                if student_id:
                    query += " AND j.id = ?"
                    params.append(student_id)
                elif student_name:
                    query += " AND j.اسم_التلميذ LIKE ?"
                    params.append(f"%{student_name}%")
                elif exam_number:
                    query += " AND j.رمز_التلميذ LIKE ?"
                    params.append(f"%{exam_number}%")
                else:                    # إذا لم تكن هناك بيانات كافية، إنشاء سجل افتراضي
                    complete_records.append({
                        'id': 'غير محدد',
                        'اسم_التلميذ': student_name or 'غير محدد',
                        'رمز_التلميذ': exam_number or 'غير محدد',
                        'المؤسسة_الاصلية': 'غير محدد',
                        'amount_required': 0,
                        'amount_paid': 0,
                        'amount_remaining': 0
                    })
                    continue
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                if results:
                    # إضافة جميع النتائج المطابقة مع البيانات المالية
                    for result in results:
                        complete_records.append({
                            'id': str(result[0]) if result[0] else 'غير محدد',
                            'اسم_التلميذ': result[1] or 'غير محدد',
                            'رمز_التلميذ': result[2] or 'غير محدد',
                            'المؤسسة_الاصلية': result[3] or 'غير محدد',
                            'amount_required': float(result[4]) if result[4] else 0,
                            'amount_paid': float(result[5]) if result[5] else 0,
                            'amount_remaining': float(result[6]) if result[6] else 0
                        })
                else:
                    # إذا لم يتم العثور على البيانات، استخدم البيانات المتوفرة مع قيم افتراضية للمبالغ
                    complete_records.append({
                        'id': str(student_id) if student_id else 'غير محدد',
                        'اسم_التلميذ': student_name or 'غير محدد',
                        'رمز_التلميذ': exam_number or 'غير محدد',
                        'المؤسسة_الاصلية': 'غير محدد',
                        'amount_required': 0,
                        'amount_paid': 0,
                        'amount_remaining': 0
                    })
            
            conn.close()
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            # في حالة فشل الوصول لقاعدة البيانات، استخدم البيانات المتوفرة
            complete_records = []
            for i, record in enumerate(selected_records):
                complete_records.append({
                    'id': str(record.get('id', i+1)),
                    'اسم_التلميذ': record.get('اسم_التلميذ', '') or record.get('الاسم_الكامل', 'غير محدد'),
                    'رمز_التلميذ': record.get('رمز_التلميذ', '') or record.get('رقم_الامتحان', 'غير محدد'),
                    'المؤسسة_الاصلية': record.get('المؤسسة_الاصلية', 'غير محدد'),
                    'amount_required': 0,
                    'amount_paid': 0,
                    'amount_remaining': 0
                })
            logo_path = None

        # إنشاء مجلد تقارير الغياب إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب المجمعة')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = absence_data.get('subject', 'مادة').replace(' ', '_').replace('/', '_')
        date_str = absence_data.get('date', '').replace('-', '_')
        output_path = os.path.join(reports_dir, f"تقرير_غياب_مجمع_{subject_name}_{date_str}_{timestamp}.pdf")

        # إنشاء تقرير الغياب
        generate_absence_report(logo_path, complete_records, output_path, absence_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء تقرير الغياب ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير الغياب المجمع بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير الغياب المجمع: {str(e)}"

def print_absence_report(parent=None, selected_records=None, absence_data=None):
    """
    دالة لإنشاء تقرير الغياب، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        selected_records: قائمة السجلات المحددة للغياب
        absence_data: بيانات الغياب (التاريخ، المادة، الفترة، إلخ)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not selected_records:
            return False, None, "لا توجد سجلات محددة للغياب."

        if not absence_data:
            return False, None, "بيانات الغياب غير متوفرة."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        
        # جلب بيانات كاملة للمترشحين من قاعدة البيانات مع البيانات المالية
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # جلب شعار المؤسسة
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            
            # بناء البيانات الكاملة للمترشحين من جدول_البيانات مع البيانات المالية
            complete_records = []
            for record in selected_records:
                # يمكن أن يحتوي record على id أو اسم_التلميذ أو رقم_الامتحان
                student_id = record.get('id', '')
                student_name = record.get('اسم_التلميذ', '') or record.get('الاسم_الكامل', '')
                student_code = record.get('رمز_التلميذ', '') or record.get('رقم_الامتحان', '')
                
                # الاستعلام المحدث للربط بين الجدولين
                query = """
                    SELECT 
                        j.id, 
                        j.اسم_التلميذ, 
                        j.رمز_التلميذ, 
                        j.المؤسسة_الاصلية,
                        COALESCE(m.amount_required, 0) as amount_required,
                        COALESCE(m.amount_paid, 0) as amount_paid,
                        COALESCE(m.amount_remaining, 0) as amount_remaining
                    FROM جدول_البيانات j
                    LEFT JOIN monthly_duties m ON j.id = m.student_id
                    WHERE 1=1
                """
                params = []
                
                # تحديد شرط البحث حسب البيانات المتوفرة
                if student_id:
                    query += " AND j.id = ?"
                    params.append(student_id)
                elif student_name:
                    query += " AND j.اسم_التلميذ LIKE ?"
                    params.append(f"%{student_name}%")
                elif student_code:
                    query += " AND j.رمز_التلميذ LIKE ?"
                    params.append(f"%{student_code}%")
                else:
                    # إذا لم تكن هناك بيانات كافية، إنشاء سجل افتراضي
                    complete_records.append({
                        'id': 'غير محدد',
                        'اسم_التلميذ': student_name or 'غير محدد',
                        'رمز_التلميذ': student_code or 'غير محدد',
                        'المؤسسة_الاصلية': 'غير محدد',
                        'amount_required': 0,
                        'amount_paid': 0,
                        'amount_remaining': 0
                    })
                    continue
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                if results:
                    # إضافة جميع النتائج المطابقة مع البيانات المالية
                    for result in results:
                        complete_records.append({
                            'id': str(result[0]) if result[0] else 'غير محدد',
                            'اسم_التلميذ': result[1] or 'غير محدد',
                            'رمز_التلميذ': result[2] or 'غير محدد',
                            'المؤسسة_الاصلية': result[3] or 'غير محدد',
                            'amount_required': float(result[4]) if result[4] else 0,
                            'amount_paid': float(result[5]) if result[5] else 0,
                            'amount_remaining': float(result[6]) if result[6] else 0
                        })
                else:
                    # إذا لم يتم العثور على البيانات، استخدم البيانات المتوفرة مع قيم افتراضية للمبالغ
                    complete_records.append({
                        'id': str(student_id) if student_id else 'غير محدد',
                        'اسم_التلميذ': student_name or 'غير محدد',
                        'رمز_التلميذ': exam_number or 'غير محدد',
                        'المؤسسة_الاصلية': 'غير محدد',
                        'amount_required': 0,
                        'amount_paid': 0,
                        'amount_remaining': 0
                    })
            
            conn.close()
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            # في حالة فشل الوصول لقاعدة البيانات، استخدم البيانات المتوفرة
            complete_records = []
            for i, record in enumerate(selected_records):
                complete_records.append({
                    'id': str(record.get('id', i+1)),
                    'اسم_التلميذ': record.get('اسم_التلميذ', '') or record.get('الاسم_الكامل', 'غير محدد'),
                    'رمز_التلميذ': record.get('رمز_التلميذ', '') or record.get('رقم_الامتحان', 'غير محدد'),
                    'المؤسسة_الاصلية': record.get('المؤسسة_الاصلية', 'غير محدد'),
                    'amount_required': 0,
                    'amount_paid': 0,
                    'amount_remaining': 0
                })
            logo_path = None

        # إنشاء مجلد تقارير الغياب إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = absence_data.get('subject', 'مادة').replace(' ', '_').replace('/', '_')
        date_str = absence_data.get('date', '').replace('-', '_')
        output_path = os.path.join(reports_dir, f"تقرير_غياب_{subject_name}_{date_str}_{timestamp}.pdf")

        # إنشاء تقرير الغياب
        generate_absence_report(logo_path, complete_records, output_path, absence_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء تقرير الغياب ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير الغياب بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير الغياب: {str(e)}"

def create_mock_student_record(student_id):
    """إنشاء سجل تجريبي للتلميذ لاختبار التقارير مع البيانات المالية"""
    return {
        'id': str(student_id),
        'اسم_التلميذ': f'التلميذ رقم {student_id}',
        'رمز_التلميذ': f'CD{student_id:05d}',
        'المؤسسة_الاصلية': 'مؤسسة تجريبية',
        'amount_required': 1000.0,
        'amount_paid': 600.0,
        'amount_remaining': 400.0
    }

def create_mock_absence_data(student_id):
    """إنشاء بيانات غياب تجريبية"""
    return {
        'subject': 'مادة تجريبية',
        'date': datetime.now().strftime('%Y-%m-%d'),
        'period': 'الفترة الصباحية',
        'notes': f'تقرير تجريبي للتلميذ ID: {student_id}'
    }

def print_absence_report_for_student(parent=None, student_id=None):
    """
    دالة مختصرة لإنشاء تقرير غياب لتلميذ محدد
    
    المعاملات:
        parent: كائن النافذة الأم
        student_id: معرف التلميذ
        
    العوائد:
        (success, output_path, message): نتيجة العملية
    """
    try:
        if not student_id:
            return False, None, "معرف التلميذ غير متوفر."
        
        # إنشاء بيانات تجريبية
        test_records = [create_mock_student_record(student_id)]
        test_absence_data = create_mock_absence_data(student_id)
        
        # استدعاء دالة إنشاء التقرير الأساسية
        return print_absence_report(
            parent=parent,
            selected_records=test_records,
            absence_data=test_absence_data
        )
        
    except Exception as e:
        return False, None, f"خطأ في إنشاء تقرير الغياب: {str(e)}"

def generate_section_month_report(section, month, parent=None):
    """إنشاء تقرير القسم والشهر مع جدولين"""
    try:
        # إعداد مسار قاعدة البيانات
        db_path = "data.db"
        if not os.path.exists(db_path):
            return False, "لا يمكن العثور على قاعدة البيانات"
        
        # إعداد مسار الحفظ
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"تقرير_القسم_{section}_{month}_{current_time}.pdf"
        output_path = os.path.join(desktop_path, output_filename)
        
        # جمع البيانات
        section_info = get_section_info_from_db(db_path, section)
        monthly_duties_data = get_monthly_duties_by_section_month(db_path, section, month)
        
        if not section_info:
            return False, f"لا توجد معلومات للقسم '{section}' في قاعدة البيانات"
            
        if not monthly_duties_data:
            return False, f"لا توجد أداءات شهرية للقسم '{section}' في شهر '{month}'"
        
        # إنشاء التقرير
        success = generate_section_month_pdf_report(
            output_path=output_path,
            section_info=section_info,
            monthly_duties_data=monthly_duties_data,
            section=section,
            month=month
        )
        
        if success:
            message = f"تم إنشاء التقرير بنجاح\nالمسار: {output_path}"
            return True, message
        else:
            return False, "فشل في إنشاء ملف PDF"
            
    except Exception as e:
        error_msg = f"خطأ في إنشاء التقرير: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return False, error_msg

def get_section_info_from_db(db_path, section):
    """جلب معلومات القسم من جدول_المواد_والاقسام"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT القسم, المادة, اسم_الاستاذ, نسبة_الواجبات
            FROM جدول_المواد_والاقسام 
            WHERE القسم = ?
            LIMIT 1
        """, (section,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'section': result[0],
                'subject': result[1],
                'teacher_name': result[2],
                'duties_percentage': result[3]
            }
        return None
        
    except Exception as e:
        print(f"خطأ في جلب معلومات القسم: {str(e)}")
        return None

def get_monthly_duties_by_section_month(db_path, section, month):
    """جلب الأداءات الشهرية للقسم في الشهر المحدد"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # ربط جدول monthly_duties مع جدول_البيانات للحصول على القسم
        cursor.execute("""
            SELECT 
                md.id,
                jb.اسم_التلميذ,
                jb.رمز_التلميذ,
                jb.المؤسسة_الاصلية,
                md.amount_required,
                md.amount_paid,
                md.amount_remaining,
                md.payment_status,
                md.payment_date,
                md.notes
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE jb.القسم = ? AND md.month = ?
            ORDER BY jb.اسم_التلميذ
        """, (section, month))
        
        results = cursor.fetchall()
        conn.close()
        
        monthly_duties = []
        for row in results:
            monthly_duties.append({
                'id': row[0],
                'student_name': row[1],
                'student_code': row[2],
                'institution': row[3],
                'amount_required': row[4],
                'amount_paid': row[5],
                'amount_remaining': row[6],
                'payment_status': row[7],
                'payment_date': row[8],
                'notes': row[9]
            })
        
        return monthly_duties
        
    except Exception as e:
        print(f"خطأ في جلب الأداءات الشهرية: {str(e)}")
        return []

def generate_section_month_pdf_report(output_path, section_info, monthly_duties_data, section, month):
    """إنشاء تقرير PDF للقسم والشهر"""
    try:
        pdf = ArabicPDF()
        pdf.add_page()
        
        # إضافة الخط العربي
        pdf.add_font('Amiri', '', 'Amiri-Regular.ttf', uni=True)
        pdf.add_font('Amiri', 'B', 'Amiri-Bold.ttf', uni=True)
        
        # رأس الصفحة
        add_section_month_header(pdf, section, month)
        
        # الجدول الأول: معلومات القسم
        add_section_info_table(pdf, section_info)
        
        # مساحة فارغة
        pdf.ln(10)
        
        # الجدول الثاني: الأداءات الشهرية
        add_monthly_duties_table(pdf, monthly_duties_data, month)
        
        # حفظ الملف
        pdf.output(output_path)
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء PDF: {str(e)}")
        traceback.print_exc()
        return False

def add_section_month_header(pdf, section, month):
    """إضافة رأس التقرير"""
    pdf.set_font('Amiri', 'B', 18)
    title = prepare_arabic_text(f"تقرير القسم: {section} - شهر: {month}")
    pdf.cell(0, 15, title, align='C', ln=True)
    
    pdf.set_font('Amiri', '', 12)
    date_text = prepare_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}")
    pdf.cell(0, 10, date_text, align='C', ln=True)
    pdf.ln(5)

def add_section_info_table(pdf, section_info):
    """إضافة جدول معلومات القسم"""
    # عنوان الجدول
    pdf.set_font('Amiri', 'B', 14)
    title = prepare_arabic_text("معلومات القسم")
    pdf.cell(0, 8, title, align='C', ln=True)
    pdf.ln(2)
    
    # بيانات الجدول (صفان، 4 أعمدة)
    pdf.set_font('Amiri', '', 12)
    
    # الصف الأول: القسم + المادة
    col_widths = [70, 30, 60, 35]
    
    # الصف الأول
    pdf.cell(col_widths[0], 8, prepare_arabic_text("القسم"), border=1, align='C')
    pdf.cell(col_widths[1], 8, prepare_arabic_text(section_info['section']), border=1, align='C')
    pdf.cell(col_widths[2], 8, prepare_arabic_text("المادة"), border=1, align='C')
    pdf.cell(col_widths[3], 8, prepare_arabic_text(section_info['subject']), border=1, align='C')
    pdf.ln()
    
    # الصف الثاني: نسبة_الواجبات + اسم_الاستاذ
    pdf.cell(col_widths[0], 8, prepare_arabic_text("نسبة الواجبات"), border=1, align='C')
    pdf.cell(col_widths[1], 8, prepare_arabic_text(str(section_info['duties_percentage'])), border=1, align='C')
    pdf.cell(col_widths[2], 8, prepare_arabic_text("اسم الأستاذ"), border=1, align='C')
    pdf.cell(col_widths[3], 8, prepare_arabic_text(section_info['teacher_name']), border=1, align='C')
    pdf.ln()

def add_monthly_duties_table(pdf, monthly_duties_data, month):
    """إضافة جدول الأداءات الشهرية"""
    # عنوان الجدول
    pdf.set_font('Amiri', 'B', 14)
    title = prepare_arabic_text(f"الأداءات الشهرية - شهر {month}")
    pdf.cell(0, 8, title, align='C', ln=True)
    pdf.ln(2)
    
    # رأس الجدول
    pdf.set_font('Amiri', 'B', 10)
    col_widths = [15, 45, 25, 40, 20, 20, 20, 10]
    headers = ['ID', 'اسم التلميذ', 'رمز التلميذ', 'المؤسسة الأصلية', 'المطلوب', 'المدفوع', 'المتبقي', 'الحالة']
    
    for i, header in enumerate(headers):
        pdf.cell(col_widths[i], 8, prepare_arabic_text(header), border=1, align='C')
    pdf.ln()
    
    # بيانات الجدول
    pdf.set_font('Amiri', '', 9)
    for duty in monthly_duties_data:
        pdf.cell(col_widths[0], 6, str(duty['id']), border=1, align='C')
        pdf.cell(col_widths[1], 6, prepare_arabic_text(duty['student_name']), border=1, align='C')
        pdf.cell(col_widths[2], 6, prepare_arabic_text(duty['student_code']), border=1, align='C')
        pdf.cell(col_widths[3], 6, prepare_arabic_text(duty['institution']), border=1, align='C')
        pdf.cell(col_widths[4], 6, str(duty['amount_required']), border=1, align='C')
        pdf.cell(col_widths[5], 6, str(duty['amount_paid']), border=1, align='C')
        pdf.cell(col_widths[6], 6, str(duty['amount_remaining']), border=1, align='C')
        
        # حالة الدفع مختصرة
        status = "مدفوع" if duty['amount_remaining'] == 0 else "متبقي"
        pdf.cell(col_widths[7], 6, prepare_arabic_text(status), border=1, align='C')
        pdf.ln()

def prepare_arabic_text(text):
    """تحضير النص العربي للعرض في PDF"""
    if text is None:
        return ""
    text = str(text)
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

if __name__=='__main__':
    # مثال على الاستخدام مع بيانات تجريبية محدثة
    try:
        # اختبار إنشاء تقرير لتلميذ محدد
        success, output_path, message = print_absence_report_for_student(
            student_id=1
        )
        
        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")
        
        # اختبار إنشاء تقرير عام مع بيانات جدول_البيانات والبيانات المالية
        test_records = [
            {
                'id': '1',
                'اسم_التلميذ': 'أحمد محمد علي',
                'رمز_التلميذ': 'CD12345',
                'المؤسسة_الاصلية': 'مؤسسة النور',
                'amount_required': 1200.0,
                'amount_paid': 800.0,
                'amount_remaining': 400.0
            },
            {
                'id': '2',
                'اسم_التلميذ': 'فاطمة أحمد محمد',
                'رمز_التلميذ': 'CD12346',
                'المؤسسة_الاصلية': 'مؤسسة الأمل',
                'amount_required': 1500.0,
                'amount_paid': 1000.0,
                'amount_remaining': 500.0
            }
        ]
        
        test_absence_data = {
            'subject': 'الرياضيات',
            'date': '2024-12-20',
            'period': 'الفترة الصباحية',
            'notes': 'غياب بدون عذر'
        }

        success2, output_path2, message2 = print_absence_report(
            selected_records=test_records,
            absence_data=test_absence_data
        )
        
        print(f"\nالاختبار الثاني:")
        print(f"النتيجة: {success2}")
        print(f"المسار: {output_path2}")
        print(f"الرسالة: {message2}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
