#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل الجديد باستخدام QThread لمنع تشنج النافذة
"""
import sys
import sqlite3
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QDateEdit, QLabel, QPushButton, QTextEdit,
                             QProgressBar)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class DataWorkerThread(QThread):
    """خيط عمل لمحاكاة تحميل البيانات الثقيلة"""
    
    progress_update = pyqtSignal(str)
    data_ready = pyqtSignal(list)
    finished_work = pyqtSignal()
    
    def __init__(self, delay_seconds=2):
        super().__init__()
        self.delay_seconds = delay_seconds
        self.is_cancelled = False
    
    def cancel(self):
        self.is_cancelled = True
    
    def run(self):
        """محاكاة عملية تحميل ثقيلة"""
        try:
            self.progress_update.emit("🔄 بدء تحميل البيانات...")
            
            # محاكاة عملية ثقيلة
            for i in range(10):
                if self.is_cancelled:
                    return
                
                time.sleep(self.delay_seconds / 10)  # تقسيم التأخير
                progress = f"📊 معالجة البيانات... {(i+1)*10}%"
                self.progress_update.emit(progress)
            
            if not self.is_cancelled:
                # محاكاة بيانات
                fake_data = [f"تلميذ {i+1}" for i in range(100)]
                self.data_ready.emit(fake_data)
                self.progress_update.emit("✅ تم تحميل البيانات بنجاح")
            
        except Exception as e:
            self.progress_update.emit(f"❌ خطأ: {e}")
        finally:
            self.finished_work.emit()

class ThreadingTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.is_loading = False
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار حل التشنج باستخدام QThread")
        self.setGeometry(250, 250, 700, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار حل تشنج النافذة باستخدام QThread")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2c3e50; padding: 15px;")
        layout.addWidget(title)
        
        # تاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Arial", 12))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Arial", 12))
        self.date_edit.setMinimumHeight(35)
        
        # ربط تغيير التاريخ بالحل الجديد
        self.date_edit.dateChanged.connect(self.on_date_changed_threaded)
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        layout.addLayout(date_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة النتائج
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Arial", 11))
        self.result_text.setMaximumHeight(250)
        self.result_text.setPlainText("جاهز للاختبار...\n\nغير التاريخ ولاحظ:\n✅ لا تشنج في الواجهة\n✅ يمكن تحريك النافذة\n✅ الأزرار تستجيب")
        layout.addWidget(self.result_text)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار بدون خيوط (سيسبب تشنج)
        blocking_btn = QPushButton("اختبار بدون خيوط (تشنج)")
        blocking_btn.setFont(QFont("Arial", 11))
        blocking_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 8px;")
        blocking_btn.clicked.connect(self.test_blocking_operation)
        buttons_layout.addWidget(blocking_btn)
        
        # زر اختبار مع خيوط (لا تشنج)
        threaded_btn = QPushButton("اختبار مع خيوط (لا تشنج)")
        threaded_btn.setFont(QFont("Arial", 11))
        threaded_btn.setStyleSheet("background-color: #27ae60; color: white; padding: 8px;")
        threaded_btn.clicked.connect(self.test_threaded_operation)
        buttons_layout.addWidget(threaded_btn)
        
        # زر إيقاف
        stop_btn = QPushButton("إيقاف العملية")
        stop_btn.setFont(QFont("Arial", 11))
        stop_btn.setStyleSheet("background-color: #f39c12; color: white; padding: 8px;")
        stop_btn.clicked.connect(self.stop_operation)
        buttons_layout.addWidget(stop_btn)
        
        layout.addLayout(buttons_layout)
        
        # تعليمات
        instructions = QLabel("""
🎯 التعليمات:
• غير التاريخ أعلاه - ستلاحظ عدم تشنج النافذة
• جرب الزر الأحمر لرؤية التشنج القديم
• جرب الزر الأخضر لرؤية الحل الجديد
• يمكنك تحريك النافذة أثناء التحميل مع الحل الجديد
        """)
        instructions.setFont(QFont("Arial", 9))
        instructions.setStyleSheet("background-color: #ecf0f1; padding: 10px; border-radius: 5px;")
        layout.addWidget(instructions)
        
    def on_date_changed_threaded(self):
        """معالج تغيير التاريخ باستخدام خيط منفصل"""
        selected_date = self.date_edit.date().toString("yyyy-MM-dd")
        current_time = time.strftime("%H:%M:%S")
        
        message = f"[{current_time}] تم تغيير التاريخ إلى: {selected_date}"
        self.result_text.append(message)
        print(message)
        
        # بدء تحميل البيانات في خيط منفصل
        self.start_threaded_loading(f"تحميل بيانات {selected_date}")
        
    def test_blocking_operation(self):
        """اختبار عملية تسبب تشنج (في خيط الواجهة)"""
        current_time = time.strftime("%H:%M:%S")
        self.result_text.append(f"[{current_time}] بدء عملية مسببة للتشنج...")
        
        # عملية ثقيلة في خيط الواجهة - ستسبب تشنج
        QApplication.processEvents()
        time.sleep(3)  # محاكاة عملية ثقيلة
        
        self.result_text.append(f"[{current_time}] انتهاء العملية المسببة للتشنج")
        
    def test_threaded_operation(self):
        """اختبار عملية لا تسبب تشنج (في خيط منفصل)"""
        current_time = time.strftime("%H:%M:%S")
        self.result_text.append(f"[{current_time}] بدء عملية بدون تشنج...")
        
        self.start_threaded_loading("عملية اختبار")
        
    def start_threaded_loading(self, operation_name):
        """بدء تحميل في خيط منفصل"""
        if self.is_loading:
            self.result_text.append("⚠️ عملية جارية بالفعل...")
            return
        
        self.is_loading = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم دوار
        
        # إنشاء وتشغيل خيط العمل
        self.worker_thread = DataWorkerThread(delay_seconds=2)
        self.worker_thread.progress_update.connect(self.on_progress_update)
        self.worker_thread.data_ready.connect(self.on_data_ready)
        self.worker_thread.finished_work.connect(self.on_work_finished)
        
        self.worker_thread.start()
        
    def on_progress_update(self, message):
        """معالج تحديث التقدم"""
        current_time = time.strftime("%H:%M:%S")
        self.result_text.append(f"[{current_time}] {message}")
        print(f"[{current_time}] {message}")
        
    def on_data_ready(self, data):
        """معالج استلام البيانات"""
        current_time = time.strftime("%H:%M:%S")
        message = f"[{current_time}] تم استلام {len(data)} عنصر"
        self.result_text.append(message)
        print(message)
        
    def on_work_finished(self):
        """معالج انتهاء العمل"""
        self.is_loading = False
        self.progress_bar.setVisible(False)
        
        current_time = time.strftime("%H:%M:%S")
        self.result_text.append(f"[{current_time}] انتهاء العملية ✅")
        
        # تنظيف الخيط
        if self.worker_thread:
            self.worker_thread.deleteLater()
            self.worker_thread = None
            
    def stop_operation(self):
        """إيقاف العملية الجارية"""
        if self.worker_thread and self.worker_thread.isRunning():
            current_time = time.strftime("%H:%M:%S")
            self.result_text.append(f"[{current_time}] إيقاف العملية...")
            
            self.worker_thread.cancel()
            self.worker_thread.wait(1000)
            
            self.result_text.append(f"[{current_time}] تم إيقاف العملية")
        else:
            self.result_text.append("لا توجد عملية جارية")
            
    def closeEvent(self, event):
        """إيقاف الخيوط عند إغلاق النافذة"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.cancel()
            self.worker_thread.wait(2000)
        event.accept()

def main():
    print("🚀 اختبار حل التشنج باستخدام QThread...")
    
    app = QApplication(sys.argv)
    window = ThreadingTestWindow()
    window.show()
    
    print("✅ النافذة جاهزة - اختبر تغيير التاريخ!")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
