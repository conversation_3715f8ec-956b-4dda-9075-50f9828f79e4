#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_groups_table():
    """اختبار جدول المجموعات الصحيح"""
    print("🧪 اختبار جدول المجموعات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # جلب جميع المجموعات
            cursor.execute("SELECT * FROM المجموعات")
            all_groups = cursor.fetchall()
            print(f"📊 إجمالي السجلات: {len(all_groups)}")
            
            if all_groups:
                print("\n📋 جميع المجموعات:")
                for i, group in enumerate(all_groups, 1):
                    print(f"   {i:2d}. ID: {group[0]}, الاسم: {group[1]}")
            
            # جلب المجموعات المتاحة (نفس الاستعلام المستخدم في التطبيق)
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            available_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"\n🎯 المجموعات المتاحة للتعديل الجماعي ({len(available_groups)}):")
            for i, group in enumerate(available_groups, 1):
                print(f"   {i}. {group}")
                
        else:
            print("❌ جدول المجموعات غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_filter_vs_bulk_edit():
    """مقارنة مصادر البيانات للتصفية والتعديل الجماعي"""
    print("\n📊 مقارنة مصادر البيانات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # مجموعات التصفية (من جدول_البيانات)
        print("🔍 مجموعات التصفية (من جدول_البيانات):")
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        
        for i, group in enumerate(filter_groups, 1):
            print(f"   {i}. {group}")
        
        # مجموعات التعديل الجماعي (من المجموعات)
        print(f"\n🔄 مجموعات التعديل الجماعي (من المجموعات):")
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        bulk_groups = [row[0] for row in cursor.fetchall()]
        
        for i, group in enumerate(bulk_groups, 1):
            print(f"   {i}. {group}")
        
        print(f"\n📈 إحصائيات:")
        print(f"   - مجموعات التصفية: {len(filter_groups)}")
        print(f"   - مجموعات التعديل الجماعي: {len(bulk_groups)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المقارنة: {e}")

def simulate_bulk_edit_loading():
    """محاكاة تحميل المجموعات في التعديل الجماعي"""
    print("\n🔄 محاكاة تحميل المجموعات للتعديل الجماعي")
    print("=" * 50)
    
    try:
        print("🔍 بدء تحميل المجموعات للتعديل الجماعي...")
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()

        # التحقق من وجود جدول المجموعات أولاً
        print("🔍 فحص وجود جدول المجموعات...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
        table_check = cursor.fetchone()
        
        if not table_check:
            print("❌ جدول المجموعات غير موجود")
            print("📋 النتيجة: القائمة ستحتوي على 'جدول المجموعات غير موجود'")
            print("🔒 القائمة ستكون معطلة")
            conn.close()
            return
        else:
            print("✅ جدول المجموعات موجود")

        # جلب المجموعات من جدول المجموعات فقط
        print("🔍 جلب المجموعات من جدول المجموعات...")
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups_result = cursor.fetchall()
        groups = [row[0] for row in groups_result]
        
        print(f"📋 المجموعات المجلبة من جدول المجموعات: {groups}")

        if groups:
            print("✅ تم تحميل المجموعات بنجاح")
            print("📋 القائمة ستحتوي على:")
            print("   1. اختر المجموعة الجديدة")
            for i, group in enumerate(groups, 2):
                print(f"   {i}. {group}")
            print("🔓 القائمة ستكون مفعلة")
            print(f"✅ تم تحميل {len(groups)} مجموعة من جدول المجموعات للتعديل الجماعي")
            print(f"📋 المجموعات المضافة للقائمة: {groups}")
        else:
            print("⚠️ لا توجد مجموعات في جدول المجموعات")
            print("📋 النتيجة: القائمة ستحتوي على 'لا توجد مجموعات متاحة'")
            print("🔒 القائمة ستكون معطلة")

        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة تحميل المجموعات: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لجدول المجموعات الصحيح")
    print("=" * 60)
    
    test_groups_table()
    test_filter_vs_bulk_edit()
    simulate_bulk_edit_loading()
