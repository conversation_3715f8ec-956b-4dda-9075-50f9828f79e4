# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # الأيقونة الرئيسية
    ('01.ico', '.'),
    
    # جميع ملفات Python الموجودة فعلياً في المجلد
    ('absence_grouped_reports_window.py', '.'),
    ('help_guide.py', '.'),
    ('main_window.py', '.'),
    ('print10.py', '.'),
    ('print11.py', '.'),
    ('print12.py', '.'),
    ('print13.py', '.'),
    ('print133.py', '.'),
    ('print14.py', '.'),
    ('print15.py', '.'),
    ('print16.py', '.'),
    ('professional_exam_table.py', '.'),
    ('sub001_window.py', '.'),
    ('sub01_window.py', '.'),
    ('sub100_window.py', '.'),
    ('sub1_window.py', '.'),
    ('sub202_window.py', '.'),
    ('sub20_window.py', '.'),
    ('sub212_window.py', '.'),
    ('sub222_window.py', '.'),
    ('sub22_window.py', '.'),
    ('sub23_window.py', '.'),
    ('sub24_window.py', '.'),
    ('sub25_window.py', '.'),
    ('sub26_window.py', '.'),
    ('sub27_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub40_window.py', '.'),
    ('sub8_window.py', '.'),
    
    # المجلدات الموجودة
    ('fonts/', 'fonts/'),
    
    # ملفات قاعدة البيانات والتكوين
    ('data.db', '.'),
    
    # إزالة الأنماط wildcard وإضافة ملفات محددة إذا كانت موجودة
    # ('config.txt', '.'),  # أضف هنا أي ملفات txt محددة إذا كانت موجودة
    # ('settings.json', '.'),  # أضف هنا أي ملفات json محددة إذا كانت موجودة
]

a = Analysis(
    ['main_window.py'],  # تغيير نقطة البداية إلى main_window.py
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # جميع النوافذ الموجودة فعلياً
        'absence_grouped_reports_window',
        'help_guide',
        'main_window',
        'professional_exam_table',
        'sub001_window',
        'sub01_window',
        'sub100_window',
        'sub1_window',
        'sub202_window',
        'sub20_window',
        'sub212_window',
        'sub222_window',
        'sub22_window',
        'sub23_window',
        'sub24_window',
        'sub25_window',
        'sub26_window',
        'sub27_window',
        'sub2_window',
        'sub40_window',
        'sub8_window',
        
        # جميع وحدات الطباعة الموجودة
        'print10',
        'print11',
        'print12',
        'print13',
        'print133',
        'print14',
        'print15',
        'print16',
        
        # مكتبات PyQt5 الأساسية
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        
        # مكتبات قاعدة البيانات
        'sqlite3',
        
        # مكتبات معالجة Excel
        'pandas',
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        
        # مكتبات PDF والطباعة العربية
        'fpdf',
        'fpdf2',
        'arabic_reshaper',
        'bidi.algorithm',
        'reportlab',
        
        # مكتبات معالجة الصور
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        
        # مكتبات النظام الأساسية
        'os',
        'sys',
        'datetime',
        'time',
        'pathlib',
        're',
        'json',
        'csv',
        'math',
        'random',
        'subprocess',
        'threading',
        'logging',
        'traceback',
        'hashlib',
        'base64',
        'urllib',
        'urllib.request',
        'urllib.parse',
        
        # مكتبات Windows
        'win32api',
        'win32con',
        'win32gui',
        'win32print',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات الثقيلة غير المطلوبة
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'notebook',
        'scipy',
        'sklearn',
        'cv2',
        'pygame',
        'kivy',
        'flask',
        'django',
        'requests',
        'selenium',
        'beautifulsoup4',
        'scrapy',
        'networkx',
        'sympy',
        'matplotlib',
        'numpy',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء الملف التنفيذي النهائي
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='برنامج_إدارة_الامتحانات',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,          # إزالة رموز التصحيح لتقليل الحجم
    upx=False,           # تعطيل الضغط لتجنب مشاكل التوافق
    console=False,       # إخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',  # بنية 64 بت
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',       # تأكيد مسار الأيقونة
)

# جمع جميع الملفات في مجلد واحد
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,        # الحفاظ على الرموز للتصحيح
    upx=False,          # تعطيل الضغط لضمان الاستقرار
    name='برنامج_إدارة_الامتحانات'
)
