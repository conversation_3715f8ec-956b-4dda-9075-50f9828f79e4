#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_sections_source():
    """اختبار مصدر الأقسام من جدول_المواد_والاقسام"""
    print("🔍 اختبار مصدر الأقسام...")
    
    try:
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول_المواد_والاقسام
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المواد_والاقسام'")
        if not cursor.fetchone():
            print("❌ جدول جدول_المواد_والاقسام غير موجود")
            conn.close()
            return False
        
        print("✅ جدول جدول_المواد_والاقسام موجود")
        
        # جلب الأقسام من الجدول
        cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
        sections = cursor.fetchall()
        
        if sections:
            print(f"📋 تم العثور على {len(sections)} قسم:")
            for i, section in enumerate(sections[:10], 1):  # عرض أول 10 أقسام
                print(f"   {i}. {section[0]}")
            if len(sections) > 10:
                print(f"   ... و {len(sections) - 10} قسم آخر")
        else:
            print("⚠️ لا توجد أقسام في جدول_المواد_والاقسام")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مصدر الأقسام: {e}")
        return False

def test_currency_fields():
    """اختبار حقول العملة"""
    print("\n💰 اختبار حقول العملة...")
    
    # أمثلة على العملات المختلفة
    currency_examples = [
        "500 درهم",
        "200 ريال",
        "100 دولار",
        "50 يورو",
        "1000 جنيه",
        "75.50 درهم",
        "250.25 ريال"
    ]
    
    print("📝 أمثلة على العملات المدعومة:")
    for example in currency_examples:
        print(f"   ✓ {example}")
    
    print("\n🔧 ميزات حقول العملة:")
    print("   • إدخال نص حر مع العملة")
    print("   • دعم الأرقام العشرية")
    print("   • حفظ النص كما هو في قاعدة البيانات")
    print("   • استخراج الرقم تلقائياً للحسابات")
    print("   • الحفاظ على العملة في المبلغ النهائي")
    
    return True

def create_sample_sections():
    """إنشاء أقسام تجريبية في جدول_المواد_والاقسام"""
    print("\n📚 إنشاء أقسام تجريبية...")
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # إنشاء الجدول إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS جدول_المواد_والاقسام (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                اسم_الاستاذ TEXT,
                المادة TEXT,
                القسم TEXT,
                المجموعة TEXT,
                نسبة_الواجبات INTEGER DEFAULT 100,
                تاريخ_الحفظ DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # أقسام تجريبية
        sample_sections = [
            ('أحمد محمد', 'الرياضيات', 'قسم / 01', 'المجموعة الأولى', 100),
            ('فاطمة علي', 'العلوم', 'قسم / 02', 'المجموعة الثانية', 90),
            ('محمد حسن', 'اللغة العربية', 'قسم / 03', 'المجموعة الثالثة', 95),
            ('سارة أحمد', 'الفيزياء', 'قسم / 04', 'المجموعة الرابعة', 85),
            ('عمر خالد', 'الكيمياء', 'قسم / 05', 'المجموعة الخامسة', 100),
        ]
        
        for section_data in sample_sections:
            # التحقق من عدم وجود السجل مسبقاً
            cursor.execute("""
                SELECT COUNT(*) FROM جدول_المواد_والاقسام 
                WHERE اسم_الاستاذ = ? AND المادة = ? AND القسم = ?
            """, (section_data[0], section_data[1], section_data[2]))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO جدول_المواد_والاقسام 
                    (اسم_الاستاذ, المادة, القسم, المجموعة, نسبة_الواجبات)
                    VALUES (?, ?, ?, ?, ?)
                """, section_data)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء الأقسام التجريبية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأقسام التجريبية: {e}")
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار مميزات التعديل الجماعي المحدثة")
    print("=" * 70)
    
    # إنشاء أقسام تجريبية أولاً
    create_sample_sections()
    
    # اختبار مصدر الأقسام
    sections_test = test_sections_source()
    
    # اختبار حقول العملة
    currency_test = test_currency_fields()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print(f"   🔍 مصدر الأقسام: {'✅ نجح' if sections_test else '❌ فشل'}")
    print(f"   💰 حقول العملة: {'✅ نجح' if currency_test else '❌ فشل'}")
    
    if sections_test and currency_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("📋 التحديثات المطبقة:")
        print("   ✓ مصدر الأقسام: جدول_المواد_والاقسام")
        print("   ✓ حقول المبلغ: نص حر مع العملة")
        print("   ✓ دعم عملات متعددة")
        print("   ✓ حفظ النص كما هو")
        print("   ✓ استخراج الأرقام للحسابات")
    else:
        print("\n💥 بعض الاختبارات فشلت!")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
