#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_section_filtering_comparison():
    """مقارنة بين طريقتي التصفية: القسم الحالي vs القسم في monthly_duties"""
    print("🧪 مقارنة طرق التصفية في التقرير الشهري")
    print("=" * 70)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص بيانات التلاميذ وأقسامهم الحالية
        print("1️⃣ فحص الأقسام الحالية للتلاميذ:")
        cursor.execute("""
            SELECT id, اسم_التلميذ, القسم
            FROM جدول_البيانات
            WHERE القسم IS NOT NULL
            ORDER BY القسم, اسم_التلميذ
            LIMIT 10
        """)
        
        current_sections = cursor.fetchall()
        print("   📋 عينة من التلاميذ وأقسامهم الحالية:")
        for student in current_sections:
            print(f"      - ID: {student[0]}, الاسم: {student[1]}, القسم الحالي: {student[2]}")
        
        # 2. فحص بيانات monthly_duties وأقسامها
        print("\n2️⃣ فحص الأقسام في جدول monthly_duties:")
        cursor.execute("""
            SELECT student_id, month, القسم, اسم_الاستاذ
            FROM monthly_duties
            WHERE القسم IS NOT NULL
            ORDER BY student_id, month
            LIMIT 10
        """)
        
        monthly_sections = cursor.fetchall()
        print("   📋 عينة من الأداءات الشهرية وأقسامها:")
        for duty in monthly_sections:
            print(f"      - طالب ID: {duty[0]}, شهر: {duty[1]}, قسم الأداء: {duty[2]}, أستاذ: {duty[3]}")
        
        # 3. مقارنة النتائج لقسم معين وشهر معين
        test_section = "قسم / 01"
        test_month = "يناير"
        
        print(f"\n3️⃣ مقارنة النتائج للقسم: {test_section} - الشهر: {test_month}")
        
        # الطريقة القديمة: الاعتماد على القسم الحالي
        print("\n   🔴 الطريقة القديمة (القسم الحالي في جدول_البيانات):")
        cursor.execute("""
            SELECT 
                jb.اسم_التلميذ,
                jb.القسم as current_section,
                md.القسم as duty_section,
                md.month,
                md.amount_paid
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE jb.القسم = ? AND md.month = ?
            ORDER BY jb.اسم_التلميذ
        """, (test_section, test_month))
        
        old_method_results = cursor.fetchall()
        print(f"      📊 عدد النتائج: {len(old_method_results)}")
        for result in old_method_results:
            print(f"      - {result[0]}: قسم حالي={result[1]}, قسم الأداء={result[2]}, شهر={result[3]}, مبلغ={result[4]}")
        
        # الطريقة الجديدة: الاعتماد على القسم في monthly_duties
        print("\n   🟢 الطريقة الجديدة (القسم في monthly_duties):")
        cursor.execute("""
            SELECT 
                jb.اسم_التلميذ,
                jb.القسم as current_section,
                md.القسم as duty_section,
                md.month,
                md.amount_paid
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE md.القسم = ? AND md.month = ?
            ORDER BY jb.اسم_التلميذ
        """, (test_section, test_month))
        
        new_method_results = cursor.fetchall()
        print(f"      📊 عدد النتائج: {len(new_method_results)}")
        for result in new_method_results:
            print(f"      - {result[0]}: قسم حالي={result[1]}, قسم الأداء={result[2]}, شهر={result[3]}, مبلغ={result[4]}")
        
        # 4. تحليل الاختلافات
        print(f"\n4️⃣ تحليل الاختلافات:")
        old_students = set([r[0] for r in old_method_results])
        new_students = set([r[0] for r in new_method_results])
        
        only_in_old = old_students - new_students
        only_in_new = new_students - old_students
        
        if only_in_old:
            print(f"   ⚠️ طلاب يظهرون في الطريقة القديمة فقط: {only_in_old}")
            print("      (هؤلاء انتقلوا من أقسام أخرى لكن أداءاتهم كانت في أقسام مختلفة)")
        
        if only_in_new:
            print(f"   ✅ طلاب يظهرون في الطريقة الجديدة فقط: {only_in_new}")
            print("      (هؤلاء أدوا مساهماتهم في هذا القسم لكن انتقلوا لأقسام أخرى)")
        
        if not only_in_old and not only_in_new:
            print("   ✅ النتائج متطابقة - لا توجد حالات انتقال")
        
        # 5. محاكاة حالة انتقال طالب
        print(f"\n5️⃣ محاكاة حالة انتقال طالب:")
        print("   📝 سيناريو: طالب كان في قسم / 01 في يناير، انتقل إلى قسم / 02 في فبراير")
        
        # البحث عن طالب للاختبار
        cursor.execute("""
            SELECT id, اسم_التلميذ, القسم
            FROM جدول_البيانات
            LIMIT 1
        """)
        test_student = cursor.fetchone()
        
        if test_student:
            student_id, student_name, current_section = test_student
            print(f"   👤 طالب الاختبار: {student_name} (ID: {student_id})")
            print(f"   📚 القسم الحالي: {current_section}")
            
            # فحص أداءاته الشهرية
            cursor.execute("""
                SELECT month, القسم, amount_paid
                FROM monthly_duties
                WHERE student_id = ?
                ORDER BY month
            """, (student_id,))
            
            student_duties = cursor.fetchall()
            if student_duties:
                print("   📊 أداءاته الشهرية:")
                for duty in student_duties:
                    print(f"      - {duty[0]}: قسم={duty[1]}, مبلغ={duty[2]}")
            else:
                print("   📊 لا توجد أداءات شهرية مسجلة")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def demonstrate_correct_accounting():
    """توضيح المحاسبة الصحيحة"""
    print("\n💡 توضيح المحاسبة الصحيحة")
    print("=" * 50)
    
    print("🎯 المبدأ الصحيح:")
    print("   ✅ كل دفعة تُحسب للقسم الذي كان فيه الطالب وقت الدفع")
    print("   ✅ إذا انتقل الطالب، تبقى دفعاته السابقة مرتبطة بالأقسام السابقة")
    print("   ✅ الدفعات الجديدة تُحسب للقسم الجديد")
    
    print("\n📊 مثال:")
    print("   👤 الطالب: أحمد محمد")
    print("   📅 يناير 2024: كان في قسم / 01 → دفع 500 درهم → يُحسب لقسم / 01")
    print("   🔄 انتقل إلى قسم / 02")
    print("   📅 فبراير 2024: أصبح في قسم / 02 → دفع 600 درهم → يُحسب لقسم / 02")
    
    print("\n📈 النتيجة في التقارير:")
    print("   📋 تقرير قسم / 01 - يناير: يظهر أحمد بدفعة 500 درهم")
    print("   📋 تقرير قسم / 02 - فبراير: يظهر أحمد بدفعة 600 درهم")
    print("   📋 تقرير قسم / 01 - فبراير: لا يظهر أحمد (لأنه انتقل)")
    print("   📋 تقرير قسم / 02 - يناير: لا يظهر أحمد (لم يكن فيه بعد)")

if __name__ == "__main__":
    print("🧪 اختبار شامل لطرق التصفية في التقرير الشهري")
    print("=" * 80)
    
    test_section_filtering_comparison()
    demonstrate_correct_accounting()
    
    print("\n✅ تم الانتهاء من الاختبار!")
    print("🎯 التعديل المطبق: الآن التصفية تعتمد على عمود القسم في monthly_duties")
    print("💡 هذا يضمن دقة المحاسبة عند انتقال الطلاب بين الأقسام")
