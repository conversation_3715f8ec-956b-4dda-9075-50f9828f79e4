#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime
from archived_accounts_manager import ArchivedAccountsManager

def quick_archive_available_months():
    """ترحيل سريع لجميع الشهور المتاحة"""
    print("🚀 أداة الترحيل السريع للحسابات")
    print("=" * 50)
    
    # إنشاء مدير الحسابات المرحلة
    manager = ArchivedAccountsManager()
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب جميع الشهور المتاحة في monthly_duties
        cursor.execute("""
            SELECT DISTINCT month, year, COUNT(*) as count
            FROM monthly_duties
            WHERE month IS NOT NULL AND year IS NOT NULL
            GROUP BY month, year
            ORDER BY year DESC, 
                CASE month
                    WHEN 'يناير' THEN 1
                    WHEN 'فبراير' THEN 2
                    WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4
                    WHEN 'مايو' THEN 5
                    WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7
                    WHEN 'أغسطس' THEN 8
                    WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10
                    WHEN 'نوفمبر' THEN 11
                    WHEN 'ديسمبر' THEN 12
                    ELSE 13
                END DESC
        """)
        
        available_months = cursor.fetchall()
        conn.close()
        
        if not available_months:
            print("⚠️ لا توجد بيانات شهرية للترحيل")
            return
        
        print(f"📋 تم العثور على {len(available_months)} شهر للترحيل:")
        for month_data in available_months:
            print(f"   📅 {month_data[0]}/{month_data[1]} - {month_data[2]} سجل")
        
        # ترحيل كل شهر
        print(f"\n🔄 بدء عملية الترحيل...")
        archived_count = 0
        
        for month_data in available_months:
            month, year, record_count = month_data
            
            print(f"\n📤 ترحيل {month}/{year}...")
            success, message = manager.archive_monthly_accounts(month, year, force_update=True)
            
            if success:
                print(f"   ✅ {message}")
                archived_count += 1
            else:
                print(f"   ❌ فشل: {message}")
        
        print(f"\n🎯 النتيجة النهائية:")
        print(f"   ✅ تم ترحيل {archived_count} من {len(available_months)} شهر")
        
        # عرض الشهور المرحلة
        archived_months = manager.get_archived_months()
        print(f"\n📊 الشهور المرحلة حالياً: {len(archived_months)}")
        for month_data in archived_months:
            print(f"   📅 {month_data[0]}/{month_data[1]} - {month_data[2]} سجل")
        
    except Exception as e:
        print(f"❌ خطأ في الترحيل السريع: {e}")

def test_archived_report():
    """اختبار التقرير مع البيانات المرحلة"""
    print(f"\n🧪 اختبار التقرير مع البيانات المرحلة")
    print("=" * 50)
    
    try:
        manager = ArchivedAccountsManager()
        archived_months = manager.get_archived_months()
        
        if not archived_months:
            print("⚠️ لا توجد شهور مرحلة للاختبار")
            return
        
        # اختيار أول شهر مرحل للاختبار
        test_month, test_year = archived_months[0][0], archived_months[0][1]
        test_section = "قسم / 01"
        
        print(f"🎯 اختبار التقرير:")
        print(f"   📅 الشهر: {test_month}/{test_year}")
        print(f"   📚 القسم: {test_section}")
        
        # اختبار جلب البيانات
        from print_section_monthly import get_monthly_duties_by_section_month
        
        report_data = get_monthly_duties_by_section_month('data.db', test_section, test_month, test_year)
        
        if report_data:
            print(f"   ✅ تم جلب {len(report_data)} سجل من البيانات المرحلة")
            
            # عرض عينة من البيانات
            print(f"   📝 عينة من البيانات:")
            for i, record in enumerate(report_data[:3], 1):
                print(f"      {i}. {record[0]} - {record[2]:.2f} درهم - {record[5]}")
            
            if len(report_data) > 3:
                print(f"      ... و {len(report_data) - 3} سجل آخر")
            
            # اختبار إنشاء التقرير
            print(f"\n📊 اختبار إنشاء التقرير...")
            from print_section_monthly import print_section_monthly_report
            
            success, output_path, message = print_section_monthly_report(
                section=test_section,
                month=test_month
            )
            
            if success:
                print(f"   ✅ تم إنشاء التقرير بنجاح!")
                print(f"   📁 المسار: {output_path}")
                print(f"   💬 الرسالة: {message}")
            else:
                print(f"   ❌ فشل في إنشاء التقرير: {message}")
        else:
            print(f"   ⚠️ لا توجد بيانات للقسم {test_section} في {test_month}/{test_year}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير: {e}")

def check_data_consistency():
    """فحص تطابق البيانات الحية والمرحلة"""
    print(f"\n🔍 فحص تطابق البيانات")
    print("=" * 30)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        manager = ArchivedAccountsManager()
        archived_months = manager.get_archived_months()
        
        if not archived_months:
            print("⚠️ لا توجد شهور مرحلة للفحص")
            return
        
        for month_data in archived_months:
            month, year = month_data[0], month_data[1]
            
            # البيانات الحية
            cursor.execute("""
                SELECT COUNT(*), SUM(amount_paid), SUM(amount_required)
                FROM monthly_duties
                WHERE month = ? AND year = ?
            """, (month, year))
            live_data = cursor.fetchone()
            
            # البيانات المرحلة
            cursor.execute("""
                SELECT COUNT(*), SUM(amount_paid), SUM(amount_required)
                FROM الحسابات_المرحلة
                WHERE month = ? AND year = ?
            """, (month, year))
            archived_data = cursor.fetchone()
            
            print(f"📅 {month}/{year}:")
            print(f"   📊 حية: {live_data[0]} سجل، مدفوع: {live_data[1] or 0:.2f}، مطلوب: {live_data[2] or 0:.2f}")
            print(f"   📋 مرحلة: {archived_data[0]} سجل، مدفوع: {archived_data[1] or 0:.2f}، مطلوب: {archived_data[2] or 0:.2f}")
            
            # فحص التطابق
            if (live_data[0] == archived_data[0] and 
                abs((live_data[1] or 0) - (archived_data[1] or 0)) < 0.01 and
                abs((live_data[2] or 0) - (archived_data[2] or 0)) < 0.01):
                print(f"   ✅ البيانات متطابقة")
            else:
                print(f"   ⚠️ هناك اختلاف في البيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص التطابق: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("🏦 أداة الترحيل السريع والاختبار")
    print("=" * 60)
    
    # 1. ترحيل البيانات
    quick_archive_available_months()
    
    # 2. فحص التطابق
    check_data_consistency()
    
    # 3. اختبار التقرير
    test_archived_report()
    
    print(f"\n🎯 تم الانتهاء من جميع العمليات!")
    print("💡 الآن التقارير ستعتمد على البيانات المرحلة تلقائياً")
    print("🖥️ يمكنك استخدام واجهة الإدارة: python archived_accounts_window.py")

if __name__ == "__main__":
    main()
