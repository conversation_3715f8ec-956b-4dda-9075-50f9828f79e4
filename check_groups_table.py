#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_groups_table():
    """فحص جدول المجموعات"""
    print("🔍 فحص جدول المجموعات")
    print("=" * 40)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول المجموعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المجموعات'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # فحص هيكل الجدول
            cursor.execute("PRAGMA table_info(جدول_المجموعات)")
            columns = cursor.fetchall()
            print(f"📋 أعمدة الجدول ({len(columns)} عمود):")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            
            # فحص وجود عمود اسم_المجموعة
            column_names = [col[1] for col in columns]
            if 'اسم_المجموعة' in column_names:
                print("✅ عمود اسم_المجموعة موجود")
                
                # جلب المجموعات المتاحة
                cursor.execute("SELECT COUNT(*) FROM جدول_المجموعات")
                total_count = cursor.fetchone()[0]
                print(f"📊 إجمالي السجلات: {total_count}")
                
                cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
                groups = cursor.fetchall()
                print(f"📊 عدد المجموعات المتاحة: {len(groups)}")
                
                if groups:
                    print("المجموعات المتاحة:")
                    for i, group in enumerate(groups[:10], 1):
                        print(f"   {i:2d}. {group[0]}")
                    if len(groups) > 10:
                        print(f"   ... و {len(groups) - 10} مجموعة أخرى")
                else:
                    print("⚠️ لا توجد مجموعات في الجدول")
            else:
                print("❌ عمود اسم_المجموعة غير موجود")
                print("💡 الأعمدة المتاحة:", column_names)
        else:
            print("❌ جدول المجموعات غير موجود")
            print("💡 سيتم استخدام جدول_البيانات كبديل")
            
            # فحص جدول_البيانات كبديل
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            backup_groups = cursor.fetchall()
            print(f"📊 المجموعات في جدول_البيانات: {len(backup_groups)}")
        
        conn.close()
        return table_exists is not None
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول المجموعات: {e}")
        return False

def create_groups_table_if_needed():
    """إنشاء جدول المجموعات إذا لم يكن موجود"""
    print("\n🔧 إنشاء جدول المجموعات إذا لزم الأمر")
    print("-" * 45)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المجموعات'")
        if cursor.fetchone():
            print("✅ جدول المجموعات موجود بالفعل")
            conn.close()
            return True
        
        # إنشاء الجدول
        cursor.execute("""
            CREATE TABLE جدول_المجموعات (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                اسم_المجموعة TEXT NOT NULL UNIQUE,
                وصف_المجموعة TEXT,
                تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إدراج مجموعات تجريبية من جدول_البيانات
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL")
        existing_groups = cursor.fetchall()
        
        if existing_groups:
            print(f"📋 نقل {len(existing_groups)} مجموعة من جدول_البيانات...")
            for group in existing_groups:
                cursor.execute("""
                    INSERT OR IGNORE INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)
                    VALUES (?, ?)
                """, (group[0], f"مجموعة {group[0]}"))
        else:
            # إدراج مجموعات افتراضية
            default_groups = [
                ("المجموعة الأولى", "مجموعة تجريبية أولى"),
                ("المجموعة الثانية", "مجموعة تجريبية ثانية"),
                ("المجموعة الثالثة", "مجموعة تجريبية ثالثة")
            ]
            
            print("📋 إدراج مجموعات افتراضية...")
            for group_name, description in default_groups:
                cursor.execute("""
                    INSERT INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)
                    VALUES (?, ?)
                """, (group_name, description))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جدول المجموعات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول المجموعات: {e}")
        return False

def test_group_loading():
    """اختبار تحميل المجموعات"""
    print("\n🧪 اختبار تحميل المجموعات")
    print("-" * 30)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # محاكاة وظيفة load_groups_to_combo
        try:
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            groups = [row[0] for row in cursor.fetchall()]
            
            print("✅ تحميل من جدول المجموعات نجح")
            print(f"📊 عدد المجموعات: {len(groups)}")
            
            if groups:
                print("المجموعات المحملة:")
                for i, group in enumerate(groups[:5], 1):
                    print(f"   {i}. {group}")
                if len(groups) > 5:
                    print(f"   ... و {len(groups) - 5} مجموعة أخرى")
        
        except Exception as e:
            print(f"⚠️ فشل تحميل من جدول المجموعات: {e}")
            print("🔄 محاولة التحميل من جدول_البيانات...")
            
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            backup_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"✅ تحميل من جدول_البيانات نجح")
            print(f"📊 عدد المجموعات: {len(backup_groups)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل المجموعات: {e}")
        return False

def show_recommendations():
    """عرض التوصيات"""
    print("\n💡 التوصيات:")
    print("=" * 20)
    
    print("🎯 للحصول على أفضل أداء:")
    print("1. تأكد من وجود جدول المجموعات")
    print("2. تأكد من وجود عمود اسم_المجموعة")
    print("3. أضف المجموعات المطلوبة إلى الجدول")
    
    print("\n📋 هيكل جدول المجموعات المقترح:")
    print("CREATE TABLE جدول_المجموعات (")
    print("    id INTEGER PRIMARY KEY AUTOINCREMENT,")
    print("    اسم_المجموعة TEXT NOT NULL UNIQUE,")
    print("    وصف_المجموعة TEXT,")
    print("    تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,")
    print("    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP")
    print(")")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 فحص واختبار جدول المجموعات")
    print("=" * 50)
    
    # فحص الجدول
    table_ok = check_groups_table()
    
    # إنشاء الجدول إذا لزم الأمر
    if not table_ok:
        create_ok = create_groups_table_if_needed()
    else:
        create_ok = True
    
    # اختبار التحميل
    loading_ok = test_group_loading()
    
    # عرض التوصيات
    show_recommendations()
    
    print("\n" + "=" * 50)
    print("📊 نتائج الفحص:")
    print(f"📋 جدول المجموعات: {'✅ موجود' if table_ok else '❌ غير موجود'}")
    print(f"🔧 إنشاء الجدول: {'✅ نجح' if create_ok else '❌ فشل'}")
    print(f"📥 تحميل المجموعات: {'✅ نجح' if loading_ok else '❌ فشل'}")
    
    if table_ok and create_ok and loading_ok:
        print("\n🎉 جدول المجموعات جاهز للاستخدام!")
    else:
        print("\n💥 يوجد مشاكل تحتاج إلى حل!")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
