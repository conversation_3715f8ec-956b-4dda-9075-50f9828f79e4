#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_absence_tables():
    """إصلاح جداول الغياب وإنشاء البيانات المطلوبة"""
    print("🔧 إصلاح جداول الغياب")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص الجداول الموجودة
        print("\n📋 فحص الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        for table in existing_tables:
            print(f"   ✅ {table}")
        
        # 2. إنشاء جدول البيانات إذا لم يكن موجوداً
        if 'جدول_البيانات' not in existing_tables:
            print("\n➕ إنشاء جدول البيانات:")
            cursor.execute("""
                CREATE TABLE جدول_البيانات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الطالب TEXT NOT NULL,
                    اسم_المجموعة TEXT,
                    القسم TEXT,
                    رقم_الهاتف TEXT,
                    العنوان TEXT,
                    تاريخ_التسجيل DATE DEFAULT CURRENT_DATE
                )
            """)
            print("   ✅ تم إنشاء جدول_البيانات")
        else:
            print("\n✅ جدول_البيانات موجود")
        
        # 3. إنشاء جدول المواد والأقسام إذا لم يكن موجوداً
        if 'جدول_المواد_والاقسام' not in existing_tables:
            print("\n➕ إنشاء جدول المواد والأقسام:")
            cursor.execute("""
                CREATE TABLE جدول_المواد_والاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    القسم TEXT NOT NULL,
                    المجموعة TEXT,
                    اسم_الاستاذ TEXT,
                    المادة TEXT,
                    عدد_الحصص INTEGER DEFAULT 0
                )
            """)
            print("   ✅ تم إنشاء جدول_المواد_والاقسام")
        else:
            print("\n✅ جدول_المواد_والاقسام موجود")
        
        # 4. إنشاء جدول الغياب
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS absence_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                student_name TEXT,
                absence_date TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        print("\n✅ جدول الغياب جاهز")
        
        # 5. فحص البيانات الموجودة
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        student_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
        section_count = cursor.fetchone()[0]
        
        print(f"\n📊 البيانات الحالية:")
        print(f"   👥 عدد الطلاب: {student_count}")
        print(f"   🏫 عدد الأقسام: {section_count}")
        
        # 6. إضافة بيانات تجريبية إذا لم تكن موجودة
        if student_count == 0:
            print("\n➕ إضافة بيانات تجريبية للطلاب:")
            
            sample_students = [
                ("أحمد محمد علي", "المجموعة الأولى", "القسم الأول"),
                ("فاطمة حسن أحمد", "المجموعة الثانية", "القسم الثاني"),
                ("محمد علي حسن", "المجموعة الأولى", "القسم الأول"),
                ("عائشة محمود علي", "المجموعة الثالثة", "القسم الثالث"),
                ("علي أحمد محمد", "المجموعة الثانية", "القسم الثاني"),
                ("زينب حسن محمد", "المجموعة الأولى", "القسم الأول"),
                ("يوسف علي أحمد", "المجموعة الثالثة", "القسم الثالث"),
                ("مريم محمد حسن", "المجموعة الثانية", "القسم الثاني"),
                ("خالد حسام الدين", "المجموعة الأولى", "القسم الأول"),
                ("نور الهدى محمد", "المجموعة الثالثة", "القسم الثالث"),
                ("سارة أحمد علي", "المجموعة الثانية", "القسم الثاني"),
                ("حسن محمد أحمد", "المجموعة الأولى", "القسم الأول")
            ]
            
            cursor.executemany("""
                INSERT INTO جدول_البيانات (اسم_الطالب, اسم_المجموعة, القسم)
                VALUES (?, ?, ?)
            """, sample_students)
            
            print(f"   ✅ تم إضافة {len(sample_students)} طالب")
        
        if section_count == 0:
            print("\n➕ إضافة بيانات تجريبية للأقسام:")
            
            sample_sections = [
                ("القسم الأول", "المجموعة الأولى", "أستاذ أحمد محمد", "الرياضيات"),
                ("القسم الثاني", "المجموعة الثانية", "أستاذة فاطمة علي", "العلوم"),
                ("القسم الثالث", "المجموعة الثالثة", "أستاذ محمد حسن", "اللغة العربية"),
                ("القسم الأول", "المجموعة الأولى", "أستاذة سارة أحمد", "اللغة الإنجليزية"),
                ("القسم الثاني", "المجموعة الثانية", "أستاذ علي حسن", "التاريخ"),
                ("القسم الثالث", "المجموعة الثالثة", "أستاذة نور محمد", "الجغرافيا")
            ]
            
            cursor.executemany("""
                INSERT INTO جدول_المواد_والاقسام (القسم, المجموعة, اسم_الاستاذ, المادة)
                VALUES (?, ?, ?, ?)
            """, sample_sections)
            
            print(f"   ✅ تم إضافة {len(sample_sections)} قسم ومادة")
        
        # 7. إضافة بعض سجلات الغياب التجريبية
        cursor.execute("SELECT COUNT(*) FROM absence_records")
        absence_count = cursor.fetchone()[0]
        
        if absence_count == 0:
            print("\n➕ إضافة سجلات غياب تجريبية:")
            
            # جلب بعض الطلاب للغياب التجريبي
            cursor.execute("SELECT id, اسم_الطالب FROM جدول_البيانات LIMIT 3")
            students_for_absence = cursor.fetchall()
            
            sample_absences = []
            dates = ['2024-01-15', '2024-01-16', '2024-01-17']
            
            for student in students_for_absence:
                for date in dates[:2]:  # غياب يومين لكل طالب
                    sample_absences.append((
                        student[0], 
                        student[1], 
                        date, 
                        "غياب تجريبي"
                    ))
            
            cursor.executemany("""
                INSERT INTO absence_records (student_id, student_name, absence_date, notes)
                VALUES (?, ?, ?, ?)
            """, sample_absences)
            
            print(f"   ✅ تم إضافة {len(sample_absences)} سجل غياب تجريبي")
        
        # 8. إنشاء فهارس للأداء
        print("\n🔍 إنشاء فهارس للأداء:")
        
        indexes = [
            ("idx_student_name", "جدول_البيانات", "اسم_الطالب"),
            ("idx_student_section", "جدول_البيانات", "القسم"),
            ("idx_absence_date", "absence_records", "absence_date"),
            ("idx_absence_student", "absence_records", "student_id")
        ]
        
        for idx_name, table_name, column_name in indexes:
            try:
                cursor.execute(f"""
                    CREATE INDEX IF NOT EXISTS {idx_name} 
                    ON `{table_name}`(`{column_name}`)
                """)
                print(f"   ✅ فهرس {idx_name}")
            except Exception as e:
                print(f"   ⚠️ خطأ في إنشاء فهرس {idx_name}: {e}")
        
        # 9. حفظ التغييرات
        conn.commit()
        conn.close()
        
        # 10. عرض النتائج النهائية
        print("\n🎯 النتائج النهائية:")
        print("=" * 30)
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        final_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
        final_sections = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM absence_records")
        final_absences = cursor.fetchone()[0]
        
        print(f"✅ الطلاب: {final_students}")
        print(f"✅ الأقسام: {final_sections}")
        print(f"✅ سجلات الغياب: {final_absences}")
        
        conn.close()
        
        print("\n🚀 الآن يمكنك تشغيل نافذة الغياب:")
        print("   python absence_management_window.py")
        
        print("\n💡 أو تشغيل فحص الجداول:")
        print("   python check_tables.py")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجداول: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_absence_tables()
