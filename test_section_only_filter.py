#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_section_only_filter():
    """اختبار التصفية بالقسم فقط"""
    print("🧪 اختبار التصفية بالقسم فقط")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار تحميل الأقسام
        print("🔍 تحميل الأقسام من جدول_المواد_والاقسام:")
        cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
        sections = [row[0] for row in cursor.fetchall()]
        
        print(f"✅ تم تحميل {len(sections)} قسم:")
        for i, section in enumerate(sections, 1):
            print(f"   {i}. {section}")
        
        # اختبار التصفية لكل قسم
        print(f"\n🔍 اختبار التصفية لكل قسم:")
        for section in sections:
            print(f"\n📂 القسم: {section}")
            
            # محاكاة استعلام التصفية (نفس الاستعلام المستخدم في التطبيق)
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                       القسم,
                       اجمالي_مبلغ_التسجيل, 
                       الواجب_الشهري, المبلغ_النهائي_الشهري
                FROM جدول_البيانات
                WHERE القسم = ?
                ORDER BY id DESC
            """, (section,))
            
            records = cursor.fetchall()
            print(f"   📊 عدد الطلاب: {len(records)}")
            
            if records:
                print("   📋 عينة من الطلاب:")
                for i, record in enumerate(records[:3], 1):  # أول 3 طلاب
                    print(f"      {i}. ID: {record[0]}, الاسم: {record[1]}, القسم: {record[5]}")
                if len(records) > 3:
                    print(f"      ... و {len(records) - 3} طالب آخر")
        
        # اختبار عرض جميع الطلاب (بدون تصفية)
        print(f"\n🌐 اختبار عرض جميع الطلاب:")
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                   القسم,
                   اجمالي_مبلغ_التسجيل, 
                   الواجب_الشهري, المبلغ_النهائي_الشهري
            FROM جدول_البيانات
            ORDER BY id DESC
        """)
        
        all_records = cursor.fetchall()
        print(f"📊 إجمالي الطلاب في قاعدة البيانات: {len(all_records)}")
        
        # إحصائيات الأقسام
        print(f"\n📈 إحصائيات الأقسام:")
        cursor.execute("""
            SELECT القسم, COUNT(*) as count
            FROM جدول_البيانات
            WHERE القسم IS NOT NULL
            GROUP BY القسم
            ORDER BY القسم
        """)
        
        section_stats = cursor.fetchall()
        for section, count in section_stats:
            print(f"   📊 {section}: {count} طالب")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def simulate_filter_workflow():
    """محاكاة سير عمل التصفية الجديد"""
    print("\n🔄 محاكاة سير عمل التصفية الجديد")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # الخطوة 1: تحميل الأقسام
        print("1️⃣ تحميل الأقسام...")
        cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
        sections = [row[0] for row in cursor.fetchall()]
        print(f"   ✅ تم تحميل {len(sections)} قسم")
        
        # الخطوة 2: محاكاة اختيار قسم
        if sections:
            selected_section = sections[0]  # اختيار أول قسم
            print(f"2️⃣ اختيار القسم: {selected_section}")
            
            # الخطوة 3: تطبيق التصفية
            print("3️⃣ تطبيق التصفية...")
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                       القسم,
                       اجمالي_مبلغ_التسجيل, 
                       الواجب_الشهري, المبلغ_النهائي_الشهري
                FROM جدول_البيانات
                WHERE القسم = ?
                ORDER BY id DESC
            """, (selected_section,))
            
            filtered_records = cursor.fetchall()
            print(f"   ✅ نتائج التصفية: {len(filtered_records)} طالب")
            
            if filtered_records:
                print("   📋 الطلاب المفلترين:")
                for record in filtered_records:
                    print(f"      - ID: {record[0]}, الاسم: {record[1]}, القسم: {record[5]}")
        
        # الخطوة 4: محاكاة إعادة تعيين التصفية
        print("\n4️⃣ محاكاة إعادة تعيين التصفية...")
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                   القسم,
                   اجمالي_مبلغ_التسجيل, 
                   الواجب_الشهري, المبلغ_النهائي_الشهري
            FROM جدول_البيانات
            ORDER BY id DESC
        """)
        
        all_records = cursor.fetchall()
        print(f"   ✅ عرض جميع الطلاب: {len(all_records)} طالب")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل للتصفية بالقسم فقط")
    print("=" * 60)
    
    test_section_only_filter()
    simulate_filter_workflow()
    
    print("\n✅ تم الانتهاء من الاختبار!")
    print("🎯 الآن يمكن اختبار التطبيق - التصفية تعتمد على القسم فقط")
