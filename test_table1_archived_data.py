#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_table1_with_archived_data():
    """اختبار الجدول الأول مع البيانات المرحلة"""
    print("🧪 اختبار الجدول الأول مع البيانات المرحلة")
    print("=" * 60)
    
    try:
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب شهر للاختبار
        cursor.execute("""
            SELECT DISTINCT month, year 
            FROM monthly_duties 
            LIMIT 1
        """)
        
        test_data = cursor.fetchone()
        if not test_data:
            print("❌ لا توجد بيانات في monthly_duties للاختبار")
            conn.close()
            return
        
        test_month, test_year = test_data
        test_section = "قسم / 01"
        
        print(f"🎯 اختبار الجدول الأول:")
        print(f"   📅 الشهر: {test_month}/{test_year}")
        print(f"   📚 القسم: {test_section}")
        
        conn.close()
        
        # اختبار الوظيفة المحدثة
        print(f"\n📊 اختبار وظيفة get_section_info_from_db:")
        try:
            from print_section_monthly import get_section_info_from_db
            
            # اختبار بدون شهر (البيانات الحية)
            print(f"\n1️⃣ اختبار بدون شهر (البيانات الحية):")
            section_info_live = get_section_info_from_db('data.db', test_section)
            
            if section_info_live:
                stats = section_info_live['student_stats']
                subjects = section_info_live['section_subjects']
                
                print(f"   📊 إحصائيات حية:")
                print(f"      - إجمالي التلاميذ: {stats[0]}")
                print(f"      - عدد الذكور: {stats[1]}")
                print(f"      - عدد الإناث: {stats[2]}")
                
                if subjects:
                    print(f"   📚 معلومات المواد:")
                    for subject in subjects:
                        print(f"      - أستاذ: {subject[0]}, مادة: {subject[1]}, مجموعة: {subject[2]}, نسبة: {subject[3]}%")
            
            # اختبار مع شهر (البيانات المرحلة)
            print(f"\n2️⃣ اختبار مع شهر (البيانات المرحلة):")
            section_info_archived = get_section_info_from_db('data.db', test_section, test_month, test_year)
            
            if section_info_archived:
                stats = section_info_archived['student_stats']
                subjects = section_info_archived['section_subjects']
                
                print(f"   📊 إحصائيات مرحلة:")
                print(f"      - إجمالي التلاميذ: {stats[0]}")
                print(f"      - عدد الذكور: {stats[1]}")
                print(f"      - عدد الإناث: {stats[2]}")
                
                if subjects:
                    print(f"   📚 معلومات المواد:")
                    for subject in subjects:
                        print(f"      - أستاذ: {subject[0]}, مادة: {subject[1]}, مجموعة: {subject[2]}, نسبة: {subject[3]}%")
            
            # مقارنة النتائج
            print(f"\n🔍 مقارنة النتائج:")
            if section_info_live and section_info_archived:
                live_stats = section_info_live['student_stats']
                archived_stats = section_info_archived['student_stats']
                
                print(f"   📊 البيانات الحية: {live_stats[0]} طالب ({live_stats[1]} ذكر، {live_stats[2]} أنثى)")
                print(f"   📋 البيانات المرحلة: {archived_stats[0]} طالب ({archived_stats[1]} ذكر، {archived_stats[2]} أنثى)")
                
                if live_stats[0] == archived_stats[0]:
                    print(f"   ✅ الأرقام متطابقة")
                else:
                    print(f"   ⚠️ هناك اختلاف في الأرقام")
                    print(f"      💡 هذا طبيعي إذا انتقل طلاب بين الأقسام")
            
        except ImportError as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
        
        # فحص البيانات المرحلة مباشرة
        print(f"\n3️⃣ فحص البيانات المرحلة مباشرة:")
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # فحص وجود البيانات المرحلة
            cursor.execute("""
                SELECT COUNT(*) 
                FROM الحسابات_المرحلة 
                WHERE القسم = ? AND month = ? AND year = ?
            """, (test_section, test_month, test_year))
            
            archived_count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات المرحلة لـ {test_section}: {archived_count}")
            
            if archived_count > 0:
                # جلب الإحصائيات مباشرة
                cursor.execute("""
                    SELECT COUNT(*) as total_students,
                           COUNT(CASE WHEN jb.النوع = 'ذكر' THEN 1 END) as male_count,
                           COUNT(CASE WHEN jb.النوع = 'أنثى' THEN 1 END) as female_count
                    FROM الحسابات_المرحلة ar
                    LEFT JOIN جدول_البيانات jb ON ar.student_id = jb.id
                    WHERE ar.القسم = ? AND ar.month = ? AND ar.year = ?
                """, (test_section, test_month, test_year))
                
                direct_stats = cursor.fetchone()
                print(f"   📋 إحصائيات مباشرة من البيانات المرحلة:")
                print(f"      - إجمالي: {direct_stats[0]}")
                print(f"      - ذكور: {direct_stats[1]}")
                print(f"      - إناث: {direct_stats[2]}")
                
                # عرض عينة من البيانات
                cursor.execute("""
                    SELECT ar.اسم_التلميذ, jb.النوع, ar.amount_paid
                    FROM الحسابات_المرحلة ar
                    LEFT JOIN جدول_البيانات jb ON ar.student_id = jb.id
                    WHERE ar.القسم = ? AND ar.month = ? AND ar.year = ?
                    LIMIT 5
                """, (test_section, test_month, test_year))
                
                sample_data = cursor.fetchall()
                print(f"   📝 عينة من البيانات المرحلة:")
                for data in sample_data:
                    print(f"      - {data[0]} ({data[1]}) - {data[2]:.2f} درهم")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص البيانات المرحلة: {e}")
        
        # اختبار التقرير الكامل
        print(f"\n4️⃣ اختبار التقرير الكامل:")
        try:
            from print_section_monthly import print_section_monthly_report
            
            print(f"   🔄 إنشاء تقرير شهري...")
            success, output_path, message = print_section_monthly_report(
                section=test_section,
                month=test_month
            )
            
            if success:
                print(f"   ✅ تم إنشاء التقرير بنجاح!")
                print(f"   📁 المسار: {output_path}")
                print(f"   💬 الرسالة: {message}")
                
                # فحص حجم الملف
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"   📊 حجم الملف: {file_size} بايت")
                    
                    if file_size > 1000:  # أكثر من 1KB
                        print(f"   ✅ الملف يبدو صحيحاً")
                    else:
                        print(f"   ⚠️ الملف صغير جداً، قد يكون هناك مشكلة")
                else:
                    print(f"   ❌ الملف غير موجود")
            else:
                print(f"   ❌ فشل في إنشاء التقرير: {message}")
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التقرير: {e}")
        
        print(f"\n🎯 انتهى الاختبار!")
        print("💡 الآن الجدول الأول يعتمد على البيانات المرحلة")
        print("📊 الإحصائيات تعكس الوضع وقت الشهر المحدد")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")

if __name__ == "__main__":
    test_table1_with_archived_data()
