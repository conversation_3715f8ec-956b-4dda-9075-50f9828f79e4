#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def fix_data_consistency():
    """إصلاح تطابق البيانات بين الجدولين"""
    print("🔧 إصلاح تطابق البيانات بين الجدولين")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # عرض البيانات الحالية
        print("📊 البيانات الحالية:")
        
        # مجموعات التصفية
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        print(f"🔍 مجموعات التصفية: {filter_groups}")
        
        # مجموعات البيانات
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        data_groups = [row[0] for row in cursor.fetchall()]
        print(f"📋 مجموعات البيانات: {data_groups}")
        
        # أقسام التصفية
        cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
        filter_sections = [row[0] for row in cursor.fetchall()]
        print(f"🔍 أقسام التصفية: {filter_sections}")
        
        # أقسام البيانات
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم")
        data_sections = [row[0] for row in cursor.fetchall()]
        print(f"📋 أقسام البيانات: {data_sections}")
        
        # عرض الطلاب الحاليين
        cursor.execute("SELECT id, اسم_التلميذ, اسم_المجموعة, القسم FROM جدول_البيانات ORDER BY id")
        students = cursor.fetchall()
        print(f"\n👥 الطلاب الحاليين ({len(students)}):")
        for student in students:
            print(f"   ID: {student[0]}, الاسم: {student[1]}, المجموعة: {student[2]}, القسم: {student[3]}")
        
        # اقتراح التحديث
        print(f"\n💡 اقتراح التحديث:")
        print("سيتم تحديث بيانات الطلاب لتتطابق مع مجموعات وأقسام التصفية")
        
        # تحديث بيانات الطلاب
        if students and filter_groups and filter_sections:
            print("\n🔄 بدء التحديث...")
            
            # تحديث كل طالب
            for i, student in enumerate(students):
                student_id = student[0]
                student_name = student[1]
                
                # اختيار مجموعة وقسم من القوائم المتاحة
                group_index = i % len(filter_groups)
                section_index = i % len(filter_sections)
                
                new_group = filter_groups[group_index]
                new_section = filter_sections[section_index]
                
                # تحديث الطالب
                cursor.execute("""
                    UPDATE جدول_البيانات 
                    SET اسم_المجموعة = ?, القسم = ?
                    WHERE id = ?
                """, (new_group, new_section, student_id))
                
                print(f"   ✅ تم تحديث {student_name}: المجموعة = {new_group}, القسم = {new_section}")
            
            conn.commit()
            print(f"\n✅ تم تحديث {len(students)} طالب بنجاح!")
            
            # التحقق من النتائج
            print("\n📊 البيانات بعد التحديث:")
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            updated_groups = [row[0] for row in cursor.fetchall()]
            print(f"📋 مجموعات البيانات المحدثة: {updated_groups}")
            
            cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم")
            updated_sections = [row[0] for row in cursor.fetchall()]
            print(f"📋 أقسام البيانات المحدثة: {updated_sections}")
            
        else:
            print("⚠️ لا توجد بيانات كافية للتحديث")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")

def test_filter_after_fix():
    """اختبار التصفية بعد الإصلاح"""
    print("\n🧪 اختبار التصفية بعد الإصلاح")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار كل مجموعة
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        
        for group in groups:
            print(f"\n🔍 اختبار المجموعة: {group}")
            
            # جلب الأقسام للمجموعة
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_المواد_والاقسام 
                WHERE المجموعة = ? AND القسم IS NOT NULL 
                ORDER BY القسم
            """, (group,))
            sections = [row[0] for row in cursor.fetchall()]
            print(f"   📋 الأقسام المتاحة: {sections}")
            
            # اختبار كل قسم
            for section in sections:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM جدول_البيانات 
                    WHERE اسم_المجموعة = ? AND القسم = ?
                """, (group, section))
                count = cursor.fetchone()[0]
                print(f"   📊 {section}: {count} طالب")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    print("🛠️ إصلاح تطابق البيانات للتصفية")
    print("=" * 60)
    
    fix_data_consistency()
    test_filter_after_fix()
    
    print("\n✅ تم الانتهاء من الإصلاح!")
    print("🎯 الآن يمكن اختبار التصفية في التطبيق")
