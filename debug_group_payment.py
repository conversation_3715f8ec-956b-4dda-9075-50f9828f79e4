#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def debug_database():
    """تشخيص مشاكل قاعدة البيانات"""
    print("🔍 تشخيص قاعدة البيانات...")
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص جدول البيانات الرئيسي
        print("\n📋 فحص جدول_البيانات:")
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        student_count = cursor.fetchone()[0]
        print(f"   عدد التلاميذ: {student_count}")
        
        if student_count > 0:
            cursor.execute("SELECT id, الاسم_الكامل, رمز_التلميذ, القسم FROM جدول_البيانات LIMIT 5")
            students = cursor.fetchall()
            print("   عينة من التلاميذ:")
            for student in students:
                print(f"   - ID: {student[0]}, الاسم: {student[1]}, الرمز: {student[2]}, القسم: {student[3]}")
        
        # فحص جدول الأداءات
        print("\n📊 فحص جدول_الاداءات:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_الاداءات'")
        if cursor.fetchone():
            print("   ✅ جدول_الاداءات موجود")
            
            # فحص أعمدة الجدول
            cursor.execute("PRAGMA table_info(جدول_الاداءات)")
            columns = cursor.fetchall()
            print("   أعمدة الجدول:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            
            # فحص عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM جدول_الاداءات")
            payment_count = cursor.fetchone()[0]
            print(f"   عدد سجلات الدفع: {payment_count}")
            
            # عرض آخر 3 سجلات
            if payment_count > 0:
                cursor.execute("""
                    SELECT اسم_التلميذ, الشهر_المحدد, المبلغ_المدفوع, تاريخ_الدفع 
                    FROM جدول_الاداءات 
                    ORDER BY id DESC LIMIT 3
                """)
                recent_payments = cursor.fetchall()
                print("   آخر المدفوعات:")
                for payment in recent_payments:
                    print(f"   - {payment[0]}: {payment[2]} ({payment[1]}) - {payment[3]}")
        else:
            print("   ⚠️ جدول_الاداءات غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_insert_payment():
    """اختبار إدراج دفعة تجريبية"""
    print("\n🧪 اختبار إدراج دفعة تجريبية...")
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # إنشاء الجدول إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS جدول_الاداءات (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                رمز_التلميذ TEXT,
                اسم_التلميذ TEXT,
                القسم TEXT,
                الشهر_المحدد TEXT,
                المبلغ_المدفوع TEXT,
                تاريخ_الدفع DATE,
                حالة_الدفع TEXT,
                ملاحظات TEXT,
                تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP,
                معرف_التلميذ INTEGER
            )
        """)
        
        # التحقق من وجود عمود الشهر_المحدد
        cursor.execute("PRAGMA table_info(جدول_الاداءات)")
        columns = [col[1] for col in cursor.fetchall()]
        if 'الشهر_المحدد' not in columns:
            print("إضافة عمود الشهر_المحدد...")
            cursor.execute("ALTER TABLE جدول_الاداءات ADD COLUMN الشهر_المحدد TEXT")
        
        # الحصول على أول تلميذ
        cursor.execute("SELECT id, الاسم_الكامل, رمز_التلميذ, القسم FROM جدول_البيانات LIMIT 1")
        student = cursor.fetchone()
        
        if student:
            print(f"إدراج دفعة تجريبية للتلميذ: {student[1]}")
            
            # إدراج دفعة تجريبية
            cursor.execute("""
                INSERT INTO جدول_الاداءات 
                (رمز_التلميذ, اسم_التلميذ, القسم, الشهر_المحدد, المبلغ_المدفوع, 
                 تاريخ_الدفع, حالة_الدفع, ملاحظات, معرف_التلميذ)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                student[2] or f"ID_{student[0]}",  # رمز التلميذ
                student[1],  # اسم التلميذ
                student[3] or "غير محدد",  # القسم
                "يناير",  # الشهر المحدد
                "100 درهم",  # المبلغ المدفوع
                datetime.now().strftime('%Y-%m-%d'),  # تاريخ الدفع
                "مدفوع كاملاً",  # حالة الدفع
                "اختبار تجريبي",  # الملاحظات
                student[0]  # معرف التلميذ
            ))
            
            conn.commit()
            print("✅ تم إدراج الدفعة التجريبية بنجاح")
            
            # التحقق من الإدراج
            cursor.execute("SELECT * FROM جدول_الاداءات WHERE ملاحظات = 'اختبار تجريبي'")
            test_payment = cursor.fetchone()
            if test_payment:
                print(f"✅ تم التحقق من الإدراج: ID {test_payment[0]}")
            else:
                print("❌ لم يتم العثور على الدفعة التجريبية")
        else:
            print("❌ لا يوجد تلاميذ في قاعدة البيانات")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإدراج: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_selected_students():
    """فحص التلاميذ المحددين في الواجهة"""
    print("\n👥 نصائح لفحص التلاميذ المحددين:")
    print("   1. تأكد من تحديد التلاميذ بوضع علامة ✓ في العمود الأول")
    print("   2. تأكد من أن التلاميذ المحددين لديهم بيانات صحيحة")
    print("   3. تحقق من أن الأسماء والأرقام غير فارغة")
    print("   4. تأكد من أن قاعدة البيانات تحتوي على بيانات التلاميذ")
    
    print("\n🔧 خطوات استكشاف الأخطاء:")
    print("   1. افتح نافذة الأداء الجماعي")
    print("   2. راقب رسائل وحدة التحكم (Console)")
    print("   3. تحقق من رسائل الخطأ التفصيلية")
    print("   4. تأكد من صحة البيانات المدخلة")

def main():
    """الوظيفة الرئيسية للتشخيص"""
    print("=" * 60)
    print("🔍 تشخيص مشاكل أداء الواجبات الشهرية الجماعية")
    print("=" * 60)
    
    # تشخيص قاعدة البيانات
    db_ok = debug_database()
    
    # اختبار الإدراج
    if db_ok:
        insert_ok = test_insert_payment()
    else:
        insert_ok = False
    
    # نصائح للمستخدم
    check_selected_students()
    
    print("\n" + "=" * 60)
    print("📊 نتائج التشخيص:")
    print(f"   🗄️ قاعدة البيانات: {'✅ سليمة' if db_ok else '❌ مشكلة'}")
    print(f"   📝 اختبار الإدراج: {'✅ نجح' if insert_ok else '❌ فشل'}")
    
    if db_ok and insert_ok:
        print("\n🎉 النظام يعمل بشكل صحيح!")
        print("💡 إذا كان لا يزال هناك خطأ، تحقق من:")
        print("   - تحديد التلاميذ بشكل صحيح")
        print("   - صحة البيانات المدخلة")
        print("   - رسائل وحدة التحكم")
    else:
        print("\n💥 يوجد مشاكل تحتاج إلى حل!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
