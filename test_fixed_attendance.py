#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtGui import QColor

def test_color_fix():
    """اختبار إصلاح الألوان"""
    try:
        print("🔧 اختبار إصلاح الألوان...")
        
        # اختبار QColor
        red_color = QColor(250, 219, 216)
        yellow_color = QColor(255, 243, 205)
        green_color = QColor(213, 244, 230)
        
        print(f"✅ الألوان تعمل بشكل صحيح:")
        print(f"   أحمر: {red_color.name()}")
        print(f"   أصفر: {yellow_color.name()}")
        print(f"   أخضر: {green_color.name()}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الألوان: {e}")
        return False

def test_database_structure():
    """اختبار بنية قاعدة البيانات"""
    try:
        print("\n🔧 اختبار بنية قاعدة البيانات...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار جدول البيانات
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        columns = cursor.fetchall()
        print(f"✅ أعمدة جدول_البيانات: {[col[1] for col in columns]}")
        
        # اختبار جدول المواد والأقسام
        try:
            cursor.execute("PRAGMA table_info(جدول_المواد_والاقسام)")
            sections_columns = cursor.fetchall()
            print(f"✅ أعمدة جدول_المواد_والاقسام: {[col[1] for col in sections_columns]}")
        except Exception as e:
            print(f"⚠️ جدول_المواد_والاقسام غير موجود: {e}")
        
        # اختبار عينة من البيانات
        cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL LIMIT 3")
        sample = cursor.fetchall()
        print(f"✅ عينة من البيانات:")
        for i, student in enumerate(sample, 1):
            print(f"   {i}. {student[0]} | {student[1]} | {student[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_attendance_window():
    """اختبار نافذة معالجة الغياب"""
    try:
        print("\n🔧 اختبار نافذة معالجة الغياب...")
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from attendance_processing_window import AttendanceProcessingWindow
        
        window = AttendanceProcessingWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # اختبار تحميل الأقسام
        window.load_sections()
        sections_count = window.section_combo.count()
        print(f"✅ تم تحميل {sections_count} قسم")
        
        # اختبار تحميل التلاميذ
        window.load_students_data()
        students_count = window.students_table.rowCount()
        print(f"✅ تم تحميل {students_count} تلميذ في الجدول")
        
        # اختبار الأعمدة
        headers = []
        for col in range(window.students_table.columnCount()):
            header = window.students_table.horizontalHeaderItem(col)
            if header:
                headers.append(header.text())
        print(f"✅ رؤوس الأعمدة: {headers}")
        
        # اختبار عينة من البيانات
        if students_count > 0:
            print("✅ عينة من البيانات في الجدول:")
            for row in range(min(3, students_count)):
                absence_count = window.students_table.item(row, 0).text() if window.students_table.item(row, 0) else "فارغ"
                student_name = window.students_table.item(row, 1).text() if window.students_table.item(row, 1) else "فارغ"
                student_code = window.students_table.item(row, 2).text() if window.students_table.item(row, 2) else "فارغ"
                order_num = window.students_table.item(row, 3).text() if window.students_table.item(row, 3) else "فارغ"
                print(f"   الصف {row+1}: غياب={absence_count} | اسم={student_name} | رمز={student_code} | ترتيب={order_num}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_report():
    """اختبار تقرير PDF"""
    try:
        print("\n🔧 اختبار تقرير PDF...")
        
        # اختبار الاستيراد
        from attendance_sheet_report import create_attendance_sheet_report
        print("✅ تم استيراد نظام التقارير بنجاح")
        
        # اختبار إنشاء تقرير (بدون فتح)
        # success, path, message = create_attendance_sheet_report("قسم تجريبي", 2024, 1, "يناير")
        # print(f"✅ اختبار إنشاء التقرير: {success}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات الإصلاحات...")
    print("=" * 50)
    
    # اختبار الألوان
    color_ok = test_color_fix()
    
    # اختبار قاعدة البيانات
    db_ok = test_database_structure()
    
    # اختبار النافذة
    window_ok = test_attendance_window()
    
    # اختبار PDF
    pdf_ok = test_pdf_report()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   إصلاح الألوان: {'✅ نجح' if color_ok else '❌ فشل'}")
    print(f"   قاعدة البيانات: {'✅ نجح' if db_ok else '❌ فشل'}")
    print(f"   نافذة معالجة الغياب: {'✅ نجح' if window_ok else '❌ فشل'}")
    print(f"   نظام التقارير: {'✅ نجح' if pdf_ok else '❌ فشل'}")
    
    if all([color_ok, db_ok, window_ok, pdf_ok]):
        print("\n🎯 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("\n🚀 لتشغيل النافذة:")
        print("   python attendance_processing_window.py")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
