#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_filter_groups():
    """اختبار مجموعات التصفية من جدول_المواد_والاقسام"""
    print("🔍 اختبار مجموعات التصفية")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول_المواد_والاقسام
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المواد_والاقسام'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول_المواد_والاقسام موجود")
            
            # جلب جميع المجموعات من الجدول
            cursor.execute("SELECT * FROM جدول_المواد_والاقسام")
            all_records = cursor.fetchall()
            print(f"📊 إجمالي السجلات في جدول_المواد_والاقسام: {len(all_records)}")
            
            if all_records:
                print("\n📋 عينة من السجلات:")
                for i, record in enumerate(all_records[:5], 1):  # أول 5 سجلات
                    print(f"   {i}. {record}")
            
            # محاكاة استعلام التصفية (نفس الاستعلام المستخدم في التطبيق)
            cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
            filter_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"\n🎯 مجموعات التصفية ({len(filter_groups)}):")
            for i, group in enumerate(filter_groups, 1):
                print(f"   {i}. {group}")
                
        else:
            print("❌ جدول_المواد_والاقسام غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_bulk_edit_groups():
    """اختبار مجموعات التعديل الجماعي من جدول المجموعات"""
    print("\n🔄 اختبار مجموعات التعديل الجماعي")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول المجموعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # محاكاة استعلام التعديل الجماعي
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            bulk_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"🎯 مجموعات التعديل الجماعي ({len(bulk_groups)}):")
            for i, group in enumerate(bulk_groups, 1):
                print(f"   {i}. {group}")
                
        else:
            print("❌ جدول المجموعات غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def compare_sources():
    """مقارنة مصادر البيانات"""
    print("\n📊 مقارنة مصادر البيانات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # مجموعات التصفية (من جدول_المواد_والاقسام)
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        
        # مجموعات التعديل الجماعي (من المجموعات)
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        bulk_groups = [row[0] for row in cursor.fetchall()]
        
        print("🔍 مجموعات التصفية (جدول_المواد_والاقسام):")
        for group in filter_groups:
            print(f"   • {group}")
        
        print("\n🔄 مجموعات التعديل الجماعي (المجموعات):")
        for group in bulk_groups:
            print(f"   • {group}")
        
        print(f"\n📈 إحصائيات:")
        print(f"   - مجموعات التصفية: {len(filter_groups)}")
        print(f"   - مجموعات التعديل الجماعي: {len(bulk_groups)}")
        
        # التحقق من التطابق
        if set(filter_groups) == set(bulk_groups):
            print("   ✅ المجموعات متطابقة")
        else:
            print("   ⚠️ المجموعات مختلفة")
            print("   🔍 المجموعات الموجودة في التصفية فقط:")
            for group in set(filter_groups) - set(bulk_groups):
                print(f"      - {group}")
            print("   🔍 المجموعات الموجودة في التعديل الجماعي فقط:")
            for group in set(bulk_groups) - set(filter_groups):
                print(f"      - {group}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المقارنة: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لمصادر بيانات المجموعات المحدثة")
    print("=" * 60)
    
    test_filter_groups()
    test_bulk_edit_groups()
    compare_sources()
