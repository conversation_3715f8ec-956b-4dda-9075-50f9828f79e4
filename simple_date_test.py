#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لحل مشكلة مؤشر الماوس الدوار عند تغيير التاريخ
نافذة منفصلة بدون جداول أو قواعد بيانات
"""
import sys
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QDateEdit, QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont

class SimpleDateTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        print("🔧 إنشاء نافذة اختبار بسيطة...")
        self.init_ui()
        print("✅ تم إنشاء النافذة - جاهزة للاختبار")
        
    def init_ui(self):
        self.setWindowTitle("اختبار مؤشر الماوس عند تغيير التاريخ")
        self.setGeometry(300, 300, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار حل مؤشر الماوس الدوار")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: blue; padding: 20px;")
        layout.addWidget(title)
        
        # تاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("اختر التاريخ:")
        date_label.setFont(QFont("Arial", 14))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Arial", 14))
        self.date_edit.setMinimumHeight(40)
        
        # ربط تغيير التاريخ بالحل الآمن
        self.date_edit.dateChanged.connect(self.on_date_changed_safe)
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        layout.addLayout(date_layout)
        
        # منطقة عرض النتائج
        self.result_text = QTextEdit()
        self.result_text.setFont(QFont("Arial", 12))
        self.result_text.setMaximumHeight(200)
        self.result_text.setPlainText("جاهز للاختبار...\nغير التاريخ ولاحظ عدم ظهور مؤشر دوار!")
        layout.addWidget(self.result_text)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار مع مؤشر دوار (للمقارنة)
        bad_btn = QPushButton("اختبار مع مؤشر دوار")
        bad_btn.setFont(QFont("Arial", 12))
        bad_btn.setStyleSheet("background-color: #e74c3c; color: white; padding: 10px;")
        bad_btn.clicked.connect(self.test_with_cursor)
        buttons_layout.addWidget(bad_btn)
        
        # زر اختبار بدون مؤشر دوار
        good_btn = QPushButton("اختبار بدون مؤشر دوار")
        good_btn.setFont(QFont("Arial", 12))
        good_btn.setStyleSheet("background-color: #27ae60; color: white; padding: 10px;")
        good_btn.clicked.connect(self.test_without_cursor)
        buttons_layout.addWidget(good_btn)
        
        # زر تنظيف المؤشرات
        clear_btn = QPushButton("تنظيف المؤشرات")
        clear_btn.setFont(QFont("Arial", 12))
        clear_btn.setStyleSheet("background-color: #f39c12; color: white; padding: 10px;")
        clear_btn.clicked.connect(self.clear_all_cursors)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
        # رسالة التعليمات
        instructions = QLabel("""
التعليمات:
1. غير التاريخ أعلاه - يجب ألا ترى مؤشر دوار
2. اضغط الزر الأحمر لرؤية المشكلة القديمة
3. اضغط الزر الأخضر لرؤية الحل الجديد
4. اضغط الزر البرتقالي لتنظيف أي مؤشرات عالقة
        """)
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("background-color: #ecf0f1; padding: 15px; border-radius: 5px;")
        layout.addWidget(instructions)
        
    def on_date_changed_safe(self):
        """معالج آمن لتغيير التاريخ - بدون مؤشر دوار"""
        current_time = time.strftime("%H:%M:%S")
        selected_date = self.date_edit.date().toString("yyyy-MM-dd")
        
        print(f"📅 [{current_time}] تم تغيير التاريخ إلى: {selected_date}")
        
        # تنظيف أي مؤشرات موجودة فوراً
        self.clear_all_cursors()
        
        # محاكاة معالجة بسيطة بدون مؤشر تحميل
        QApplication.processEvents()
        
        # تحديث النص
        message = f"[{current_time}] تم تغيير التاريخ إلى: {selected_date}\n✅ لا يوجد مؤشر دوار!"
        self.result_text.append(message)
        
        print(f"✅ [{current_time}] تم معالجة تغيير التاريخ بأمان")
        
    def test_with_cursor(self):
        """اختبار مع مؤشر دوار (للمقارنة)"""
        current_time = time.strftime("%H:%M:%S")
        print(f"🔄 [{current_time}] اختبار مع مؤشر دوار...")
        
        # إظهار مؤشر التحميل
        QApplication.setOverrideCursor(Qt.WaitCursor)
        self.result_text.append(f"[{current_time}] بدء اختبار مع مؤشر دوار...")
        
        # محاكاة عملية طويلة
        QApplication.processEvents()
        time.sleep(2)
        
        # إزالة مؤشر التحميل
        QApplication.restoreOverrideCursor()
        self.result_text.append(f"[{current_time}] انتهاء الاختبار - تم إزالة المؤشر")
        
        print(f"✅ [{current_time}] انتهاء اختبار المؤشر الدوار")
    
    def test_without_cursor(self):
        """اختبار بدون مؤشر دوار"""
        current_time = time.strftime("%H:%M:%S")
        print(f"🛡️ [{current_time}] اختبار بدون مؤشر دوار...")
        
        self.result_text.append(f"[{current_time}] بدء اختبار بدون مؤشر دوار...")
        
        # محاكاة عملية بدون مؤشر تحميل
        QApplication.processEvents()
        time.sleep(1)
        
        self.result_text.append(f"[{current_time}] انتهاء الاختبار - لا يوجد مؤشر دوار!")
        
        print(f"✅ [{current_time}] انتهاء الاختبار الآمن")
    
    def clear_all_cursors(self):
        """تنظيف جميع مؤشرات التحميل"""
        current_time = time.strftime("%H:%M:%S")
        print(f"🔧 [{current_time}] تنظيف جميع المؤشرات...")
        
        try:
            # إزالة جميع المؤشرات المتراكمة
            count = 0
            for i in range(20):
                try:
                    QApplication.restoreOverrideCursor()
                    count += 1
                except:
                    break
            
            # فرض المؤشر العادي
            QApplication.setOverrideCursor(Qt.ArrowCursor)
            QApplication.restoreOverrideCursor()
            
            message = f"[{current_time}] تم تنظيف {count} مؤشر"
            self.result_text.append(message)
            print(f"✅ [{current_time}] تم تنظيف {count} مؤشر")
            
        except Exception as e:
            error_msg = f"[{current_time}] خطأ في تنظيف المؤشرات: {e}"
            self.result_text.append(error_msg)
            print(f"❌ {error_msg}")

def main():
    print("🚀 بدء اختبار حل مؤشر الماوس الدوار...")
    
    app = QApplication(sys.argv)
    print("✅ تم إنشاء التطبيق")
    
    window = SimpleDateTestWindow()
    print("✅ تم إنشاء النافذة")
    
    window.show()
    print("✅ تم عرض النافذة")
    print("🎯 اختبر تغيير التاريخ الآن!")
    print("📝 راقب وحدة التحكم والنافذة لرؤية النتائج")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
