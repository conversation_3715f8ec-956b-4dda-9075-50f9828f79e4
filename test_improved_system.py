#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام المحسن لمعالجة الغياب
"""

import sys
import os

def test_database_setup():
    """اختبار إعداد قاعدة البيانات"""
    try:
        print("🔧 اختبار إعداد قاعدة البيانات...")
        
        # تشغيل إصلاح جداول الغياب
        from fix_absence_table import create_absence_tables, add_sample_absence_data, update_absence_statistics
        
        # إنشاء الجداول
        tables_ok = create_absence_tables()
        if not tables_ok:
            return False
        
        # إضافة بيانات تجريبية
        data_ok = add_sample_absence_data()
        if not data_ok:
            return False
        
        # تحديث الإحصائيات
        stats_ok = update_absence_statistics()
        
        print("✅ إعداد قاعدة البيانات نجح")
        return stats_ok
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def test_improved_report():
    """اختبار التقرير المحسن"""
    try:
        print("\n📄 اختبار التقرير المحسن...")
        
        from improved_attendance_report import create_improved_attendance_report
        
        # إنشاء تقرير تجريبي
        success, path, message = create_improved_attendance_report(
            "قسم / 01", 
            2024, 
            12, 
            "ديسمبر"
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير المحسن: {path}")
            
            # التحقق من وجود الملف
            if os.path.exists(path):
                file_size = os.path.getsize(path)
                print(f"📊 حجم الملف: {file_size} بايت")
                return True
            else:
                print("❌ الملف لم يُنشأ فعلياً")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير المحسن: {e}")
        return False

def test_absence_reports():
    """اختبار تقارير الغياب الورقية"""
    try:
        print("\n📊 اختبار تقارير الغياب الورقية...")
        
        from absence_reports_generator import (
            generate_monthly_absence_report, 
            generate_yearly_absence_report,
            save_and_open_report
        )
        
        # اختبار التقرير الشهري
        print("   📅 اختبار التقرير الشهري...")
        pdf, data, msg = generate_monthly_absence_report(2024, 12)
        if pdf:
            success, path = save_and_open_report(pdf, "شهري", "ديسمبر_2024")
            if success:
                print(f"   ✅ التقرير الشهري: {path}")
            else:
                print("   ❌ فشل في حفظ التقرير الشهري")
                return False
        else:
            print("   ⚠️ لا توجد بيانات للتقرير الشهري")
        
        # اختبار التقرير السنوي
        print("   📈 اختبار التقرير السنوي...")
        pdf, data, msg = generate_yearly_absence_report(2024)
        if pdf:
            success, path = save_and_open_report(pdf, "سنوي", "2024")
            if success:
                print(f"   ✅ التقرير السنوي: {path}")
            else:
                print("   ❌ فشل في حفظ التقرير السنوي")
                return False
        else:
            print("   ⚠️ لا توجد بيانات للتقرير السنوي")
        
        print("✅ تقارير الغياب الورقية تعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تقارير الغياب: {e}")
        return False

def test_attendance_window():
    """اختبار نافذة معالجة الغياب"""
    try:
        print("\n🖥️ اختبار نافذة معالجة الغياب...")
        
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from attendance_processing_window import AttendanceProcessingWindow
        
        # إنشاء النافذة
        window = AttendanceProcessingWindow()
        print("   ✅ تم إنشاء النافذة")
        
        # اختبار تحميل الأقسام
        window.load_sections()
        sections_count = window.section_combo.count()
        print(f"   ✅ تم تحميل {sections_count} قسم")
        
        # اختبار تحميل التلاميذ
        window.load_students_data()
        students_count = window.students_table.rowCount()
        print(f"   ✅ تم تحميل {students_count} تلميذ")
        
        print("✅ نافذة معالجة الغياب تعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        return False

def test_pdf_libraries():
    """اختبار مكتبات PDF"""
    try:
        print("\n📚 اختبار مكتبات PDF...")
        
        # اختبار FPDF
        try:
            from fpdf import FPDF
            print("   ✅ مكتبة FPDF متوفرة")
        except ImportError:
            print("   ❌ مكتبة FPDF غير متوفرة")
            print("      قم بتثبيتها: pip install fpdf2")
            return False
        
        # اختبار مكتبات النص العربي
        try:
            import arabic_reshaper
            from bidi.algorithm import get_display
            print("   ✅ مكتبات النص العربي متوفرة")
        except ImportError:
            print("   ⚠️ مكتبات النص العربي غير متوفرة")
            print("      قم بتثبيتها: pip install arabic-reshaper python-bidi")
            print("      (اختيارية - سيعمل النظام بدونها)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكتبات PDF: {e}")
        return False

def display_improvements():
    """عرض التحسينات المطبقة"""
    print("\n🎯 التحسينات المطبقة:")
    print("=" * 50)
    
    improvements = [
        "✅ ترتيب الأعمدة: الأسبوع الخامس → الرابع → الثالث → الثاني → الأول → الاسم → الرمز → الترتيب",
        "✅ رقم الترتيب في سطرين",
        "✅ تغيير مسميات الأسابيع إلى تاريخ يوم الاثنين",
        "✅ إنشاء جداول الغياب المطلوبة (daily_absence, monthly_absence_stats, yearly_absence_stats)",
        "✅ تقارير ورقية: الغياب الشهري، السنوي، وحسب التلميذ",
        "✅ إعدادات تحكم يدوي في التقرير (عرض الأعمدة، ارتفاع الصفوف، نمط الحدود)",
        "✅ تخطيط محسن للجدول الثاني",
        "✅ دعم النص العربي في التقارير",
        "✅ حفظ تلقائي وفتح التقارير"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")

def display_usage_guide():
    """عرض دليل الاستخدام"""
    print("\n📖 دليل الاستخدام:")
    print("=" * 50)
    
    print("🔧 1. إعداد النظام:")
    print("   python fix_absence_table.py")
    print()
    
    print("🖥️ 2. تشغيل نافذة معالجة الغياب:")
    print("   python attendance_processing_window.py")
    print()
    
    print("📄 3. إنشاء التقرير المحسن:")
    print("   python improved_attendance_report.py")
    print()
    
    print("📊 4. إنشاء تقارير الغياب الورقية:")
    print("   python absence_reports_generator.py")
    print()
    
    print("⚙️ 5. تخصيص التقرير:")
    print("   - افتح improved_attendance_report.py")
    print("   - عدل الإعدادات في الأسطر 8-60")
    print("   - احفظ واختبر")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النظام المحسن لمعالجة الغياب...")
    print("=" * 60)
    
    # اختبار مكتبات PDF
    pdf_ok = test_pdf_libraries()
    
    # اختبار إعداد قاعدة البيانات
    db_ok = False
    if pdf_ok:
        db_ok = test_database_setup()
    
    # اختبار التقرير المحسن
    report_ok = False
    if db_ok:
        report_ok = test_improved_report()
    
    # اختبار تقارير الغياب
    absence_reports_ok = False
    if db_ok:
        absence_reports_ok = test_absence_reports()
    
    # اختبار النافذة
    window_ok = False
    if db_ok:
        window_ok = test_attendance_window()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"   مكتبات PDF: {'✅ نجح' if pdf_ok else '❌ فشل'}")
    print(f"   إعداد قاعدة البيانات: {'✅ نجح' if db_ok else '❌ فشل'}")
    print(f"   التقرير المحسن: {'✅ نجح' if report_ok else '❌ فشل'}")
    print(f"   تقارير الغياب الورقية: {'✅ نجح' if absence_reports_ok else '❌ فشل'}")
    print(f"   نافذة معالجة الغياب: {'✅ نجح' if window_ok else '❌ فشل'}")
    
    if all([pdf_ok, db_ok, report_ok, absence_reports_ok, window_ok]):
        print("\n🎯 جميع الاختبارات نجحت!")
        display_improvements()
        display_usage_guide()
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        
        if not pdf_ok:
            print("\n💡 لإصلاح مشاكل PDF:")
            print("   pip install fpdf2 arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
