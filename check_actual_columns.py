#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_actual_columns():
    """فحص الأعمدة الفعلية في جدول البيانات"""
    print("🔍 فحص الأعمدة الفعلية في جدول البيانات")
    print("=" * 60)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص بنية جدول البيانات
        print("\n📋 بنية جدول البيانات:")
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        columns = cursor.fetchall()
        
        print("   الأعمدة الموجودة:")
        for i, col in enumerate(columns, 1):
            col_id, col_name, col_type, not_null, default_val, primary_key = col
            print(f"      {i:2d}. {col_name} ({col_type})")
            if primary_key:
                print(f"          🔑 مفتاح أساسي")
        
        # فحص عينة من البيانات
        print(f"\n📊 عينة من البيانات (أول 5 سجلات):")
        cursor.execute("SELECT * FROM جدول_البيانات LIMIT 5")
        sample_data = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        print(f"   الأعمدة: {' | '.join(column_names)}")
        print("   " + "-" * 100)
        
        for i, row in enumerate(sample_data, 1):
            row_str = " | ".join([str(cell)[:15] if cell is not None else "NULL" for cell in row])
            print(f"   {i:2d}. {row_str}")
        
        # تحليل الأعمدة المحتملة للطلاب
        print(f"\n🔍 تحليل الأعمدة المحتملة:")
        
        name_candidates = []
        group_candidates = []
        section_candidates = []
        
        for col_name in column_names:
            col_lower = col_name.lower()
            
            # مرشحين لأسماء الطلاب
            if any(keyword in col_lower for keyword in ['اسم', 'name']):
                cursor.execute(f"SELECT COUNT(DISTINCT `{col_name}`) FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != ''")
                distinct_count = cursor.fetchone()[0]
                
                cursor.execute(f"SELECT `{col_name}` FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != '' LIMIT 3")
                sample_values = [row[0] for row in cursor.fetchall()]
                
                name_candidates.append({
                    'column': col_name,
                    'distinct_count': distinct_count,
                    'sample': sample_values
                })
            
            # مرشحين للمجموعات
            if any(keyword in col_lower for keyword in ['مجموعة', 'group']):
                cursor.execute(f"SELECT COUNT(DISTINCT `{col_name}`) FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != ''")
                distinct_count = cursor.fetchone()[0]
                
                cursor.execute(f"SELECT DISTINCT `{col_name}` FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != '' LIMIT 5")
                distinct_values = [row[0] for row in cursor.fetchall()]
                
                group_candidates.append({
                    'column': col_name,
                    'distinct_count': distinct_count,
                    'values': distinct_values
                })
            
            # مرشحين للأقسام
            if any(keyword in col_lower for keyword in ['قسم', 'section']):
                cursor.execute(f"SELECT COUNT(DISTINCT `{col_name}`) FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != ''")
                distinct_count = cursor.fetchone()[0]
                
                cursor.execute(f"SELECT DISTINCT `{col_name}` FROM جدول_البيانات WHERE `{col_name}` IS NOT NULL AND `{col_name}` != '' LIMIT 5")
                distinct_values = [row[0] for row in cursor.fetchall()]
                
                section_candidates.append({
                    'column': col_name,
                    'distinct_count': distinct_count,
                    'values': distinct_values
                })
        
        # عرض المرشحين
        print(f"\n👤 مرشحين لأعمدة أسماء الطلاب:")
        if name_candidates:
            for candidate in name_candidates:
                print(f"   📝 {candidate['column']}: {candidate['distinct_count']} قيمة مميزة")
                print(f"      عينة: {candidate['sample']}")
        else:
            print("   ❌ لم يتم العثور على أعمدة أسماء واضحة")
        
        print(f"\n👥 مرشحين لأعمدة المجموعات:")
        if group_candidates:
            for candidate in group_candidates:
                print(f"   📝 {candidate['column']}: {candidate['distinct_count']} قيمة مميزة")
                print(f"      القيم: {candidate['values']}")
        else:
            print("   ❌ لم يتم العثور على أعمدة مجموعات واضحة")
        
        print(f"\n🏫 مرشحين لأعمدة الأقسام:")
        if section_candidates:
            for candidate in section_candidates:
                print(f"   📝 {candidate['column']}: {candidate['distinct_count']} قيمة مميزة")
                print(f"      القيم: {candidate['values']}")
        else:
            print("   ❌ لم يتم العثور على أعمدة أقسام واضحة")
        
        # اقتراح الأعمدة الصحيحة
        print(f"\n💡 الاقتراحات:")
        print("=" * 30)
        
        # أفضل مرشح للأسماء
        if name_candidates:
            best_name = max(name_candidates, key=lambda x: x['distinct_count'])
            print(f"✅ أفضل عمود للأسماء: {best_name['column']}")
            print(f"   📊 {best_name['distinct_count']} اسم مميز")
        
        # أفضل مرشح للمجموعات
        if group_candidates:
            best_group = min(group_candidates, key=lambda x: x['distinct_count'])
            print(f"✅ أفضل عمود للمجموعات: {best_group['column']}")
            print(f"   📊 {best_group['distinct_count']} مجموعة")
        
        # أفضل مرشح للأقسام
        if section_candidates:
            best_section = min(section_candidates, key=lambda x: x['distinct_count'])
            print(f"✅ أفضل عمود للأقسام: {best_section['column']}")
            print(f"   📊 {best_section['distinct_count']} قسم")
        
        conn.close()
        
        print(f"\n🔧 كود الإصلاح المقترح:")
        print("=" * 40)
        
        if name_candidates:
            best_name_col = max(name_candidates, key=lambda x: x['distinct_count'])['column']
            print(f"students_name_column = '{best_name_col}'")
        
        if group_candidates:
            best_group_col = min(group_candidates, key=lambda x: x['distinct_count'])['column']
            print(f"students_group_column = '{best_group_col}'")
        
        print(f"\n🚀 الخطوة التالية:")
        print("سأقوم بتعديل الكود ليستخدم الأعمدة الصحيحة")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأعمدة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_actual_columns()
