#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QDialogButtonBox, QMessageBox, QFrame,
    QComboBox, QSpinBox, QFileDialog, QProgressDialog, QApplication
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt
import sqlite3
import os
import platform
import subprocess
import sys

# استيراد مكتبة ReportLab لإنشاء ملفات PDF
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.units import mm
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    # محاولة تثبيت مكتبة ReportLab
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.units import mm
        REPORTLAB_AVAILABLE = True
    except:
        pass

class TableLabelsPDF(QDialog):
    """نافذة إعداد ملصقات الطاولات للطباعة على PDF"""

    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        self.db_path = db_path
        self.candidates_data = []
        self.initUI()
        self.loadData()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إعداد ملصقات الطاولات للطباعة")
        self.setFixedSize(800, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
        """)
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إضافة عنوان
        title_label = QLabel("إعداد ملصقات الطاولات للطباعة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066CC;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة تعليمات
        instructions_label = QLabel("حدد خيارات الطباعة ثم اضغط على زر الطباعة لإنشاء ملف PDF")
        instructions_label.setFont(QFont("Calibri", 13, QFont.Bold))
        instructions_label.setStyleSheet("color: black;")
        instructions_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(instructions_label)

        # إنشاء إطار للخيارات
        options_frame = QFrame()
        options_frame.setFrameShape(QFrame.StyledPanel)
        options_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #0066CC;
                border-radius: 5px;
            }
        """)
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(10)

        # إضافة خيار المستوى
        level_layout = QHBoxLayout()
        level_label = QLabel("المستوى:")
        level_label.setFont(QFont("Calibri", 13, QFont.Bold))
        level_layout.addWidget(level_label)

        self.level_combo = QComboBox()
        self.level_combo.setFont(QFont("Calibri", 13))
        self.level_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #0066CC;
                border-radius: 5px;
                padding: 5px;
                min-height: 30px;
                min-width: 200px;
            }
        """)
        level_layout.addWidget(self.level_combo)
        level_layout.addStretch(1)
        options_layout.addLayout(level_layout)

        main_layout.addWidget(options_frame)

        # إضافة معلومات عن عدد المترشحين
        info_layout = QHBoxLayout()

        self.candidates_count_label = QLabel("عدد المترشحين: 0")
        self.candidates_count_label.setFont(QFont("Calibri", 13, QFont.Bold))
        info_layout.addWidget(self.candidates_count_label)

        self.pages_count_label = QLabel("عدد الصفحات المتوقعة: 0")
        self.pages_count_label.setFont(QFont("Calibri", 13, QFont.Bold))
        info_layout.addWidget(self.pages_count_label)

        main_layout.addLayout(info_layout)

        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر الطباعة
        self.print_button = QPushButton("طباعة على PDF")
        self.print_button.setFont(QFont("Calibri", 13, QFont.Bold))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.print_button.clicked.connect(self.printToPDF)
        buttons_layout.addWidget(self.print_button)

        main_layout.addLayout(buttons_layout)

        # إضافة أزرار موافق وإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.setStyleSheet("""
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        main_layout.addWidget(button_box)

        # ربط التغييرات في القوائم المنسدلة - إزالة ربط أحداث البطاقات
        self.level_combo.currentIndexChanged.connect(self.levelChanged)

    def loadData(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            if not self.db_path:
                self.db_path = "data.db"

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل المستويات
            cursor.execute("SELECT DISTINCT المستوى FROM امتحانات ORDER BY المستوى")
            levels = cursor.fetchall()

            self.level_combo.clear()
            self.level_combo.addItem("جميع المستويات")
            for level in levels:
                if level[0]:  # تأكد من أن المستوى ليس فارغًا
                    self.level_combo.addItem(level[0])

            conn.close()

            # تحديث عدد المترشحين
            self.levelChanged()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {e}")

    def levelChanged(self):
        """معالجة تغيير المستوى"""
        try:
            level = self.level_combo.currentText()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل المترشحين للمستوى المحدد
            if level == "جميع المستويات":
                cursor.execute("""
                    SELECT المستوى, القاعة, رقم_الامتحان, الاسم_الكامل
                    FROM امتحانات
                    WHERE رقم_الامتحان IS NOT NULL AND رقم_الامتحان != ''
                    ORDER BY CAST(رقم_الامتحان AS INTEGER)
                """)
            else:
                cursor.execute("""
                    SELECT المستوى, القاعة, رقم_الامتحان, الاسم_الكامل
                    FROM امتحانات
                    WHERE المستوى = ? AND رقم_الامتحان IS NOT NULL AND رقم_الامتحان != ''
                    ORDER BY CAST(رقم_الامتحان AS INTEGER)
                """, (level,))

            self.candidates_data = cursor.fetchall()

            # تحديث عدد المترشحين
            self.candidates_count_label.setText(f"عدد المترشحين: {len(self.candidates_data)}")

            conn.close()

            # تحديث عدد الصفحات
            self.updatePagesCount()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المترشحين: {e}")

    def updatePagesCount(self):
        """تحديث عدد الصفحات المتوقعة"""
        try:
            # قيم افتراضية ثابتة للبطاقات
            cards_per_row = 2
            cards_per_column = 4
            cards_per_page = cards_per_row * cards_per_column

            if len(self.candidates_data) > 0:
                pages_count = (len(self.candidates_data) + cards_per_page - 1) // cards_per_page
                self.pages_count_label.setText(f"عدد الصفحات المتوقعة: {pages_count}")
            else:
                self.pages_count_label.setText("عدد الصفحات المتوقعة: 0")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث عدد الصفحات: {e}")

    def printToPDF(self):
        """طباعة البطاقات على ملف PDF باستخدام مكتبة ReportLab"""
        try:
            if not REPORTLAB_AVAILABLE:
                QMessageBox.warning(self, "تحذير", "مكتبة ReportLab غير متوفرة. يرجى تثبيتها لاستخدام هذه الميزة.")
                return

            # تثبيت المكتبات اللازمة للنصوص العربية
            try:
                import arabic_reshaper
                from bidi.algorithm import get_display
                ARABIC_SUPPORT = True
            except ImportError:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "arabic-reshaper", "python-bidi"])
                    import arabic_reshaper
                    from bidi.algorithm import get_display
                    ARABIC_SUPPORT = True
                except:
                    ARABIC_SUPPORT = False
                    QMessageBox.warning(self, "تحذير", "لم يتم تثبيت مكتبات دعم اللغة العربية. قد لا تظهر النصوص العربية بشكل صحيح.")

            if len(self.candidates_data) == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للطباعة.")
                return

            # دالة لإعادة تشكيل النص العربي
            def reshape(text):
                if ARABIC_SUPPORT:
                    return get_display(arabic_reshaper.reshape(str(text)))
                return text

            # فتح مربع حوار لاختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف PDF", "", "ملفات PDF (*.pdf)"
            )

            if not file_path:
                return

            # إضافة امتداد .pdf إذا لم يكن موجودًا
            if not file_path.lower().endswith('.pdf'):
                file_path += '.pdf'

            # تعيين أبعاد صفحة A4 بالنقاط
            page_width, page_height = A4  # A4 = (595.2755905511812, 841.8897637795277) بالنقاط

            # حساب أبعاد البطاقة - قيم افتراضية ثابتة
            cards_per_row = 2
            cards_per_column = 4

            # هوامش الصفحة بالملليمتر
            margin_horizontal = 10 * mm  # هامش أفقي
            margin_vertical = 10 * mm    # هامش عمودي

            # المسافة بين البطاقات بالملليمتر - 5 نقط
            spacing_horizontal = 5 * mm  # مسافة أفقية بين البطاقات
            spacing_vertical = 5 * mm    # مسافة عمودية بين البطاقات

            # حساب عرض وارتفاع البطاقة لتملأ الصفحة بالكامل
            card_width = (page_width - 2 * margin_horizontal - (cards_per_row - 1) * spacing_horizontal) / cards_per_row
            card_height = (page_height - 2 * margin_vertical - (cards_per_column - 1) * spacing_vertical) / cards_per_column

            # إظهار شريط تقدم محسن
            cards_per_page = cards_per_row * cards_per_column
            pages_count = (len(self.candidates_data) + cards_per_page - 1) // cards_per_page

            progress = QProgressDialog("جاري إنشاء ملف PDF...", "إلغاء العملية", 0, len(self.candidates_data), self)
            progress.setWindowTitle("إنشاء ملف PDF")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)  # إظهار شريط التقدم فوراً
            progress.setMinimumWidth(400)   # عرض أكبر لشريط التقدم
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                    border: 2px solid #0066CC;
                    border-radius: 10px;
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                }
                QProgressBar {
                    border: 2px solid #0066CC;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 12pt;
                    color: black;
                    background-color: #f0f0f0;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45a049);
                    border-radius: 3px;
                }
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12pt;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            progress.show()

            # تسجيل الخط العربي
            try:
                # محاولة تسجيل خط Arial (الأكثر توافقًا مع العربية)
                arial_path = os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'arial.ttf')
                if os.path.exists(arial_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arial_path))
                else:
                    # البحث عن خط Arial في مسارات أخرى
                    font_paths = [
                        '/usr/share/fonts/truetype/msttcorefonts/Arial.ttf',  # Linux
                        '/Library/Fonts/Arial.ttf',  # macOS
                        os.path.join(os.path.dirname(__file__), 'arial.ttf')  # نفس مجلد البرنامج
                    ]
                    for path in font_paths:
                        if os.path.exists(path):
                            pdfmetrics.registerFont(TTFont('Arabic', path))
                            break
            except Exception as e:
                print(f"تعذر تسجيل الخط: {e}")

            # إنشاء ملف PDF
            c = canvas.Canvas(file_path, pagesize=A4)

            # تعيين حجم الخط
            header_font_size = 18
            name_font_size = 18

            current_page = 0
            cards_on_page = 0

            # حساب عدد البطاقات في الصفحة
            cards_per_page = cards_per_row * cards_per_column

            # معالجة كل مترشح
            for index in range(len(self.candidates_data)):
                # التحقق من إلغاء العملية
                if progress.wasCanceled():
                    break

                # تحديث شريط التقدم مع معلومات تفصيلية
                progress.setValue(index + 1)
                progress.setLabelText(f"جاري معالجة البطاقة {index + 1} من {len(self.candidates_data)}\n"
                                    f"الصفحة {current_page + 1} من {pages_count}\n"
                                    f"المترشح: {self.candidates_data[index][3] or 'غير محدد'}")
                
                # السماح للتطبيق بمعالجة الأحداث (لضمان استجابة الواجهة)
                QApplication.processEvents()

                # إذا كنا بحاجة إلى صفحة جديدة
                if cards_on_page == 0:
                    if current_page > 0:
                        c.showPage()  # إنهاء الصفحة الحالية
                    current_page += 1

                # حساب موضع البطاقة في الصفحة
                i = cards_on_page

                # حساب موضع البطاقة
                row = i // cards_per_row
                col = i % cards_per_row

                x = margin_horizontal + col * (card_width + spacing_horizontal)
                y = page_height - margin_vertical - (row + 1) * card_height - row * spacing_vertical

                # الحصول على بيانات المترشح
                level = self.candidates_data[index][0] or ""
                room = self.candidates_data[index][1] or ""
                exam_num = self.candidates_data[index][2] or ""
                full_name = self.candidates_data[index][3] or ""

                # رسم إطار البطاقة
                c.setStrokeColorRGB(0, 0, 0)  # لون أسود غامق
                c.setLineWidth(1.5)
                c.rect(x, y, card_width, card_height)

                # حساب هوامش النص داخل البطاقة
                text_margin = card_width * 0.05  # 5% من عرض البطاقة

                # تعيين الخط للعناوين - استخدام Calibri حجم 18
                try:
                    # محاولة تسجيل خط Calibri
                    calibri_path = os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'calibri.ttf')
                    if os.path.exists(calibri_path):
                        pdfmetrics.registerFont(TTFont('Calibri', calibri_path))
                        c.setFont('Calibri', header_font_size)
                    else:
                        c.setFont('Arabic', header_font_size)
                except:
                    c.setFont('Helvetica-Bold', header_font_size)

                # تعيين اللون الأسود الغامق
                c.setFillColorRGB(0, 0, 0)

                # حساب مواضع النص - توزيع عمودي متساوي
                available_height = card_height - 40  # ترك هامش 20 من الأعلى و20 من الأسفل
                row_spacing = available_height / 4  # تقسيم المساحة على 4 عناصر
                
                row1_y = y + card_height - 20  # الصف الأول (القاعة)
                row2_y = y + card_height - 20 - row_spacing  # الصف الثاني (المستوى)
                row3_label_y = y + card_height - 20 - (row_spacing * 2)  # الصف الثالث (عنوان الاسم)
                row3_value_y = y + card_height - 20 - (row_spacing * 2.5)  # الصف الثالث (قيمة الاسم)
                row4_y = y + 20  # الصف الرابع (رقم الامتحان)

                # رسم القاعة في الصف الأول (من اليمين إلى اليسار)
                room_text = reshape(f"{room}")
                c.drawRightString(x + card_width - text_margin, row1_y, room_text)

                # رسم المستوى في الصف الثاني (في الوسط) - حجم 15
                try:
                    if os.path.exists(os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'calibri.ttf')):
                        c.setFont('Calibri', 15)
                    else:
                        c.setFont('Arabic', 15)
                except:
                    c.setFont('Helvetica-Bold', 15)
                    
                c.drawCentredString(x + card_width/2, row2_y, reshape(level))

                # العودة إلى الخط العادي للعناصر الأخرى
                try:
                    if os.path.exists(os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'calibri.ttf')):
                        c.setFont('Calibri', header_font_size)
                    else:
                        c.setFont('Arabic', header_font_size)
                except:
                    c.setFont('Helvetica-Bold', header_font_size)

                # رسم عنوان الاسم الكامل في الصف الثالث (من اليمين إلى اليسار)
                c.drawRightString(x + card_width - text_margin, row3_label_y, reshape("الاســــم الكامل للمترشح:"))

                # تعيين الخط للاسم - Calibri حجم 18 أسود غامق
                name_font_size = 18
                try:
                    # محاولة استخدام Calibri للاسم أيضاً
                    calibri_path = os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'calibrib.ttf')  # Calibri Bold
                    if os.path.exists(calibri_path):
                        pdfmetrics.registerFont(TTFont('CalibriBold', calibri_path))
                        c.setFont('CalibriBold', name_font_size)
                    else:
                        c.setFont('Arabic', name_font_size)
                except:
                    c.setFont('Helvetica-Bold', name_font_size)

                # رسم الاسم الكامل في الصف الثالث (في الوسط)
                c.drawCentredString(x + card_width/2, row3_value_y, reshape(full_name))

                # تعيين الخط لرقم الامتحان - العودة إلى Calibri حجم 18
                try:
                    if os.path.exists(os.path.join(os.environ.get('WINDIR', ''), 'Fonts', 'calibri.ttf')):
                        c.setFont('Calibri', header_font_size)
                    else:
                        c.setFont('Arabic', header_font_size)
                except:
                    c.setFont('Helvetica-Bold', header_font_size)

                # رسم رقم الامتحان في الصف الرابع (من اليمين إلى اليسار)
                exam_text = reshape(f"رقم الامتحان: {exam_num}")
                c.drawRightString(x + card_width - text_margin, row4_y, exam_text)

                # زيادة عداد البطاقات في الصفحة
                cards_on_page += 1
                if cards_on_page == cards_per_page:
                    cards_on_page = 0

            # حفظ الصفحة الأخيرة
            if cards_on_page > 0:
                c.showPage()

            # حفظ الملف PDF
            c.save()

            # إغلاق شريط التقدم
            progress.setValue(len(self.candidates_data))
            progress.close()

            # عرض رسالة نجاح محسنة
            QMessageBox.information(
                self, "✅ تم بنجاح", 
                f"🎉 تم إنشاء ملف PDF بنجاح!\n\n"
                f"📊 إحصائيات العملية:\n"
                f"   • عدد البطاقات: {len(self.candidates_data)}\n"
                f"   • عدد الصفحات: {current_page}\n"
                f"   • تخطيط البطاقات: {cards_per_row} × {cards_per_column}\n\n"
                f"📁 مسار الملف:\n{file_path}"
            )

            # فتح الملف باستخدام طريقة أكثر أمانًا
            try:
                # تحديد الأمر المناسب حسب نظام التشغيل
                if platform.system() == 'Windows':
                    os.startfile(file_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])
            except Exception as e:
                print(f"تعذر فتح الملف: {e}")
                # عدم إظهار رسالة خطأ للمستخدم، فقط تجاهل الخطأ

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ملف PDF: {e}")

# للاختبار المستقل
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    
    # إنشاء قاعدة بيانات تجريبية للاختبار
    test_db_path = "test_data.db"
    
    try:
        # إنشاء اتصال بقاعدة البيانات التجريبية
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول امتحانات تجريبي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS امتحانات (
                id INTEGER PRIMARY KEY,
                المستوى TEXT,
                القاعة TEXT,
                رقم_الامتحان TEXT,
                الاسم_الكامل TEXT
            )
        ''')
        
        # إدراج بيانات تجريبية
        test_data = [
            ("المستوى الأول", "1", "101", "أحمد محمد علي"),
            ("المستوى الأول", "1", "102", "فاطمة أحمد سالم"),
            ("المستوى الأول", "2", "103", "محمد عبدالله حسن"),
            ("المستوى الثاني", "2", "201", "نور الدين يوسف"),
            ("المستوى الثاني", "3", "202", "عائشة إبراهيم محمد"),
            ("المستوى الثاني", "3", "203", "خالد سعد العتيبي"),
            ("المستوى الثالث", "4", "301", "سارة محمد الأحمد"),
            ("المستوى الثالث", "4", "302", "عبدالرحمن علي السالم"),
        ]
        
        cursor.executemany('''
            INSERT OR REPLACE INTO امتحانات (المستوى, القاعة, رقم_الامتحان, الاسم_الكامل)
            VALUES (?, ?, ?, ?)
        ''', test_data)
        
        conn.commit()
        conn.close()
        
        print(f"تم إنشاء قاعدة بيانات تجريبية: {test_db_path}")
        
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات التجريبية: {e}")
        test_db_path = None
    
    # إنشاء وعرض النافذة
    window = TableLabelsPDF(db_path=test_db_path)
    window.show()
    
    # تشغيل التطبيق
    result = app.exec_()
    
    # تنظيف قاعدة البيانات التجريبية عند الانتهاء
    try:
        if test_db_path and os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("تم حذف قاعدة البيانات التجريبية")
    except:
        pass
    
    sys.exit(result)
