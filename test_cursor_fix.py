#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حل مشكلة مؤشر الماوس الدوار عند تغيير التاريخ
"""
import sys
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QDateEdit, QLabel, QTableWidget, QTableWidgetItem,
                             QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor

class CursorTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        print("🔧 إنشاء نافذة اختبار مؤشر الماوس...")
        self.init_ui()
        self.load_test_data()
        print("✅ تم إنشاء النافذة - جاهزة للاختبار")
        
    def init_ui(self):
        self.setWindowTitle("اختبار حل مؤشر الماوس الدوار")
        self.setGeometry(200, 200, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار حل مؤشر الماوس الدوار عند تغيير التاريخ")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Arial", 12))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Arial", 12))
        
        # ربط تغيير التاريخ بالحل الجديد
        self.date_edit.dateChanged.connect(self.on_date_changed_safe)
        
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        layout.addLayout(date_layout)
        
        # جدول البيانات
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["الاسم", "الرمز", "العدد"])
        self.table.setFont(QFont("Arial", 10))
        layout.addWidget(self.table)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        test_btn = QPushButton("اختبار تحميل عادي")
        test_btn.clicked.connect(self.test_normal_load)
        buttons_layout.addWidget(test_btn)
        
        safe_btn = QPushButton("اختبار تحميل آمن")
        safe_btn.clicked.connect(self.test_safe_load)
        buttons_layout.addWidget(safe_btn)
        
        clear_btn = QPushButton("تنظيف المؤشرات")
        clear_btn.clicked.connect(self.clear_all_cursors)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
        # رسالة التعليمات
        instructions = QLabel("التعليمات: غير التاريخ ولاحظ أنه لا يظهر مؤشر دوار!")
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("color: blue; padding: 10px;")
        layout.addWidget(instructions)
        
    def on_date_changed_safe(self):
        """معالج آمن لتغيير التاريخ - بدون مؤشر دوار"""
        print("📅 تم تغيير التاريخ - تحميل آمن...")
        
        # تنظيف أي مؤشرات موجودة فوراً
        self.clear_all_cursors()
        
        # تحميل البيانات فوراً بدون مؤشر تحميل
        QApplication.processEvents()
        self.load_data_immediate()
        
        print("✅ تم تحميل البيانات بأمان")
        
    def load_data_immediate(self):
        """تحميل فوري للبيانات بدون مؤشر تحميل"""
        try:
            print("🚀 تحميل فوري للبيانات...")
            
            # محاكاة بيانات
            import random
            data = []
            for i in range(50):
                data.append((f"تلميذ {i+1}", f"P{1000+i}", random.randint(0, 15)))
            
            # عرض البيانات في الجدول
            self.table.setRowCount(len(data))
            for row, (name, code, count) in enumerate(data):
                # الاسم
                name_item = QTableWidgetItem(name)
                name_item.setFont(QFont("Arial", 10))
                self.table.setItem(row, 0, name_item)
                
                # الرمز
                code_item = QTableWidgetItem(code)
                code_item.setFont(QFont("Arial", 10))
                self.table.setItem(row, 1, code_item)
                
                # العدد مع تلوين
                count_item = QTableWidgetItem(str(count))
                count_item.setFont(QFont("Arial", 10, QFont.Bold))
                count_item.setTextAlignment(Qt.AlignCenter)
                
                if count > 10:
                    count_item.setBackground(QColor(255, 200, 200))  # أحمر فاتح
                elif count > 5:
                    count_item.setBackground(QColor(255, 255, 200))  # أصفر فاتح
                else:
                    count_item.setBackground(QColor(200, 255, 200))  # أخضر فاتح
                
                self.table.setItem(row, 2, count_item)
            
            print(f"✅ تم تحميل {len(data)} عنصر فوراً")
            
        except Exception as e:
            print(f"❌ خطأ في التحميل الفوري: {e}")
        finally:
            # تأكد من عدم وجود مؤشر تحميل
            self.clear_all_cursors()
    
    def test_normal_load(self):
        """اختبار تحميل عادي مع مؤشر تحميل"""
        print("🔄 اختبار تحميل عادي...")
        QApplication.setOverrideCursor(Qt.WaitCursor)
        
        import time
        time.sleep(1)  # محاكاة عملية طويلة
        
        self.load_data_immediate()
        QApplication.restoreOverrideCursor()
        print("✅ انتهاء التحميل العادي")
    
    def test_safe_load(self):
        """اختبار تحميل آمن بدون مؤشر"""
        print("🛡️ اختبار تحميل آمن...")
        self.load_data_immediate()
        print("✅ انتهاء التحميل الآمن")
    
    def clear_all_cursors(self):
        """تنظيف جميع مؤشرات التحميل"""
        try:
            # إزالة جميع المؤشرات المتراكمة
            for i in range(10):
                try:
                    QApplication.restoreOverrideCursor()
                except:
                    break
            print("🔄 تم تنظيف جميع المؤشرات")
        except Exception as e:
            print(f"⚠️ خطأ في تنظيف المؤشرات: {e}")
    
    def load_test_data(self):
        """تحميل بيانات اختبار أولية"""
        self.load_data_immediate()

def main():
    print("🚀 بدء اختبار حل مؤشر الماوس الدوار...")
    
    app = QApplication(sys.argv)
    print("✅ تم إنشاء التطبيق")
    
    window = CursorTestWindow()
    print("✅ تم إنشاء النافذة")
    
    window.show()
    print("✅ تم عرض النافذة")
    print("🎯 اختبر تغيير التاريخ الآن - يجب ألا ترى مؤشر دوار!")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
