#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_main_window():
    """اختبار النافذة الرئيسية مع الزر الجديد"""
    
    try:
        # إنشاء تطبيق PyQt5
        app = QApplication(sys.argv)
        
        # استيراد النافذة الرئيسية
        from sub252_window_backup import Sub252Window
        
        # إنشاء النافذة
        window = Sub252Window()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تشغيل النافذة الرئيسية بنجاح!")
        print("🔍 ابحث عن زر '📊 تقرير القسم الشهري' في النافذة")
        print("📋 خطوات الاختبار:")
        print("   1. اختر قسماً من قائمة التصفية")
        print("   2. اضغط على زر 'تقرير القسم الشهري'")
        print("   3. اختر الشهر المطلوب")
        print("   4. اضغط على 'إنشاء التقرير'")
        print("   5. سيتم إنشاء التقرير وفتحه تلقائياً")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("🖥️ اختبار النافذة الرئيسية مع زر تقرير القسم الشهري")
    print("=" * 60)
    
    test_main_window()
