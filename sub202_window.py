#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPush<PERSON>utton,
    QListWidget, QListWidgetItem, QMessageBox, QProgressDialog
)
from PyQt5.QtGui import QFont, QPixmap, QIcon
from PyQt5.QtCore import Qt

class DatabaseImportDialog(QDialog):
    """نافذة استيراد المترشحين من قاعدة البيانات"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.parent_window = parent
        self.db_path = db_path
        self.setupUI()
        
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استيراد المترشحين من قاعدة البيانات")
        self.setMinimumSize(500, 400)
        self.setStyleSheet("background-color: #f0f8ff;")
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel("استيراد المترشحين من جدول اللوائح")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # شريط المعلومات
        info_label = QLabel("سيتم استيراد المترشحين من جدول اللوائح إلى جدول الامتحانات")
        info_label.setFont(QFont("Calibri", 12))
        info_label.setStyleSheet("color: #666; padding: 5px;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        start_import_btn = QPushButton("بدء الاستيراد")
        start_import_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        start_import_btn.setFixedHeight(45)
        start_import_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        start_import_btn.clicked.connect(self.start_import_process)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_btn.setFixedHeight(45)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(start_import_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
    
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات: {str(e)}")
            return None
    
    def start_import_process(self):
        """بدء عملية الاستيراد"""
        try:
            conn = self.connect_to_database()
            if not conn:
                return
            
            cursor = conn.cursor()
            
            # التحقق من وجود جدول اللوائح
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            if not cursor.fetchone():
                self.show_custom_warning("تنبيه", "جدول اللوائح غير موجود في قاعدة البيانات.")
                conn.close()
                return
            
            # جلب السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            academic_year_row = cursor.fetchone()
            current_academic_year = academic_year_row[0] if academic_year_row else None
            
            if not current_academic_year:
                self.show_custom_warning("تنبيه", "لم يتم العثور على السنة الدراسية في بيانات المؤسسة.")
                conn.close()
                return
            
            # جلب المستويات المتاحة في جدول اللوائح للسنة الدراسية الحالية
            cursor.execute("""
                SELECT DISTINCT المستوى 
                FROM اللوائح 
                WHERE السنة_الدراسية = ? AND المستوى IS NOT NULL AND المستوى != ''
                ORDER BY المستوى
            """, (current_academic_year,))
            
            available_levels = [row[0] for row in cursor.fetchall()]
            
            if not available_levels:
                self.show_custom_warning("تنبيه", f"لا توجد مستويات متاحة في جدول اللوائح للسنة الدراسية {current_academic_year}.")
                conn.close()
                return
            
            conn.close()
            
            # عرض نافذة اختيار المستويات
            self.show_level_selection_dialog(current_academic_year, available_levels)
            
        except Exception as e:
            self.show_custom_error("خطأ", f"حدث خطأ أثناء بدء عملية الاستيراد: {str(e)}")

    def show_level_selection_dialog(self, academic_year, available_levels):
        """عرض نافذة اختيار المستويات"""
        dialog = QDialog(self)
        dialog.setWindowTitle("اختيار المستويات للاستيراد")
        dialog.setMinimumSize(400, 300)
        dialog.setStyleSheet("background-color: #f0f8ff;")
        
        layout = QVBoxLayout(dialog)
        
        # عنوان
        title_label = QLabel(f"اختر المستويات لاستيرادها من السنة الدراسية: {academic_year}")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        layout.addWidget(title_label)
        
        # قائمة المستويات
        levels_list = QListWidget()
        levels_list.setSelectionMode(QListWidget.MultiSelection)
        
        for level in available_levels:
            item = QListWidgetItem(level)
            item.setCheckState(Qt.Checked)  # تحديد جميع المستويات افتراضياً
            levels_list.addItem(item)
        
        layout.addWidget(levels_list)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("تحديد الكل")
        select_all_btn.clicked.connect(lambda: self.toggle_all_levels(levels_list, True))
        
        deselect_all_btn = QPushButton("إلغاء تحديد الكل")
        deselect_all_btn.clicked.connect(lambda: self.toggle_all_levels(levels_list, False))
        
        import_btn = QPushButton("استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        import_btn.clicked.connect(dialog.accept)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(dialog.reject)
        
        buttons_layout.addWidget(select_all_btn)
        buttons_layout.addWidget(deselect_all_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(import_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        # عرض النافذة
        if dialog.exec_() == QDialog.Accepted:
            # جمع المستويات المحددة
            selected_levels = []
            for i in range(levels_list.count()):
                item = levels_list.item(i)
                if item.checkState() == Qt.Checked:
                    selected_levels.append(item.text())
            
            if not selected_levels:
                self.show_custom_warning("تنبيه", "الرجاء اختيار مستوى واحد على الأقل.")
                return
            
            # تنفيذ عملية الاستيراد
            success_count = self.perform_import_from_lists(academic_year, selected_levels)
            
            if success_count > 0:
                self.show_custom_success(
                    "نجح الاستيراد",
                    f"تم استيراد {success_count} مترشح بنجاح من جدول اللوائح.\n\n"
                    f"السنة الدراسية: {academic_year}\n"
                    f"المستويات: {', '.join(selected_levels)}"
                )
                # تحديث البيانات في النافذة الرئيسية
                if self.parent_window and hasattr(self.parent_window, 'load_data'):
                    self.parent_window.load_data()
                self.accept()
            else:
                self.show_custom_warning("تنبيه", "لم يتم استيراد أي مترشحين. تحقق من وجود بيانات في جدول اللوائح.")

    def toggle_all_levels(self, list_widget, checked):
        """تحديد أو إلغاء تحديد جميع المستويات"""
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            item.setCheckState(Qt.Checked if checked else Qt.Unchecked)
    
    def perform_import_from_lists(self, academic_year, selected_levels):
        """تنفيذ عملية الاستيراد الفعلية من جدول اللوائح"""
        try:
            conn = self.connect_to_database()
            if not conn:
                return 0
            
            cursor = conn.cursor()
            imported_count = 0
            
            # جلب اسم المؤسسة من جدول بيانات_المؤسسة
            cursor.execute("SELECT المؤسسة FROM بيانات_المؤسسة LIMIT 1")
            institution_row = cursor.fetchone()
            institution_name = institution_row[0] if institution_row else ''
            
            # إنشاء مؤشر تقدم
            progress = QProgressDialog("جاري استيراد المترشحين...", "إلغاء", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # جلب البيانات من جدول اللوائح للمستويات المحددة
            placeholders = ','.join(['?' for _ in selected_levels])
            
            cursor.execute(f"""
                SELECT السنة_الدراسية, القسم, المستوى, الرمز, رت
                FROM اللوائح 
                WHERE السنة_الدراسية = ? AND المستوى IN ({placeholders})
                ORDER BY المستوى, القسم, رت
            """, [academic_year] + selected_levels)
            
            lists_data = cursor.fetchall()
            
            if not lists_data:
                progress.close()
                conn.close()
                return 0
            
            progress.setMaximum(len(lists_data))
            
            # جلب البيانات الكاملة من جدول السجل_العام
            for i, (year, section, level, code, rt) in enumerate(lists_data):
                if progress.wasCanceled():
                    break
                
                progress.setValue(i)
                progress.setLabelText(f"جاري استيراد: {code} - {level}")
                
                # البحث عن بيانات المترشح في جدول السجل_العام مع إضافة الجنس وتاريخ_الازدياد
                cursor.execute("""
                    SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد
                    FROM السجل_العام 
                    WHERE الرمز = ?
                """, (code,))
                
                student_data = cursor.fetchone()
                
                if student_data:
                    student_code, full_name, gender, birth_date = student_data
                    
                    # التحقق من عدم وجود المترشح مسبقاً في جدول امتحانات
                    cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE الرمز = ?", (student_code,))
                    if cursor.fetchone()[0] > 0:
                        continue  # تجاهل المترشح إذا كان موجوداً مسبقاً
                    
                    # إدراج المترشح في جدول امتحانات مع جميع البيانات المطلوبة
                    cursor.execute("""
                        INSERT INTO امتحانات 
                        (الرمز, الاسم_الكامل, المستوى, القسم, 'ر.ت', الجنس, 
                         تاريخ_الازدياد, المؤسسة_الأصلية, رقم_الامتحان, القاعة, مركز_الامتحان)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, '', '', ?)
                    """, (student_code, full_name or '', level, section, rt or '', 
                          gender or '', birth_date or '', institution_name or '', institution_name or ''))
                    
                    imported_count += 1
            
            # حفظ التغييرات
            conn.commit()
            conn.close()
            progress.close()
            
            return imported_count
            
        except Exception as e:
            if 'progress' in locals():
                progress.close()
            if 'conn' in locals():
                conn.close()
            self.show_custom_error("خطأ", f"حدث خطأ أثناء الاستيراد: {str(e)}")
            return 0

    def show_custom_success(self, title, message):
        """عرض رسالة نجاح مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 400)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c8);
                border: 3px solid #28a745;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة النجاح
        icon_label = QLabel()
        icon_label.setText("✓")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #28a745; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #155724; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #155724; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }
            QPushButton:pressed {
                background: #1e7e34;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

    def show_custom_warning(self, title, message):
        """عرض رسالة تحذير مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 300)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fff3cd, stop:1 #ffeaa7);
                border: 3px solid #ffc107;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة التحذير
        icon_label = QLabel()
        icon_label.setText("⚠")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #856404; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #856404; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #856404; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                color: #212529;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffcd39, stop:1 #ffc107);
            }
            QPushButton:pressed {
                background: #e0a800;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

    def show_custom_error(self, title, message):
        """عرض رسالة خطأ مخصصة"""
        msg_dialog = QDialog(self)
        msg_dialog.setWindowTitle(title)
        msg_dialog.setFixedSize(500, 300)
        msg_dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8d7da, stop:1 #f1b0b7);
                border: 3px solid #dc3545;
                border-radius: 15px;
            }
        """)
        msg_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(msg_dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # أيقونة الخطأ
        icon_label = QLabel()
        icon_label.setText("✗")
        icon_label.setFont(QFont("Arial", 48, QFont.Bold))
        icon_label.setStyleSheet("color: #721c24; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: #721c24; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # النص
        text_label = QLabel(message)
        text_label.setFont(QFont("Calibri", 14))
        text_label.setStyleSheet("color: #721c24; background: transparent;")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setFixedSize(120, 45)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e4606d, stop:1 #dc3545);
            }
            QPushButton:pressed {
                background: #c82333;
            }
        """)
        ok_button.clicked.connect(msg_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        msg_dialog.exec_()

def open_database_import_dialog(parent=None, db_path="data.db"):
    """فتح نافذة استيراد المترشحين من قاعدة البيانات"""
    try:
        dialog = DatabaseImportDialog(parent=parent, db_path=db_path)
        dialog.exec_()
    except Exception as e:
        # استخدام رسالة خطأ مخصصة
        if parent:
            parent.show_custom_error("خطأ", f"فشل في فتح نافذة الاستيراد: {str(e)}")
        else:
            QMessageBox.critical(None, "خطأ", f"فشل في فتح نافذة الاستيراد: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # اختبار النافذة
    dialog = DatabaseImportDialog()
    dialog.show()
    
    sys.exit(app.exec_())
