from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView,
    QStyledItemDelegate, QStyle, QAbstractScrollArea, QDialog, QLabel,
    QLineEdit, QPushButton, QHBoxLayout, QFrame
)
from PyQt5.QtGui import QFont, QColor, QPen
from PyQt5.QtCore import Qt, QRect, QSize
from datetime import datetime

class ExamTableDelegate(QStyledItemDelegate):
    """مندوب مخصص لعرض خلايا جدول الامتحانات بشكل احترافي"""

    def __init__(self, parent=None):
        super().__init__(parent)

    def paint(self, painter, option, index):
        """رسم الخلية بتنسيق احترافي"""
        # حفظ حالة الرسام
        painter.save()

        # تعيين خلفية الخلية
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, QColor("#e0e0ff"))
        else:
            painter.fillRect(option.rect, QColor("white"))

        # رسم حدود الخلية
        pen = QPen(QColor("#0066CC"))
        pen.setWidth(2)  # زيادة سمك الحدود (يجب أن تكون قيمة صحيحة)
        painter.setPen(pen)
        painter.drawRect(option.rect)

        # الحصول على البيانات
        col = index.column()
        data = index.data(Qt.DisplayRole)

        # تنسيق النص حسب نوع العمود
        if col == 0:  # عمود التاريخ
            self.paintDateCell(painter, option.rect, data)
        else:  # أعمدة المواد
            self.paintSubjectCell(painter, option.rect, data)

        # استعادة حالة الرسام
        painter.restore()

    def paintDateCell(self, painter, rect, text):
        """رسم خلية التاريخ"""
        # إنشاء مستطيل داخلي للنص مع هامش
        inner_rect = QRect(
            rect.left() + 5,
            rect.top() + 5,
            rect.width() - 10,
            rect.height() - 10
        )

        # تعيين الخط
        font = QFont("Calibri", 13)
        font.setBold(True)
        painter.setFont(font)

        # تعيين لون النص
        painter.setPen(QColor("#0066CC"))

        # رسم النص
        painter.drawText(inner_rect, Qt.AlignCenter, text)

    def paintSubjectCell(self, painter, rect, text):
        """رسم خلية المواد"""
        if not text:
            return

        # تقسيم النص إلى مواد
        subjects = text.split(" / ")

        # حساب أبعاد كل مادة
        subject_width = rect.width() // len(subjects)

        for i, subject in enumerate(subjects):
            # حساب مستطيل المادة
            subject_rect = QRect(
                rect.left() + i * subject_width,
                rect.top(),
                subject_width,
                rect.height()
            )

            # رسم المادة
            self.paintSubject(painter, subject_rect, subject, i > 0)

    def paintSubject(self, painter, rect, text, draw_separator):
        """رسم مادة واحدة"""
        # رسم خط فاصل إذا كانت هذه ليست المادة الأولى
        if draw_separator:
            pen = QPen(QColor("#0066CC"))
            pen.setWidth(2)  # زيادة سمك الخط الفاصل (يجب أن تكون قيمة صحيحة)
            painter.setPen(pen)
            painter.drawLine(rect.left(), rect.top() + 5, rect.left(), rect.bottom() - 5)

        # إنشاء مستطيل داخلي للنص مع هامش
        inner_rect = QRect(
            rect.left() + 5,
            rect.top() + 5,
            rect.width() - 10,
            rect.height() - 10
        )

        # تقسيم المادة إلى اسم وتوقيت إذا كان هناك سطر جديد
        if "\n" in text:
            subject_parts = text.split("\n")
            subject_name = subject_parts[0]
            subject_time = subject_parts[1]

            # رسم اسم المادة
            name_rect = QRect(
                inner_rect.left(),
                inner_rect.top(),
                inner_rect.width(),
                inner_rect.height() // 2
            )
            font = QFont("Calibri", 13)
            font.setBold(True)  # جعل النص غامقًا
            painter.setFont(font)
            painter.setPen(QColor("#0066CC"))  # تغيير لون النص إلى أزرق
            painter.drawText(name_rect, Qt.AlignCenter, subject_name)

            # رسم توقيت المادة
            time_rect = QRect(
                inner_rect.left(),
                inner_rect.top() + inner_rect.height() // 2,
                inner_rect.width(),
                inner_rect.height() // 2
            )
            painter.setFont(QFont("Calibri", 11))
            painter.setPen(QPen(QColor("#666666")))
            painter.drawText(time_rect, Qt.AlignCenter, subject_time)
        else:
            # تعيين الخط
            font = QFont("Calibri", 13)
            font.setBold(True)  # جعل النص غامقًا
            painter.setFont(font)

            # تعيين لون النص
            painter.setPen(QColor("#0066CC"))  # تغيير لون النص إلى أزرق

            # رسم النص
            painter.drawText(inner_rect, Qt.AlignCenter, text)

    def sizeHint(self, option, index):
        """اقتراح حجم الخلية"""
        return QSize(option.rect.width(), 50)


class ProfessionalExamTable(QWidget):
    """جدول امتحانات احترافي"""

    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.initUI()
        self.loadData()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 0)  # تقليل الهامش السفلي
        main_layout.setSpacing(0)  # إزالة المسافة بين العناصر

        # إنشاء جدول الامتحانات
        self.exam_table = QTableWidget()
        self.exam_table.setColumnCount(5)  # خمسة أعمدة: اليوم/التاريخ، الحصة1، الحصة2، الحصة3، الحصة4
        self.exam_table.setSelectionBehavior(QTableWidget.SelectRows)  # تحديد الصفوف بالكامل

        # تعيين المندوب المخصص
        self.delegate = ExamTableDelegate()
        self.exam_table.setItemDelegate(self.delegate)

        # تنسيق رأس الجدول
        header_font = QFont("Calibri", 14, QFont.Bold)  # زيادة حجم الخط
        self.exam_table.horizontalHeader().setFont(header_font)
        self.exam_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #0066CC;
                color: white;
                border: 2px solid #0066CC;
                height: 35px;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
            }
        """)
        self.exam_table.horizontalHeader().setDefaultSectionSize(250)
        self.exam_table.horizontalHeader().setFixedHeight(35)
        self.exam_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # تعيين عناوين الأعمدة
        self.exam_table.setHorizontalHeaderLabels(["اليوم والتاريخ", "الحصة الأولى", "الحصة الثانية", "الحصة الثالثة", "الحصة الرابعة"])

        # تنسيق الصفوف
        self.exam_table.verticalHeader().setVisible(False)
        self.exam_table.setShowGrid(False)  # إخفاء الشبكة

        # إزالة المسافة الزائدة في نهاية الجدول
        self.exam_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.exam_table.setSizeAdjustPolicy(QAbstractScrollArea.AdjustToContents)

        # تنسيق الجدول
        self.exam_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #0066CC;
                background-color: white;
                gridline-color: transparent;
                margin-bottom: 0px;
            }
            QTableWidget::item {
                padding: 0px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
                border: none;
            }
            QTableView {
                margin-bottom: 0px;
            }
        """)

        # تمكين النقر المزدوج لتعديل الخلايا
        self.exam_table.cellDoubleClicked.connect(self.onCellDoubleClicked)

        # إضافة الجدول إلى التخطيط الرئيسي
        main_layout.addWidget(self.exam_table)

        # تم إزالة أزرار التحكم (إضافة يوم الامتحان، حذف يوم الامتحان، معاينة الطباعة)

    def loadData(self):
        """تحميل بيانات الامتحانات من قاعدة البيانات"""
        # في هذه المرحلة، سنقوم بإنشاء بيانات افتراضية
        # يمكن تعديل هذه الدالة لاحقًا لتحميل البيانات من قاعدة البيانات

        # تعيين عدد الصفوف
        self.exam_table.setRowCount(3)

        # إضافة بيانات افتراضية
        days = ["الجمعة 18 أبريل 2025", "الثلاثاء 2 يوليوز 2024", "الأربعاء 3 يوليوز 2024"]

        # بيانات الحصص
        subjects = [
            # اليوم الأول
            [
                "الرياضيات\n(08.00-10.00)",
                "اللغة العربية\n(10.30-12.30)",
                "الفيزياء\n(14.30-16.30)",
                "اللغة الفرنسية\n(17.00-19.00)"
            ],
            # اليوم الثاني
            [
                "اللغة العربية\n(08.00-10.00)",
                "الاجتماعيات\n(10.30-12.30)",
                "اللغة الفرنسية\n(14.30-16.30)",
                "التربية الإسلامية\n(17.00-19.00)"
            ],
            # اليوم الثالث
            [
                "الرياضيات\n(08.00-10.00)",
                "العلوم الفيزيائية\n(10.30-12.30)",
                "علوم الحياة والأرض\n(14.30-16.30)",
                "اللغة الإنجليزية\n(17.00-19.00)"
            ]
        ]

        for row in range(3):
            # إضافة اليوم والتاريخ
            day_item = QTableWidgetItem(days[row])
            day_item.setTextAlignment(Qt.AlignCenter)
            day_item.setFont(QFont("Calibri", 13, QFont.Bold))
            self.exam_table.setItem(row, 0, day_item)

            # إضافة الحصص الأربعة
            for col in range(1, 5):
                subject_item = QTableWidgetItem(subjects[row][col-1])
                subject_item.setTextAlignment(Qt.AlignCenter)
                subject_item.setFont(QFont("Calibri", 13))
                self.exam_table.setItem(row, col, subject_item)

        # تعيين ارتفاع الصفوف
        for row in range(self.exam_table.rowCount()):
            self.exam_table.setRowHeight(row, 50)  # زيادة ارتفاع الصفوف لاستيعاب المواد مع التوقيت

    def onCellDoubleClicked(self, row, column):
        """معالجة النقر المزدوج على خلية"""
        # فتح محرر لتعديل محتوى الخلية
        if column == 0:
            # تعديل التاريخ
            self.editDate(row)
        else:
            # تعديل المواد
            self.editSubjects(row, column)

    def editDate(self, row):
        """تعديل تاريخ يوم الامتحان"""
        current_item = self.exam_table.item(row, 0)
        if current_item:
            current_text = current_item.text()

            # إنشاء نافذة حوار مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل التاريخ")
            dialog.setFixedSize(400, 400)  # تعيين حجم النافذة 400×400
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-weight: bold;
                }
                QLineEdit {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 10px;
                    font-size: 14pt;
                    min-height: 40px;
                }
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-weight: bold;
                    min-width: 120px;
                    min-height: 40px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QFrame {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                }
            """)

            # إنشاء تخطيط للنافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان
            title_label = QLabel("تعديل تاريخ يوم الامتحان")
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إضافة إطار للمحتوى
            content_frame = QFrame()
            content_layout = QVBoxLayout(content_frame)
            content_layout.setContentsMargins(15, 15, 15, 15)
            content_layout.setSpacing(15)

            # إضافة تعليمات
            instructions_label = QLabel("أدخل التاريخ الجديد:")
            instructions_label.setFont(QFont("Calibri", 14, QFont.Bold))
            content_layout.addWidget(instructions_label)

            # تم إزالة المثال

            # إضافة حقل النص
            text_edit = QLineEdit(current_text)
            text_edit.setFont(QFont("Calibri", 14))
            content_layout.addWidget(text_edit)

            # إضافة مساحة فارغة
            content_layout.addStretch()

            layout.addWidget(content_frame)

            # إضافة زر حفظ فقط
            button_layout = QHBoxLayout()

            # زر حفظ
            save_button = QPushButton("حفظ")
            save_button.setFont(QFont("Calibri", 16, QFont.Bold))
            save_button.setStyleSheet("""
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 150px;
                min-height: 50px;
            """)
            save_button.clicked.connect(dialog.accept)

            # إضافة مساحة قبل وبعد الزر للتوسيط
            button_layout.addStretch(1)
            button_layout.addWidget(save_button)
            button_layout.addStretch(1)

            layout.addLayout(button_layout)

            # عرض النافذة وانتظار النتيجة
            result = dialog.exec_()

            if result == QDialog.Accepted:
                new_text = text_edit.text()
                if new_text:
                    # تحديث الخلية
                    current_item.setText(new_text)

    def editSubjects(self, row, column):
        """تعديل المواد الدراسية"""
        current_item = self.exam_table.item(row, column)
        if current_item:
            current_text = current_item.text()

            # تحديد عنوان النافذة حسب العمود
            column_titles = ["", "الأولى", "الثانية", "الثالثة", "الرابعة"]
            window_title = f"تعديل الحصة {column_titles[column]}"

            # إنشاء نافذة حوار مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle(window_title)
            dialog.setFixedSize(400, 400)  # تعيين حجم النافذة 400×400
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                }
                QLabel {
                    color: #2980b9;
                    font-weight: bold;
                }
                QLineEdit, QTimeEdit {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 8px;
                    font-size: 12pt;
                    min-height: 30px;
                }
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 8px;
                    font-weight: bold;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QFrame {
                    background-color: white;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                }
            """)

            # إنشاء تخطيط للنافذة
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة عنوان
            title_label = QLabel(window_title)
            title_label.setFont(QFont("Calibri", 18, QFont.Bold))
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # إطار إدخال المادة
            subject_frame = QFrame()
            subject_layout = QVBoxLayout(subject_frame)
            subject_layout.setContentsMargins(15, 15, 15, 15)
            subject_layout.setSpacing(15)

            # حقل اسم المادة
            subject_label = QLabel("اسم المادة:")
            subject_label.setFont(QFont("Calibri", 14, QFont.Bold))
            subject_layout.addWidget(subject_label)

            # تم إزالة مثال المادة

            # مربع نص المادة
            self.subject_input = QLineEdit()
            self.subject_input.setFont(QFont("Calibri", 14))
            self.subject_input.setMinimumHeight(40)

            # استخراج اسم المادة من النص الحالي (إذا وجد)
            subject_name = ""
            if current_text and "\n" in current_text:
                subject_name = current_text.split("\n")[0]
            else:
                subject_name = current_text

            self.subject_input.setText(subject_name)
            subject_layout.addWidget(self.subject_input)

            # إضافة مساحة
            subject_layout.addSpacing(10)

            # حقل التوقيت
            time_label = QLabel("التوقيت:")
            time_label.setFont(QFont("Calibri", 14, QFont.Bold))
            subject_layout.addWidget(time_label)

            # تم إزالة مثال التوقيت

            # مربع نص التوقيت
            self.time_input = QLineEdit()
            self.time_input.setFont(QFont("Calibri", 14))
            self.time_input.setMinimumHeight(40)

            # استخراج التوقيت من النص الحالي (إذا وجد)
            time_text = ""
            if current_text and "\n" in current_text and "(" in current_text and ")" in current_text:
                time_part = current_text.split("\n")[1]
                time_text = time_part.strip("()")
            else:
                # توقيت افتراضي حسب العمود
                default_times = ["", "08.00-10.00", "10.30-12.30", "14.30-16.30", "17.00-19.00"]
                time_text = default_times[column]

            self.time_input.setText(time_text)
            subject_layout.addWidget(self.time_input)

            # إضافة مساحة فارغة
            subject_layout.addStretch()

            layout.addWidget(subject_frame)

            # إضافة زر حفظ فقط
            button_layout = QHBoxLayout()

            # زر حفظ
            save_button = QPushButton("حفظ")
            save_button.setFont(QFont("Calibri", 16, QFont.Bold))
            save_button.setStyleSheet("""
                background-color: #2ecc71;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 150px;
                min-height: 50px;
            """)
            save_button.clicked.connect(dialog.accept)

            # إضافة مساحة قبل وبعد الزر للتوسيط
            button_layout.addStretch(1)
            button_layout.addWidget(save_button)
            button_layout.addStretch(1)

            layout.addLayout(button_layout)

            # عرض النافذة وانتظار النتيجة
            result = dialog.exec_()

            if result == QDialog.Accepted:
                # تنسيق النص
                subject_name = self.subject_input.text().strip()
                time_text = self.time_input.text().strip()

                if subject_name:
                    # تحديث الخلية
                    new_text = f"{subject_name}\n({time_text})"
                    current_item.setText(new_text)

    # تم إزالة دوال add_subject_to_list و remove_selected_subject و edit_selected_subject
    # تم إزالة دوال addExamDay و removeExamDay و printPreview و printTable
