#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        print("🔧 بدء إنشاء نافذة الاختبار...", flush=True)
        self.init_ui()
        print("✅ تم إنشاء نافذة الاختبار", flush=True)
        
        # اختبار مؤشر التحميل
        self.test_cursor()
        
    def init_ui(self):
        self.setWindowTitle("اختبار مؤشر التحميل")
        self.setGeometry(300, 300, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("اختبار مؤشر التحميل")
        title.setFont(QFont("Arial", 16))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # زر اختبار
        test_btn = QPushButton("اختبار مؤشر التحميل")
        test_btn.setFont(QFont("Arial", 12))
        test_btn.clicked.connect(self.test_cursor)
        layout.addWidget(test_btn)
        
        # زر تنظيف
        clear_btn = QPushButton("تنظيف المؤشرات")
        clear_btn.setFont(QFont("Arial", 12))
        clear_btn.clicked.connect(self.clear_cursors)
        layout.addWidget(clear_btn)
        
        # حالة
        self.status_label = QLabel("جاهز")
        self.status_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.status_label)
        
    def test_cursor(self):
        print("🔄 بدء اختبار مؤشر التحميل...", flush=True)
        self.status_label.setText("جاري الاختبار...")
        
        # إظهار مؤشر التحميل
        QApplication.setOverrideCursor(Qt.WaitCursor)
        print("⏳ تم عرض مؤشر التحميل", flush=True)
        
        # محاكاة عملية طويلة
        QApplication.processEvents()
        time.sleep(2)
        
        # إزالة مؤشر التحميل
        QApplication.restoreOverrideCursor()
        print("✅ تم إزالة مؤشر التحميل", flush=True)
        
        self.status_label.setText("تم الانتهاء من الاختبار")
        
    def clear_cursors(self):
        print("🔧 تنظيف جميع المؤشرات...", flush=True)
        self.status_label.setText("جاري التنظيف...")
        
        # إزالة جميع المؤشرات المتراكمة
        for i in range(10):
            try:
                QApplication.restoreOverrideCursor()
                print(f"   🔄 إزالة مؤشر {i+1}", flush=True)
            except:
                break
                
        # تعيين المؤشر العادي
        QApplication.setOverrideCursor(Qt.ArrowCursor)
        QApplication.restoreOverrideCursor()
        
        print("✅ تم تنظيف جميع المؤشرات", flush=True)
        self.status_label.setText("تم تنظيف المؤشرات")

def main():
    print("🚀 بدء تشغيل نافذة الاختبار...", flush=True)
    app = QApplication(sys.argv)
    print("✅ تم إنشاء التطبيق", flush=True)
    
    window = TestWindow()
    print("✅ تم إنشاء النافذة", flush=True)
    
    window.show()
    print("✅ تم عرض النافذة", flush=True)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
