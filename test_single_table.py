#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استخدام جدول تدوين_الغياب فقط
"""
import sqlite3
import sys
from datetime import datetime

def test_single_table():
    """اختبار استخدام جدول واحد فقط"""
    try:
        print("🔧 اختبار استخدام جدول تدوين_الغياب فقط...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول
        print("\n📊 فحص الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        table_names = [table[0] for table in tables]
        print(f"   الجداول الموجودة: {len(table_names)}")
        
        # التحقق من جدول تدوين_الغياب
        if 'تدوين_الغياب' in table_names:
            print("   ✅ جدول تدوين_الغياب موجود")
            
            # عرض بنية الجدول
            cursor.execute("PRAGMA table_info(تدوين_الغياب)")
            columns = cursor.fetchall()
            print("   📋 أعمدة جدول تدوين_الغياب:")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
            
            # عد السجلات
            cursor.execute("SELECT COUNT(*) FROM تدوين_الغياب")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد سجلات الغياب: {count}")
            
            if count > 0:
                # عرض آخر 3 سجلات
                cursor.execute("""
                    SELECT اسم_التلميذ, تاريخ_الغياب, عدد_الحصص_المتغيب_عنها
                    FROM تدوين_الغياب
                    ORDER BY id DESC
                    LIMIT 3
                """)
                recent = cursor.fetchall()
                print("   📝 آخر سجلات الغياب:")
                for record in recent:
                    print(f"      • {record[0]} - {record[1]} - {record[2]} حصة")
        else:
            print("   ❌ جدول تدوين_الغياب غير موجود")
        
        # التحقق من جدول absence_records
        if 'absence_records' in table_names:
            print("   ⚠️ جدول absence_records ما زال موجود (لكن لا نستخدمه)")
            cursor.execute("SELECT COUNT(*) FROM absence_records")
            old_count = cursor.fetchone()[0]
            print(f"      عدد السجلات القديمة: {old_count}")
        else:
            print("   ✅ جدول absence_records غير موجود (جيد)")
        
        # اختبار حساب الغياب من جدول تدوين_الغياب
        print("\n🧮 اختبار حساب الغياب:")
        cursor.execute("SELECT id, اسم_التلميذ FROM جدول_البيانات LIMIT 3")
        sample_students = cursor.fetchall()
        
        for student_id, student_name in sample_students:
            # حساب الغياب من جدول تدوين_الغياب
            cursor.execute("""
                SELECT SUM(عدد_الحصص_المتغيب_عنها)
                FROM تدوين_الغياب
                WHERE معرف_التلميذ = ?
            """, (student_id,))
            
            result = cursor.fetchone()[0]
            absence_count = result if result is not None else 0
            
            print(f"   👤 {student_name}: {absence_count} حصة غياب")
        
        # اختبار إدراج سجل جديد
        print("\n➕ اختبار إدراج سجل غياب جديد:")
        if sample_students:
            test_student = sample_students[0]
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            # التحقق من وجود سجل لنفس اليوم
            cursor.execute("""
                SELECT id FROM تدوين_الغياب
                WHERE معرف_التلميذ = ? AND تاريخ_الغياب = ?
            """, (test_student[0], current_date))
            
            existing = cursor.fetchone()
            
            if existing:
                print(f"   ⚠️ يوجد سجل غياب لـ {test_student[1]} في {current_date}")
            else:
                # إدراج سجل تجريبي
                cursor.execute("""
                    INSERT INTO تدوين_الغياب
                    (معرف_التلميذ, اسم_التلميذ, رمز_التلميذ, القسم, تاريخ_الغياب,
                     الحصة_الاولى, الحصة_الثانية, الحصة_الثالثة, عدد_الحصص_المتغيب_عنها, ملاحظات)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (test_student[0], test_student[1], "TEST", "اختبار", current_date,
                      1, 0, 1, 2, "سجل اختبار"))
                
                conn.commit()
                print(f"   ✅ تم إدراج سجل غياب تجريبي لـ {test_student[1]}")
                
                # حساب الغياب مرة أخرى
                cursor.execute("""
                    SELECT SUM(عدد_الحصص_المتغيب_عنها)
                    FROM تدوين_الغياب
                    WHERE معرف_التلميذ = ?
                """, (test_student[0],))
                
                new_result = cursor.fetchone()[0]
                new_absence_count = new_result if new_result is not None else 0
                print(f"   📊 الغياب الجديد لـ {test_student[1]}: {new_absence_count} حصة")
        
        conn.close()
        print("\n✅ اختبار الجدول الواحد مكتمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار استخدام جدول واحد فقط...")
    success = test_single_table()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تشغيل النافذة الرئيسية:")
        print("   python attendance_processing_window.py")
    else:
        print("\n💥 فشل في بعض الاختبارات")
        sys.exit(1)
