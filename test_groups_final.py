#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_groups_table():
    """اختبار جدول المجموعات"""
    print("🧪 اختبار جدول المجموعات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # جلب جميع المجموعات
            cursor.execute("SELECT * FROM المجموعات")
            all_groups = cursor.fetchall()
            print(f"📊 إجمالي السجلات: {len(all_groups)}")
            
            if all_groups:
                print("\n📋 جميع المجموعات:")
                for i, group in enumerate(all_groups, 1):
                    print(f"   {i:2d}. ID: {group[0]}, الاسم: {group[1]}")
            
            # جلب المجموعات المتاحة (نفس الاستعلام المستخدم في التطبيق)
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            available_groups = [row[0] for row in cursor.fetchall()]
            
            print(f"\n🎯 المجموعات المتاحة ({len(available_groups)}):")
            for i, group in enumerate(available_groups, 1):
                print(f"   {i}. {group}")
                
        else:
            print("❌ جدول المجموعات غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_groups_table()
