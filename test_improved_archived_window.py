#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_improved_archived_window():
    """اختبار النافذة المحسنة لإدارة الحسابات المرحلة"""
    print("🎨 اختبار النافذة المحسنة لإدارة الحسابات المرحلة")
    print("=" * 60)
    
    try:
        # فحص وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            print("💡 قم بتشغيل التطبيق الرئيسي أولاً لإنشاء قاعدة البيانات")
            return
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # اختبار استيراد النافذة
        print("\n🔧 اختبار استيراد النافذة المحسنة:")
        try:
            from archived_accounts_window import ArchivedAccountsWindow
            print("   ✅ تم استيراد النافذة بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
            return
        
        # إنشاء التطبيق
        print("\n🖥️ إنشاء التطبيق:")
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        print("   ✅ تم إنشاء التطبيق")
        
        # إنشاء النافذة
        print("\n🏗️ إنشاء النافذة:")
        try:
            window = ArchivedAccountsWindow()
            print("   ✅ تم إنشاء النافذة بنجاح")
            
            # فحص المكونات
            print("\n🔍 فحص مكونات النافذة:")
            
            # فحص الجدول
            if hasattr(window, 'table'):
                print(f"   ✅ الجدول موجود - عدد الأعمدة: {window.table.columnCount()}")
                headers = []
                for i in range(window.table.columnCount()):
                    header = window.table.horizontalHeaderItem(i)
                    if header:
                        headers.append(header.text())
                print(f"   📋 رؤوس الأعمدة: {headers}")
            else:
                print("   ❌ الجدول غير موجود")
            
            # فحص أزرار الترحيل
            if hasattr(window, 'month_combo') and hasattr(window, 'year_spin'):
                print("   ✅ عناصر اختيار التاريخ موجودة")
                print(f"   📅 الشهر الحالي: {window.month_combo.currentText()}")
                print(f"   📅 السنة الحالية: {window.year_spin.value()}")
            else:
                print("   ❌ عناصر اختيار التاريخ مفقودة")
            
            # فحص شريط البحث
            if hasattr(window, 'search_box'):
                print("   ✅ مربع البحث موجود")
            else:
                print("   ❌ مربع البحث مفقود")
            
            # فحص لوحة الإحصائيات
            stats_labels = [
                'total_archived_label', 'total_months_label', 
                'total_amount_label', 'db_size_label'
            ]
            stats_found = sum(1 for label in stats_labels if hasattr(window, label))
            print(f"   📊 لوحة الإحصائيات: {stats_found}/{len(stats_labels)} عنصر موجود")
            
            # فحص شريط المعلومات
            info_labels = ['db_info', 'last_archive_info']
            info_found = sum(1 for label in info_labels if hasattr(window, label))
            print(f"   ℹ️ شريط المعلومات: {info_found}/{len(info_labels)} عنصر موجود")
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء النافذة: {e}")
            return
        
        # اختبار تحميل البيانات
        print("\n📊 اختبار تحميل البيانات:")
        try:
            window.load_archived_months()
            row_count = window.table.rowCount()
            print(f"   ✅ تم تحميل البيانات - عدد الصفوف: {row_count}")
            
            if row_count > 0:
                print("   📝 عينة من البيانات:")
                for row in range(min(3, row_count)):
                    month_item = window.table.item(row, 0)
                    year_item = window.table.item(row, 1)
                    count_item = window.table.item(row, 2)
                    
                    if month_item and year_item and count_item:
                        print(f"      {row+1}. {month_item.text()}/{year_item.text()} - {count_item.text()} سجل")
            else:
                print("   ⚠️ لا توجد بيانات مرحلة")
                
        except Exception as e:
            print(f"   ❌ خطأ في تحميل البيانات: {e}")
        
        # اختبار الوظائف الجديدة
        print("\n🔧 اختبار الوظائف الجديدة:")
        
        # اختبار تحديث الإحصائيات
        try:
            window.update_stats_panel()
            print("   ✅ تحديث لوحة الإحصائيات يعمل")
        except Exception as e:
            print(f"   ❌ خطأ في تحديث الإحصائيات: {e}")
        
        # اختبار تحديث شريط المعلومات
        try:
            window.update_info_bar()
            print("   ✅ تحديث شريط المعلومات يعمل")
        except Exception as e:
            print(f"   ❌ خطأ في تحديث شريط المعلومات: {e}")
        
        # اختبار البحث
        try:
            if hasattr(window, 'search_box'):
                window.search_box.setText("يناير")
                window.filter_table()
                print("   ✅ وظيفة البحث تعمل")
            else:
                print("   ⚠️ مربع البحث غير موجود")
        except Exception as e:
            print(f"   ❌ خطأ في وظيفة البحث: {e}")
        
        # عرض النافذة للاختبار البصري
        print(f"\n🖼️ عرض النافذة للاختبار البصري:")
        print("   💡 ستظهر النافذة لمدة 10 ثوان للمراجعة البصرية")
        
        window.show()
        
        # تشغيل حلقة الأحداث لفترة قصيرة
        import time
        start_time = time.time()
        while time.time() - start_time < 10:  # 10 ثوان
            app.processEvents()
            time.sleep(0.1)
        
        window.close()
        
        print("   ✅ تم إغلاق النافذة")
        
        # النتيجة النهائية
        print(f"\n🎯 النتيجة النهائية:")
        print("=" * 40)
        print("✅ النافذة المحسنة تعمل بشكل صحيح")
        print("✅ جميع المكونات الجديدة موجودة")
        print("✅ الوظائف الأساسية تعمل")
        print("✅ التصميم محسن ومنظم")
        
        print(f"\n💡 المزايا الجديدة:")
        print("   🎨 تصميم احترافي محسن")
        print("   📊 لوحة إحصائيات تفاعلية")
        print("   🔍 وظيفة البحث والتصفية")
        print("   📈 أعمدة إضافية مفيدة")
        print("   🔄 ترحيل السنة كاملة")
        print("   📤 خيارات التصدير (قريباً)")
        print("   🧹 تنظيف البيانات (قريباً)")
        
        print(f"\n🚀 للاستخدام:")
        print("   python archived_accounts_window.py")
        print("   أو من النافذة الرئيسية: زر 'إدارة الحسابات المرحلة'")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_archived_window()
