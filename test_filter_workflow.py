#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_filter_workflow():
    """اختبار سير عمل التصفية الكامل"""
    print("🔄 اختبار سير عمل التصفية الكامل")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # الخطوة 1: تحميل المجموعات من جدول_المواد_والاقسام
        print("1️⃣ تحميل المجموعات من جدول_المواد_والاقسام...")
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        print(f"   ✅ تم تحميل {len(filter_groups)} مجموعة: {filter_groups}")
        
        # الخطوة 2: لكل مجموعة، تحميل الأقسام المرتبطة
        for group in filter_groups:
            print(f"\n2️⃣ تحميل الأقسام للمجموعة: {group}")
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_المواد_والاقسام 
                WHERE المجموعة = ? AND القسم IS NOT NULL 
                ORDER BY القسم
            """, (group,))
            sections = [row[0] for row in cursor.fetchall()]
            print(f"   ✅ الأقسام المتاحة: {sections}")
            
            # الخطوة 3: لكل قسم، البحث عن الطلاب في جدول_البيانات
            for section in sections:
                print(f"\n3️⃣ البحث عن الطلاب في المجموعة '{group}' والقسم '{section}'")
                
                # البحث بالمجموعة والقسم
                cursor.execute("""
                    SELECT id, اسم_التلميذ, رمز_التلميذ, اسم_المجموعة, القسم
                    FROM جدول_البيانات 
                    WHERE اسم_المجموعة = ? AND القسم = ?
                    ORDER BY id DESC
                """, (group, section))
                students = cursor.fetchall()
                
                print(f"   📊 عدد الطلاب الموجودين: {len(students)}")
                if students:
                    print("   📋 الطلاب:")
                    for student in students:
                        print(f"      - ID: {student[0]}, الاسم: {student[1]}, الرمز: {student[2]}")
                else:
                    print("   ⚠️ لا يوجد طلاب في هذا القسم")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_data_consistency():
    """اختبار تطابق البيانات بين الجدولين"""
    print("\n🔍 اختبار تطابق البيانات بين الجدولين")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب المجموعات من جدول_المواد_والاقسام
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL")
        filter_groups = set(row[0] for row in cursor.fetchall())
        
        # جلب المجموعات من جدول_البيانات
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL")
        data_groups = set(row[0] for row in cursor.fetchall())
        
        print(f"📋 مجموعات التصفية (جدول_المواد_والاقسام): {filter_groups}")
        print(f"📋 مجموعات البيانات (جدول_البيانات): {data_groups}")
        
        # التحقق من التطابق
        common_groups = filter_groups & data_groups
        filter_only = filter_groups - data_groups
        data_only = data_groups - filter_groups
        
        print(f"\n✅ المجموعات المشتركة: {common_groups}")
        if filter_only:
            print(f"⚠️ مجموعات في التصفية فقط: {filter_only}")
        if data_only:
            print(f"⚠️ مجموعات في البيانات فقط: {data_only}")
        
        # نفس الشيء للأقسام
        cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL")
        filter_sections = set(row[0] for row in cursor.fetchall())
        
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL")
        data_sections = set(row[0] for row in cursor.fetchall())
        
        print(f"\n📋 أقسام التصفية (جدول_المواد_والاقسام): {filter_sections}")
        print(f"📋 أقسام البيانات (جدول_البيانات): {data_sections}")
        
        common_sections = filter_sections & data_sections
        filter_sections_only = filter_sections - data_sections
        data_sections_only = data_sections - filter_sections
        
        print(f"\n✅ الأقسام المشتركة: {common_sections}")
        if filter_sections_only:
            print(f"⚠️ أقسام في التصفية فقط: {filter_sections_only}")
        if data_sections_only:
            print(f"⚠️ أقسام في البيانات فقط: {data_sections_only}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التطابق: {e}")

def simulate_section_filter_change():
    """محاكاة تغيير تصفية القسم"""
    print("\n🔄 محاكاة تغيير تصفية القسم")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # محاكاة اختيار مجموعة
        selected_group = "مجموعة مفتوحة"
        print(f"1️⃣ تم اختيار المجموعة: {selected_group}")
        
        # تحميل الأقسام للمجموعة المحددة
        cursor.execute("""
            SELECT DISTINCT القسم 
            FROM جدول_المواد_والاقسام 
            WHERE المجموعة = ? AND القسم IS NOT NULL 
            ORDER BY القسم
        """, (selected_group,))
        available_sections = [row[0] for row in cursor.fetchall()]
        print(f"2️⃣ الأقسام المتاحة: {available_sections}")
        
        # محاكاة اختيار قسم
        if available_sections:
            selected_section = available_sections[0]
            print(f"3️⃣ تم اختيار القسم: {selected_section}")
            
            # تطبيق التصفية (محاكاة apply_filters)
            print("4️⃣ تطبيق التصفية...")
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                       القسم, اجمالي_مبلغ_التسجيل, 
                       الواجب_الشهري, المبلغ_النهائي_الشهري
                FROM جدول_البيانات
                WHERE اسم_المجموعة = ? AND القسم = ?
                ORDER BY id DESC
            """, (selected_group, selected_section))
            
            filtered_records = cursor.fetchall()
            print(f"5️⃣ نتائج التصفية: {len(filtered_records)} سجل")
            
            if filtered_records:
                print("📋 السجلات المفلترة:")
                for record in filtered_records:
                    print(f"   - ID: {record[0]}, الاسم: {record[1]}, القسم: {record[5]}")
            else:
                print("⚠️ لا توجد سجلات تطابق التصفية")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المحاكاة: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لسير عمل التصفية")
    print("=" * 60)
    
    test_filter_workflow()
    test_data_consistency()
    simulate_section_filter_change()
