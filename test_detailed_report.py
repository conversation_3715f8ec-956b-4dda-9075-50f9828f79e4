#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from datetime import datetime

def test_detailed_report():
    """اختبار وظيفة طباعة التقرير المفصل"""
    
    print("🔍 اختبار وظيفة طباعة التقرير المفصل...")
    
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        # فحص الجداول المطلوبة
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص جدول جدول_البيانات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_البيانات'")
        if not cursor.fetchone():
            print("❌ جدول جدول_البيانات غير موجود")
            conn.close()
            return False
        
        print("✅ جدول جدول_البيانات موجود")
        
        # فحص عدد التلاميذ
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        student_count = cursor.fetchone()[0]
        print(f"📊 عدد التلاميذ في قاعدة البيانات: {student_count}")
        
        if student_count == 0:
            print("⚠️ لا يوجد تلاميذ في قاعدة البيانات")
            conn.close()
            return False
        
        # جلب أول تلميذ للاختبار
        cursor.execute("SELECT id, اسم_التلميذ FROM جدول_البيانات LIMIT 1")
        student = cursor.fetchone()
        student_id = student[0]
        student_name = student[1]
        
        print(f"🎯 اختبار التقرير للتلميذ: {student_name} (ID: {student_id})")
        
        # فحص جدول registration_fees
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='registration_fees'")
        if cursor.fetchone():
            print("✅ جدول registration_fees موجود")
            cursor.execute("SELECT COUNT(*) FROM registration_fees WHERE student_id = ?", (student_id,))
            reg_count = cursor.fetchone()[0]
            print(f"📋 عدد دفعات التسجيل للتلميذ: {reg_count}")
        else:
            print("⚠️ جدول registration_fees غير موجود - سيتم إنشاؤه تلقائياً")
        
        # فحص جدول monthly_duties
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_duties'")
        if cursor.fetchone():
            print("✅ جدول monthly_duties موجود")
            cursor.execute("SELECT COUNT(*) FROM monthly_duties WHERE student_id = ?", (student_id,))
            monthly_count = cursor.fetchone()[0]
            print(f"📅 عدد الواجبات الشهرية للتلميذ: {monthly_count}")
        else:
            print("⚠️ جدول monthly_duties غير موجود - سيتم إنشاؤه تلقائياً")
        
        # فحص جدول بيانات_المؤسسة للشعار
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
        if cursor.fetchone():
            print("✅ جدول بيانات_المؤسسة موجود")
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_result = cursor.fetchone()
            if logo_result and logo_result[0] and os.path.exists(logo_result[0]):
                print(f"🖼️ شعار المؤسسة موجود: {logo_result[0]}")
            else:
                print("⚠️ شعار المؤسسة غير موجود أو المسار غير صحيح")
        else:
            print("⚠️ جدول بيانات_المؤسسة غير موجود")
        
        conn.close()
        
        # اختبار استيراد وحدة print101
        try:
            from print101 import print_student_detailed_report
            print("✅ تم استيراد وحدة print101 بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد وحدة print101: {e}")
            return False
        
        # اختبار إنشاء التقرير
        print(f"🚀 بدء إنشاء التقرير المفصل للتلميذ ID: {student_id}")
        
        success, output_path, message = print_student_detailed_report(
            parent=None, 
            student_id=student_id
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            print(f"💬 الرسالة: {message}")
            
            # التحقق من وجود الملف
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 حجم الملف: {file_size} بايت")
                return True
            else:
                print("❌ الملف غير موجود رغم نجاح العملية")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار وظيفة طباعة التقرير المفصل")
    print("=" * 60)
    
    result = test_detailed_report()
    
    print("=" * 60)
    if result:
        print("🎉 الاختبار نجح! زر طباعة التقرير المفصل يعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! يحتاج زر طباعة التقرير المفصل إلى إصلاح")
    print("=" * 60)
