# 📋 دليل النظام المحسن لمعالجة الغياب

## 🎯 التحسينات المطبقة

### ✅ **1. ترتيب الأعمدة في الجدول الثاني (معكوس)**
```
الأسبوع الخامس → الأسبوع الرابع → الأسبوع الثالث → الأسبوع الثاني → الأسبوع الأول → اسم التلميذ → الرمز → رقم الترتيب
```

### ✅ **2. رقم الترتيب في سطرين**
- العمود الأخير يعرض "رقم\nالترتيب" في سطرين
- تحسين استغلال المساحة

### ✅ **3. تواريخ الأسابيع (يوم الاثنين)**
- بدلاً من "الأسبوع الأول" → "الأسبوع 1\n15/12"
- التاريخ يشير إلى يوم الاثنين من كل أسبوع

### ✅ **4. جداول الغياب المطلوبة**
- `daily_absence`: تسجيل الغياب اليومي بالحصص
- `monthly_absence_stats`: إحصائيات شهرية
- `yearly_absence_stats`: إحصائيات سنوية

### ✅ **5. تقارير ورقية شاملة**
- تقرير الغياب الشهري
- تقرير الغياب السنوي
- تقرير الغياب حسب التلميذ

### ✅ **6. تحكم يدوي كامل**
- عرض الأعمدة بالمليمتر
- ارتفاع الصفوف بالمليمتر
- نمط وسمك الحدود
- ترتيب الأعمدة

---

## 📁 الملفات الجديدة

### **1. `improved_attendance_report.py`**
- التقرير المحسن مع الترتيب الجديد
- إعدادات قابلة للتعديل في الأعلى
- دعم النص العربي

### **2. `fix_absence_table.py`**
- إنشاء جداول الغياب المطلوبة
- إضافة بيانات تجريبية
- تحديث الإحصائيات

### **3. `absence_reports_generator.py`**
- مولد التقارير الورقية
- تقارير شهرية وسنوية وفردية
- تصدير PDF احترافي

### **4. `test_improved_system.py`**
- اختبار شامل للنظام المحسن
- فحص جميع المكونات
- دليل الاستخدام

### **5. `دليل_تعديل_التقرير.md`**
- دليل تفصيلي للتعديل اليدوي
- أمثلة عملية
- نصائح مهمة

---

## 🚀 خطوات التشغيل

### **المرحلة 1: الإعداد الأولي**
```bash
# 1. إصلاح جداول الغياب
python fix_absence_table.py

# 2. اختبار النظام
python test_improved_system.py
```

### **المرحلة 2: الاستخدام اليومي**
```bash
# تشغيل نافذة معالجة الغياب
python attendance_processing_window.py
```

### **المرحلة 3: التقارير**
```bash
# التقرير المحسن (ورقة المتابعة)
python improved_attendance_report.py

# التقارير الورقية (شهري/سنوي/فردي)
python absence_reports_generator.py
```

---

## ⚙️ التخصيص والتعديل

### **تعديل التقرير المحسن:**
افتح `improved_attendance_report.py` وعدل الإعدادات:

```python
# ترتيب الأعمدة (السطر 8-16)
COLUMN_ORDER = [
    'week5', 'week4', 'week3', 'week2', 'week1',
    'name', 'id', 'order'
]

# عرض الأعمدة (السطر 18-25)
COLUMN_WIDTHS = {
    'name': 50,       # اسم التلميذ
    'id': 25,         # الرمز
    'order': 20,      # رقم الترتيب
    'week': 25,       # كل أسبوع
    'session': 8      # كل حصة
}

# ارتفاع الصفوف (السطر 27-33)
ROW_HEIGHTS = {
    'header_main': 20,      # رأس الجدول الرئيسي
    'header_sub': 10,       # رأس الجدول الفرعي
    'student_row': 12,      # صف التلميذ
    'info_row': 10          # صف معلومات القسم
}
```

---

## 📊 استخدام التقارير

### **1. ورقة المتابعة المحسنة:**
- اختر قسم من النافذة الرئيسية
- اضغط "📄 تقرير مباشر"
- سيتم إنشاء ورقة بالتخطيط الجديد

### **2. التقارير الورقية:**
```python
# تقرير شهري
from absence_reports_generator import generate_monthly_absence_report
pdf, data, msg = generate_monthly_absence_report(2024, 12, "قسم / 01")

# تقرير سنوي
from absence_reports_generator import generate_yearly_absence_report
pdf, data, msg = generate_yearly_absence_report(2024, "قسم / 01")

# تقرير فردي
from absence_reports_generator import generate_student_absence_report
pdf, data, msg = generate_student_absence_report(student_id)
```

---

## 🔧 حل المشاكل الشائعة

### **مشكلة: "no such table: daily_absence"**
```bash
# الحل: تشغيل إصلاح الجداول
python fix_absence_table.py
```

### **مشكلة: "لا توجد بيانات للتقرير"**
```bash
# الحل: إضافة بيانات تجريبية
python fix_absence_table.py
```

### **مشكلة: النص العربي لا يظهر بشكل صحيح**
```bash
# الحل: تثبيت مكتبات النص العربي
pip install arabic-reshaper python-bidi
```

### **مشكلة: خطأ في إنشاء PDF**
```bash
# الحل: تثبيت مكتبة FPDF
pip install fpdf2
```

---

## 📈 الميزات الجديدة

### **✅ جدول الغياب اليومي:**
- تسجيل الغياب بالحصص (ح1، ح2، ح3)
- ربط بمعرف التلميذ والقسم
- تاريخ الغياب والملاحظات

### **✅ الإحصائيات التلقائية:**
- حساب نسبة الحضور شهرياً
- حساب نسبة الحضور سنوياً
- تحديث تلقائي للإحصائيات

### **✅ التقارير المتقدمة:**
- تقارير PDF احترافية
- تخطيط قابل للتخصيص
- دعم كامل للنص العربي

### **✅ سهولة الاستخدام:**
- واجهة محسنة
- أزرار واضحة
- رسائل تأكيد

---

## 🎯 النتائج المتوقعة

### **الجدول الثاني المحسن:**
```
| الأسبوع 5 | الأسبوع 4 | الأسبوع 3 | الأسبوع 2 | الأسبوع 1 | اسم التلميذ | الرمز | رقم  |
| 22/12    | 15/12    | 08/12    | 01/12    | 24/11    |           |      | الترتيب |
|----------|----------|----------|----------|----------|-----------|------|------|
| ح1|ح2|ح3 | ح1|ح2|ح3 | ح1|ح2|ح3 | ح1|ح2|ح3 | ح1|ح2|ح3 | أحمد علي  | 001  |  1   |
```

### **التقارير الورقية:**
- **شهري**: إحصائيات جميع التلاميذ لشهر محدد
- **سنوي**: إحصائيات جميع التلاميذ لسنة كاملة
- **فردي**: تفاصيل غياب تلميذ محدد

### **الإعدادات المرنة:**
- تحكم كامل في التخطيط
- تعديل سهل للأبعاد
- تخصيص الألوان والحدود

---

## 📞 الدعم

في حالة وجود مشاكل:
1. **شغل الاختبار الشامل**: `python test_improved_system.py`
2. **راجع رسائل الخطأ** في وحدة التحكم
3. **تأكد من تثبيت المكتبات** المطلوبة
4. **استعد النسخة الاحتياطية** إذا لزم الأمر

---

**🌟 النظام المحسن جاهز للاستخدام مع جميع التحسينات المطلوبة!**
