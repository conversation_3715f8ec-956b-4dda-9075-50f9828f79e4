#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    print("🔍 فحص هيكل قاعدة البيانات")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص جدول_البيانات
        print("📋 أعمدة جدول_البيانات:")
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        columns = cursor.fetchall()
        
        for i, col in enumerate(columns):
            col_id, col_name, col_type, not_null, default_val, pk = col
            print(f"  {i:2d}. {col_name} ({col_type})")
        
        print(f"\nإجمالي الأعمدة: {len(columns)}")
        
        # البحث عن أعمدة الواجبات
        print("\n🔍 البحث عن أعمدة الواجبات:")
        monthly_columns = []
        for col in columns:
            col_name = col[1].lower()
            if 'واجب' in col_name or 'شهري' in col_name or 'مبلغ' in col_name:
                monthly_columns.append(col[1])
                print(f"  ✓ {col[1]}")
        
        if not monthly_columns:
            print("  ❌ لم يتم العثور على أعمدة الواجبات")
        
        # اختبار استعلام بسيط
        print("\n🧪 اختبار استعلام بسيط:")
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        count = cursor.fetchone()[0]
        print(f"عدد السجلات: {count}")
        
        if count > 0:
            # جلب أول سجل للاختبار
            cursor.execute("SELECT * FROM جدول_البيانات LIMIT 1")
            first_record = cursor.fetchone()
            print(f"أول سجل: {len(first_record)} عمود")
            
            # عرض أول 5 قيم
            print("أول 5 قيم:")
            for i, val in enumerate(first_record[:5]):
                col_name = columns[i][1] if i < len(columns) else f"عمود_{i}"
                print(f"  {col_name}: {val}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def test_monthly_duty_query():
    """اختبار استعلام الواجب الشهري"""
    print("\n💰 اختبار استعلام الواجب الشهري")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار استعلامات مختلفة
        test_queries = [
            "SELECT اسم_التلميذ, رمز_التلميذ, القسم, الواجب_الشهري FROM جدول_البيانات LIMIT 1",
            "SELECT اسم_التلميذ, رمز_التلميذ, القسم, المبلغ_النهائي_الشهري FROM جدول_البيانات LIMIT 1",
            "SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات LIMIT 1"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\nاختبار {i}:")
            print(f"الاستعلام: {query}")
            
            try:
                cursor.execute(query)
                result = cursor.fetchone()
                if result:
                    print(f"✅ نجح - النتيجة: {result}")
                    print(f"عدد الأعمدة: {len(result)}")
                else:
                    print("⚠️ لا توجد نتائج")
            except Exception as e:
                print(f"❌ فشل: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستعلام: {e}")

def suggest_fix():
    """اقتراح الحل"""
    print("\n💡 اقتراح الحل")
    print("=" * 30)
    
    print("بناءً على الفحص، يمكن حل المشكلة بإحدى الطرق التالية:")
    print("1. استخدام COALESCE للبحث في أعمدة متعددة")
    print("2. استخدام المبلغ المدفوع كافتراضي إذا لم يوجد واجب شهري")
    print("3. إضافة معالجة آمنة للفهارس")
    
    print("\nالكود المقترح:")
    print("""
    cursor.execute('''
        SELECT اسم_التلميذ, رمز_التلميذ, القسم, 
               COALESCE(الواجب_الشهري, المبلغ_النهائي_الشهري, 0) as monthly_amount
        FROM جدول_البيانات
        WHERE id = ?
    ''', (student_id,))
    """)

if __name__ == "__main__":
    check_database_structure()
    test_monthly_duty_query()
    suggest_fix()
