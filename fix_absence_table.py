#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح جدول الغياب وإنشاء جداول التقارير
"""

import sqlite3
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_absence_tables():
    """إنشاء جداول الغياب المطلوبة"""
    try:
        print("🔧 بدء إنشاء جداول الغياب...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. جدول الغياب اليومي (للتسجيل)
        print("📋 إنشاء جدول الغياب اليومي...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS daily_absence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                student_name TEXT,
                student_code TEXT,
                section TEXT,
                absence_date DATE,
                session_1 INTEGER DEFAULT 0,  -- الحصة الأولى (0=حاضر، 1=غائب)
                session_2 INTEGER DEFAULT 0,  -- الحصة الثانية
                session_3 INTEGER DEFAULT 0,  -- الحصة الثالثة
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        
        # 2. جدول إحصائيات الغياب الشهرية
        print("📊 إنشاء جدول إحصائيات الغياب الشهرية...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monthly_absence_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                student_name TEXT,
                student_code TEXT,
                section TEXT,
                year INTEGER,
                month INTEGER,
                total_sessions INTEGER DEFAULT 0,    -- إجمالي الحصص
                absent_sessions INTEGER DEFAULT 0,   -- الحصص المتغيب عنها
                attendance_rate REAL DEFAULT 100.0,  -- نسبة الحضور
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        
        # 3. جدول إحصائيات الغياب السنوية
        print("📈 إنشاء جدول إحصائيات الغياب السنوية...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS yearly_absence_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                student_name TEXT,
                student_code TEXT,
                section TEXT,
                year INTEGER,
                total_sessions INTEGER DEFAULT 0,
                absent_sessions INTEGER DEFAULT 0,
                attendance_rate REAL DEFAULT 100.0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        
        # إنشاء الفهارس
        print("🔍 إنشاء الفهارس...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_daily_absence_date ON daily_absence(absence_date)",
            "CREATE INDEX IF NOT EXISTS idx_daily_absence_student ON daily_absence(student_id)",
            "CREATE INDEX IF NOT EXISTS idx_monthly_stats_student ON monthly_absence_stats(student_id, year, month)",
            "CREATE INDEX IF NOT EXISTS idx_yearly_stats_student ON yearly_absence_stats(student_id, year)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جداول الغياب بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول الغياب: {e}")
        return False

def add_sample_absence_data():
    """إضافة بيانات غياب تجريبية"""
    try:
        print("\n📝 إضافة بيانات غياب تجريبية...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب بعض التلاميذ
        cursor.execute("SELECT id, اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات LIMIT 10")
        students = cursor.fetchall()
        
        if not students:
            print("⚠️ لا توجد بيانات تلاميذ")
            return False
        
        # إضافة غياب لآخر 30 يوم
        today = datetime.now()
        sample_data = []
        
        for i in range(30):
            date = today - timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            
            # اختيار بعض التلاميذ للغياب عشوائياً
            for j, student in enumerate(students[:5]):  # أول 5 تلاميذ
                if (i + j) % 3 == 0:  # غياب متقطع
                    sample_data.append((
                        student[0],  # student_id
                        student[1],  # student_name
                        student[2],  # student_code
                        student[3],  # section
                        date_str,    # absence_date
                        1 if (i + j) % 2 == 0 else 0,  # session_1
                        1 if (i + j) % 3 == 0 else 0,  # session_2
                        1 if (i + j) % 4 == 0 else 0,  # session_3
                        f"غياب تجريبي - {date_str}"     # notes
                    ))
        
        cursor.executemany("""
            INSERT INTO daily_absence 
            (student_id, student_name, student_code, section, absence_date, 
             session_1, session_2, session_3, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, sample_data)
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة {len(sample_data)} سجل غياب تجريبي")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        return False

def update_absence_statistics():
    """تحديث إحصائيات الغياب"""
    try:
        print("\n📊 تحديث إحصائيات الغياب...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # تحديث الإحصائيات الشهرية
        cursor.execute("""
            INSERT OR REPLACE INTO monthly_absence_stats 
            (student_id, student_name, student_code, section, year, month, 
             total_sessions, absent_sessions, attendance_rate)
            SELECT 
                student_id,
                student_name,
                student_code,
                section,
                strftime('%Y', absence_date) as year,
                strftime('%m', absence_date) as month,
                COUNT(*) * 3 as total_sessions,
                SUM(session_1 + session_2 + session_3) as absent_sessions,
                ROUND(100.0 - (SUM(session_1 + session_2 + session_3) * 100.0 / (COUNT(*) * 3)), 2) as attendance_rate
            FROM daily_absence
            GROUP BY student_id, strftime('%Y', absence_date), strftime('%m', absence_date)
        """)
        
        # تحديث الإحصائيات السنوية
        cursor.execute("""
            INSERT OR REPLACE INTO yearly_absence_stats 
            (student_id, student_name, student_code, section, year, 
             total_sessions, absent_sessions, attendance_rate)
            SELECT 
                student_id,
                student_name,
                student_code,
                section,
                strftime('%Y', absence_date) as year,
                COUNT(*) * 3 as total_sessions,
                SUM(session_1 + session_2 + session_3) as absent_sessions,
                ROUND(100.0 - (SUM(session_1 + session_2 + session_3) * 100.0 / (COUNT(*) * 3)), 2) as attendance_rate
            FROM daily_absence
            GROUP BY student_id, strftime('%Y', absence_date)
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ تم تحديث الإحصائيات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإحصائيات: {e}")
        return False

def test_absence_tables():
    """اختبار جداول الغياب"""
    try:
        print("\n🔍 اختبار جداول الغياب...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص الجداول
        tables = ['daily_absence', 'monthly_absence_stats', 'yearly_absence_stats']
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   📋 {table}: {count} سجل")
        
        # عرض عينة من البيانات
        cursor.execute("""
            SELECT student_name, absence_date, session_1, session_2, session_3
            FROM daily_absence 
            ORDER BY absence_date DESC 
            LIMIT 5
        """)
        
        recent_absences = cursor.fetchall()
        if recent_absences:
            print("\n📝 آخر سجلات الغياب:")
            for absence in recent_absences:
                sessions = []
                if absence[2]: sessions.append("ح1")
                if absence[3]: sessions.append("ح2") 
                if absence[4]: sessions.append("ح3")
                sessions_str = ", ".join(sessions) if sessions else "حاضر"
                print(f"   • {absence[0]} - {absence[1]} - {sessions_str}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجداول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح جداول الغياب...")
    print("=" * 50)
    
    # إنشاء الجداول
    tables_ok = create_absence_tables()
    
    # إضافة بيانات تجريبية
    data_ok = False
    if tables_ok:
        data_ok = add_sample_absence_data()
    
    # تحديث الإحصائيات
    stats_ok = False
    if data_ok:
        stats_ok = update_absence_statistics()
    
    # اختبار الجداول
    test_ok = False
    if stats_ok:
        test_ok = test_absence_tables()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   إنشاء الجداول: {'✅ نجح' if tables_ok else '❌ فشل'}")
    print(f"   البيانات التجريبية: {'✅ نجح' if data_ok else '❌ فشل'}")
    print(f"   تحديث الإحصائيات: {'✅ نجح' if stats_ok else '❌ فشل'}")
    print(f"   اختبار الجداول: {'✅ نجح' if test_ok else '❌ فشل'}")
    
    if all([tables_ok, data_ok, stats_ok, test_ok]):
        print("\n🎯 تم إصلاح جداول الغياب بنجاح!")
        print("\n🚀 الآن يمكنك:")
        print("   • تشغيل نافذة معالجة الغياب")
        print("   • تسجيل الغياب بالحصص")
        print("   • إنشاء التقارير المحسنة")
    else:
        print("\n⚠️ بعض العمليات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
