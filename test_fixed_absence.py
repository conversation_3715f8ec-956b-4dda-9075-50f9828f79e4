#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_fixed_absence():
    """اختبار نافذة الغياب بعد الإصلاح"""
    print("🎯 اختبار نافذة الغياب بعد إصلاح العمود")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص الأعمدة
        print("\n📋 فحص أعمدة جدول البيانات:")
        cursor.execute("PRAGMA table_info(جدول_البيانات)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"   الأعمدة: {columns}")
        
        # 2. فحص وجود العمود الصحيح
        if 'اسم_التلميذ' in columns:
            print("   ✅ عمود اسم_التلميذ موجود")
        else:
            print("   ❌ عمود اسم_التلميذ غير موجود")
        
        if 'اسم_المجموعة' in columns:
            print("   ✅ عمود اسم_المجموعة موجود")
        else:
            print("   ❌ عمود اسم_المجموعة غير موجود")
        
        # 3. اختبار الاستعلام الجديد
        print(f"\n🔍 اختبار الاستعلام الجديد:")
        query = """
            SELECT 
                COALESCE(id, rowid) as id,
                اسم_التلميذ as student_name,
                اسم_المجموعة as group_name
            FROM جدول_البيانات
            WHERE اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
            ORDER BY اسم_التلميذ
            LIMIT 10
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"   📊 عدد النتائج: {len(results)}")
        
        if len(results) > 0:
            print(f"   📝 أول 5 تلاميذ:")
            for i, row in enumerate(results[:5], 1):
                print(f"      {i}. ID: {row[0]}, الاسم: {row[1]}, المجموعة: {row[2]}")
        else:
            print("   ❌ لا توجد نتائج!")
        
        # 4. فحص الأقسام والمجموعات
        print(f"\n🏫 فحص الأقسام والمجموعات:")
        
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم")
        sections = [row[0] for row in cursor.fetchall()]
        print(f"   📚 الأقسام ({len(sections)}): {sections[:5]}...")
        
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        print(f"   👥 المجموعات ({len(groups)}): {groups[:5]}...")
        
        # 5. إحصائيات
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        total_students = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''")
        valid_students = cursor.fetchone()[0]
        
        print(f"\n📊 الإحصائيات:")
        print(f"   👥 إجمالي السجلات: {total_students}")
        print(f"   ✅ التلاميذ الصحيحين: {valid_students}")
        print(f"   📚 عدد الأقسام: {len(sections)}")
        print(f"   👥 عدد المجموعات: {len(groups)}")
        
        conn.close()
        
        # 6. النتيجة
        print(f"\n🎯 النتيجة:")
        print("=" * 30)
        
        if len(results) > 0:
            print("✅ الإصلاح نجح!")
            print("✅ النافذة ستعرض التلاميذ بأسمائهم الصحيحة")
            print("✅ التصفية ستعمل بشكل طبيعي")
            
            print(f"\n🚀 تشغيل النافذة:")
            print("   python absence_management_window.py")
            
            print(f"\n💡 ما ستراه:")
            print(f"   👤 {valid_students} تلميذ في الجدول")
            print(f"   📚 {len(sections)} قسم للتصفية")
            print(f"   👥 {len(groups)} مجموعة للتصفية")
            
        else:
            print("❌ لا تزال هناك مشكلة")
            print("💡 تحقق من البيانات في قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_absence()
