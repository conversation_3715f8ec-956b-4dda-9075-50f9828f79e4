#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ========================================
# 🔧 إعدادات التقرير المحسن القابلة للتعديل اليدوي
# ========================================

# 📋 ترتيب الأعمدة في الجدول الثاني (من اليمين لليسار)
COLUMN_ORDER = [
    'week5',      # الأسبوع الخامس
    'week4',      # الأسبوع الرابع  
    'week3',      # الأسبوع الثالث
    'week2',      # الأسبوع الثاني
    'week1',      # الأسبوع الأول
    'name',       # اسم التلميذ
    'id',         # الرمز
    'order'       # رقم الترتيب (سطرين)
]

# 📏 عرض الأعمدة بالمليمتر (غير القيم حسب الحاجة)
COLUMN_WIDTHS = {
    'name': 50,       # اسم التلميذ
    'id': 25,         # الرمز
    'order': 20,      # رقم الترتيب
    'week': 25,       # كل أسبوع (3 حصص)
    'session': 8      # كل حصة
}

# 📐 ارتفاع الصفوف بالمليمتر (غير القيم حسب الحاجة)
ROW_HEIGHTS = {
    'header_main': 20,      # رأس الجدول الرئيسي
    'header_sub': 10,       # رأس الجدول الفرعي (الحصص)
    'student_row': 12,      # صف التلميذ
    'info_row': 10          # صف معلومات القسم
}

# 🖼️ نمط الحدود (غير القيم حسب الحاجة)
BORDER_STYLE = {
    'width': 0.5,           # سمك الحدود
    'color_r': 0,           # اللون الأحمر (0-255)
    'color_g': 0,           # اللون الأخضر (0-255)  
    'color_b': 0,           # اللون الأزرق (0-255)
    'style': 'solid'        # نمط الخط
}

# 📄 إعدادات الصفحة (غير القيم حسب الحاجة)
PAGE_SETTINGS = {
    'orientation': 'P',     # P = عمودي، L = أفقي
    'margin_top': 5,        # الهامش العلوي
    'margin_bottom': 5,     # الهامش السفلي
    'margin_left': 5,       # الهامش الأيسر
    'margin_right': 5       # الهامش الأيمن
}

# 🔤 أسماء الأعمدة (غير النصوص حسب الحاجة)
COLUMN_NAMES = {
    'name': 'اسم التلميذ',
    'id': 'الرمز',
    'order': 'رقم\nالترتيب'  # سطرين
}

# ========================================

import os
import sys
import sqlite3
import calendar
import subprocess
from datetime import datetime, timedelta
from fpdf import FPDF

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    print("⚠️ مكتبات النص العربي غير متوفرة. سيتم استخدام النص الإنجليزي.")

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__(PAGE_SETTINGS['orientation'], 'mm', 'A4')
        self.set_margins(
            PAGE_SETTINGS['margin_left'], 
            PAGE_SETTINGS['margin_top'], 
            PAGE_SETTINGS['margin_right']
        )
        self.set_auto_page_break(auto=True, margin=PAGE_SETTINGS['margin_bottom'])
        
        # فحص توفر خط Calibri
        self.calibri_available = False
        self.calibri_bold_available = False
        
        try:
            self.add_font('Calibri', '', 'calibri.ttf', uni=True)
            self.add_font('Calibri', 'B', 'calibrib.ttf', uni=True)
            self.calibri_available = True
            self.calibri_bold_available = True
        except:
            pass
        
        # تعيين الخط الافتراضي والحدود
        if self.calibri_available:
            self.set_font('Calibri', '', 10)
        else:
            self.set_font('Arial', '', 10)
        
        # تعيين نمط الحدود من الإعدادات
        self.set_draw_color(
            BORDER_STYLE['color_r'], 
            BORDER_STYLE['color_g'], 
            BORDER_STYLE['color_b']
        )
        self.set_line_width(BORDER_STYLE['width'])

    def set_main_title_font(self):
        """خط العناوين الرئيسية - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 15)
        else:
            self.set_font('Arial', 'B', 15)

    def set_subtitle_font(self):
        """خط العناوين الفرعية - Calibri 14 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 14)
        else:
            self.set_font('Arial', 'B', 14)

    def set_detail_font(self):
        """خط التفاصيل - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 13)
        else:
            self.set_font('Arial', 'B', 13)

    def set_table_header_font(self):
        """خط رؤوس الجدول - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 15)
        else:
            self.set_font('Arial', 'B', 15)

    def set_table_row_font(self):
        """خط صفوف الجدول - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 13)
        else:
            self.set_font('Arial', 'B', 13)

    def ar_text(self, text):
        """تحويل النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT:
            return str(text)
        
        try:
            if isinstance(text, (int, float)):
                return str(text)
            
            text = str(text)
            if not text.strip():
                return text
            
            # تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # ترتيب النص للعرض الصحيح
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return str(text)

def get_week_dates(year, month):
    """حساب تواريخ الأسابيع في الشهر (بداية كل أسبوع يوم الاثنين)"""
    weeks = []
    
    # أول يوم في الشهر
    first_day = datetime(year, month, 1)
    
    # آخر يوم في الشهر
    last_day = datetime(year, month, calendar.monthrange(year, month)[1])
    
    # البحث عن أول يوم اثنين
    current_date = first_day
    while current_date.weekday() != 0:  # 0 = الاثنين
        current_date -= timedelta(days=1)
    
    week_num = 1
    while current_date <= last_day:
        # التحقق من أن الأسبوع يحتوي على أيام من الشهر المطلوب
        week_end = current_date + timedelta(days=6)
        if current_date.month == month or week_end.month == month:
            weeks.append({
                'week_num': week_num,
                'start_date': current_date,
                'monday_date': current_date.strftime('%d/%m')
            })
            week_num += 1
        
        current_date += timedelta(days=7)
    
    return weeks

def get_students_data(section, db_path="data.db"):
    """جلب بيانات التلاميذ من قاعدة البيانات"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        if section == "جميع الأقسام":
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ
                FROM جدول_البيانات
                WHERE اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
                ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
            """)
        else:
            cursor.execute("""
                SELECT id, اسم_التلميذ, رمز_التلميذ
                FROM جدول_البيانات
                WHERE القسم = ? AND اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
                ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
            """, (section,))
        
        students = cursor.fetchall()
        conn.close()
        return students
        
    except Exception as e:
        print(f"❌ خطأ في جلب بيانات التلاميذ: {e}")
        return []

def generate_improved_attendance_pdf(section, year, month, month_name, db_path="data.db"):
    """إنشاء PDF محسن لورقة متابعة الغياب"""
    try:
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # حساب عرض الصفحة
        page_width = 210 - PAGE_SETTINGS['margin_left'] - PAGE_SETTINGS['margin_right']
        
        # جلب بيانات التلاميذ
        students = get_students_data(section, db_path)
        
        # حساب الأسابيع
        weeks = get_week_dates(year, month)
        
        # جلب معلومات القسم
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT القسم, المادة, اسم_الاستاذ
                FROM جدول_المواد_والاقسام 
                WHERE القسم = ?
                LIMIT 1
            """, (section,))
            
            section_info = cursor.fetchone()
            if not section_info:
                section_info = (section, "غير محدد", "غير محدد")
            conn.close()
        except Exception as e:
            print(f"⚠️ خطأ في جلب معلومات القسم: {e}")
            section_info = (section, "غير محدد", "غير محدد")
        
        # الشعار والعنوان
        y = 10
        
        # عنوان الورقة
        pdf.set_main_title_font()
        pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
        pdf.cell(page_width, 10, pdf.ar_text("ورقة متابعة الغياب الشهرية"), border=0, align='C')
        y += 15
        
        pdf.set_subtitle_font()
        pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
        pdf.cell(page_width, 8, pdf.ar_text(f"شهر {month_name} {year}"), border=0, align='C')
        y += 15
        
        # معلومات القسم
        pdf.set_detail_font()
        cell_width = page_width / 4
        
        pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
        pdf.cell(cell_width, ROW_HEIGHTS['info_row'], pdf.ar_text(f"القسم: {section_info[0]}"), border=1, align='C')
        pdf.set_xy(PAGE_SETTINGS['margin_left'] + cell_width, y)
        pdf.cell(cell_width, ROW_HEIGHTS['info_row'], pdf.ar_text(f"المادة: {section_info[1]}"), border=1, align='C')
        pdf.set_xy(PAGE_SETTINGS['margin_left'] + cell_width * 2, y)
        pdf.cell(cell_width, ROW_HEIGHTS['info_row'], pdf.ar_text(f"الأستاذ(ة): {section_info[2]}"), border=1, align='C')
        pdf.set_xy(PAGE_SETTINGS['margin_left'] + cell_width * 3, y)
        pdf.cell(cell_width, ROW_HEIGHTS['info_row'], pdf.ar_text(f"عدد التلاميذ: {len(students)}"), border=1, align='C')
        
        y += ROW_HEIGHTS['info_row'] + 10

        # الجدول المحسن
        pdf.set_table_header_font()

        # حساب عرض الأعمدة
        basic_cols_width = COLUMN_WIDTHS['name'] + COLUMN_WIDTHS['id'] + COLUMN_WIDTHS['order']
        weeks_width = len(weeks) * COLUMN_WIDTHS['week']

        # رأس الجدول الأول - الأعمدة الأساسية والأسابيع (معكوس)
        x = PAGE_SETTINGS['margin_left']

        # الأسابيع أولاً (من الخامس للأول)
        weeks_reversed = list(reversed(weeks))
        for i, week in enumerate(weeks_reversed, 1):
            pdf.set_xy(x, y)
            pdf.cell(
                COLUMN_WIDTHS['week'],
                ROW_HEIGHTS['header_main'],
                pdf.ar_text(f"الأسبوع {i}\n{week['monday_date']}"),
                border=1,
                align='C'
            )
            x += COLUMN_WIDTHS['week']

        # ثم الأعمدة الأساسية
        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['name'], ROW_HEIGHTS['header_main'], pdf.ar_text(COLUMN_NAMES['name']), border=1, align='C')
        x += COLUMN_WIDTHS['name']

        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['id'], ROW_HEIGHTS['header_main'], pdf.ar_text(COLUMN_NAMES['id']), border=1, align='C')
        x += COLUMN_WIDTHS['id']

        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['order'], ROW_HEIGHTS['header_main'], pdf.ar_text(COLUMN_NAMES['order']), border=1, align='C')

        # رأس الجدول الثاني - الحصص
        y += ROW_HEIGHTS['header_main']
        x = PAGE_SETTINGS['margin_left']

        # حصص الأسابيع (معكوس)
        for week in weeks_reversed:
            for session_num in [1, 2, 3]:
                pdf.set_xy(x, y)
                pdf.cell(
                    COLUMN_WIDTHS['session'],
                    ROW_HEIGHTS['header_sub'],
                    pdf.ar_text(f"ح{session_num}"),
                    border=1,
                    align='C'
                )
                x += COLUMN_WIDTHS['session']

        # خلايا فارغة للأعمدة الأساسية
        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['name'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')
        x += COLUMN_WIDTHS['name']

        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['id'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')
        x += COLUMN_WIDTHS['id']

        pdf.set_xy(x, y)
        pdf.cell(COLUMN_WIDTHS['order'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')

        y += ROW_HEIGHTS['header_sub']

        # صفوف التلاميذ
        pdf.set_table_row_font()
        for i, student in enumerate(students, 1):
            student_id, student_name, student_code = student

            x = PAGE_SETTINGS['margin_left']

            # خلايا الحصص أولاً (فارغة للتعبئة)
            for week in weeks_reversed:
                for session in range(3):
                    pdf.set_xy(x, y)
                    pdf.cell(COLUMN_WIDTHS['session'], ROW_HEIGHTS['student_row'], "", border=1, align='C')
                    x += COLUMN_WIDTHS['session']

            # بيانات التلميذ
            pdf.set_xy(x, y)
            pdf.cell(COLUMN_WIDTHS['name'], ROW_HEIGHTS['student_row'], pdf.ar_text(str(student_name)), border=1, align='R')
            x += COLUMN_WIDTHS['name']

            pdf.set_xy(x, y)
            pdf.cell(COLUMN_WIDTHS['id'], ROW_HEIGHTS['student_row'], str(student_code or student_id), border=1, align='C')
            x += COLUMN_WIDTHS['id']

            pdf.set_xy(x, y)
            pdf.cell(COLUMN_WIDTHS['order'], ROW_HEIGHTS['student_row'], str(i), border=1, align='C')

            y += ROW_HEIGHTS['student_row']

            # انتقال لصفحة جديدة عند الحاجة
            if y > 270:
                pdf.add_page()
                y = 20

        # ملاحظات
        y += 10
        pdf.set_detail_font()
        pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
        pdf.cell(page_width, 6, pdf.ar_text("ملاحظات:"), border=0, align='R')
        y += 8

        notes = [
            "• يتم وضع علامة (×) في الخانة المقابلة للحصة التي غاب فيها التلميذ",
            "• ح1: الحصة الأولى، ح2: الحصة الثانية، ح3: الحصة الثالثة",
            "• التواريخ تشير إلى يوم الاثنين من كل أسبوع",
            "• الأسابيع مرتبة من الخامس إلى الأول (من اليمين لليسار)"
        ]

        for note in notes:
            pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
            pdf.cell(page_width, 5, pdf.ar_text(note), border=0, align='R')
            y += 6

        # التوقيع والتاريخ
        y += 10
        pdf.set_xy(PAGE_SETTINGS['margin_left'], y)
        pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"), border=0, align='R')

        pdf.set_xy(PAGE_SETTINGS['margin_left'] + page_width / 2, y)
        pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

        return pdf
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        raise

def create_improved_attendance_report(section, year, month, month_name, db_path="data.db"):
    """إنشاء تقرير ورقة متابعة الغياب المحسن"""
    try:
        # إنشاء PDF
        pdf = generate_improved_attendance_pdf(section, year, month, month_name, db_path)
        
        # إنشاء مجلد التقارير
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير متابعة الغياب المحسنة')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_section = section.replace("/", "_").replace("\\", "_").replace(" ", "_")
        filename = f"ورقة_متابعة_محسنة_{safe_section}_{month_name}_{year}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)
        
        # حفظ الملف
        pdf.output(output_path)
        print(f"✅ تم إنشاء التقرير المحسن: {output_path}")
        
        # فتح الملف
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
        except Exception as e:
            print(f"⚠️ تم إنشاء التقرير ولكن تعذر فتحه: {e}")
        
        return True, output_path, "تم إنشاء ورقة متابعة الغياب المحسنة بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return False, None, f"خطأ في إنشاء التقرير: {str(e)}"

if __name__ == "__main__":
    # اختبار
    success, path, message = create_improved_attendance_report("قسم / 01", 2024, 12, "ديسمبر")
    print(f"النتيجة: {success}")
    print(f"المسار: {path}")
    print(f"الرسالة: {message}")
