#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# نافذة معالجة الغياب
import sys
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTableWidget, QTableWidgetItem,
                             QPushButton, QLabel, QLineEdit, QComboBox,
                             QDateEdit, QMessageBox, QAbstractItemView,
                             QHeaderView, QGroupBox, QListWidget, QDialog,
                             QDialogButtonBox, QCheckBox, QTextEdit, QProgressDialog,
                             QScrollArea, QFrame, QSpacerItem, QSizePolicy, QInputDialog)
from PyQt5.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QColor

class DataLoaderThread(QThread):
    """خيط منفصل لتحميل البيانات بدون تشنج الواجهة"""

    # إشارات للتواصل مع الواجهة
    data_loaded = pyqtSignal(list)  # إرسال البيانات المحملة
    progress_update = pyqtSignal(str)  # تحديث رسالة التقدم
    error_occurred = pyqtSignal(str)  # إرسال رسالة خطأ
    finished_loading = pyqtSignal()  # إشارة انتهاء التحميل

    def __init__(self, db_path, search_text="", selected_section="جميع الأقسام"):
        super().__init__()
        self.db_path = db_path
        self.search_text = search_text
        self.selected_section = selected_section
        self.is_cancelled = False

    def cancel(self):
        """إلغاء عملية التحميل"""
        self.is_cancelled = True

    def run(self):
        """تنفيذ تحميل البيانات في خيط منفصل"""
        try:
            import time
            start_time = time.time()

            self.progress_update.emit("🔧 بدء تحميل البيانات...")

            if self.is_cancelled:
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            self.progress_update.emit("💾 تم الاتصال بقاعدة البيانات")

            if self.is_cancelled:
                conn.close()
                return

            # بناء الاستعلام
            where_conditions = ["اسم_التلميذ IS NOT NULL", "اسم_التلميذ != ''"]
            params = []

            if self.search_text:
                where_conditions.append("اسم_التلميذ LIKE ?")
                params.append(f"%{self.search_text}%")

            if self.selected_section != "جميع الأقسام":
                where_conditions.append("القسم = ?")
                params.append(self.selected_section)

            query = f"""
                SELECT id, اسم_التلميذ, رمز_التلميذ, القسم
                FROM جدول_البيانات
                WHERE {' AND '.join(where_conditions)}
                ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
            """

            # تنفيذ الاستعلام الأساسي
            cursor.execute(query, params)
            students_basic = cursor.fetchall()

            self.progress_update.emit(f"📊 تم جلب {len(students_basic)} تلميذ")

            if self.is_cancelled:
                conn.close()
                return

            # معالجة البيانات بدون حساب الغياب - سريع جداً
            students = []
            total_students = len(students_basic)

            for i, (student_id, student_name, student_code, section) in enumerate(students_basic):
                if self.is_cancelled:
                    break

                # تحديث التقدم كل 100 تلميذ (أسرع من قبل)
                if i % 100 == 0:
                    progress_msg = f"🔄 معالجة التلميذ {i+1} من {total_students}"
                    self.progress_update.emit(progress_msg)

                # بدون حساب الغياب - فقط البيانات الأساسية
                students.append((
                    student_id,
                    student_name,
                    student_code or "غير محدد",
                    section or "غير محدد"
                ))

            conn.close()

            if not self.is_cancelled:
                end_time = time.time()
                duration = end_time - start_time
                self.progress_update.emit(f"✅ تم تحميل {len(students)} تلميذ في {duration:.2f} ثانية")

                # إرسال البيانات إلى الواجهة
                self.data_loaded.emit(students)

        except Exception as e:
            error_msg = f"❌ خطأ في تحميل البيانات: {str(e)}"
            self.error_occurred.emit(error_msg)

        finally:
            self.finished_loading.emit()


class AbsenceSessionDialog(QDialog):
    """نافذة تحديد الحصص المتغيب عنها"""
    def __init__(self, student_name, parent=None):
        super().__init__(parent)
        self.student_name = student_name
        self.selected_sessions = []
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle(f"تحديد الحصص المتغيب عنها - {self.student_name}")
        self.setGeometry(300, 300, 400, 350)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel(f"حدد الحصص التي غاب عنها:\n{self.student_name}")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # مربعات اختيار الحصص
        self.session_checkboxes = []
        sessions = ["الحصة الأولى", "الحصة الثانية", "الحصة الثالثة"]

        for session in sessions:
            checkbox = QCheckBox(session)
            checkbox.setFont(QFont("Calibri", 14, QFont.Bold))
            checkbox.setStyleSheet("""
                QCheckBox {
                    padding: 8px;
                    spacing: 10px;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                }
            """)
            self.session_checkboxes.append(checkbox)
            layout.addWidget(checkbox)

        # مربع الملاحظات
        notes_label = QLabel("ملاحظات إضافية:")
        notes_label.setFont(QFont("Calibri", 14, QFont.Bold))
        layout.addWidget(notes_label)

        self.notes_text = QTextEdit()
        self.notes_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        layout.addWidget(self.notes_text)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        ok_button = QPushButton("✅ تأكيد")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        ok_button.clicked.connect(self.accept_selection)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)
        layout.addLayout(buttons_layout)

    def accept_selection(self):
        """تأكيد الاختيار"""
        self.selected_sessions = []
        for checkbox in self.session_checkboxes:
            if checkbox.isChecked():
                self.selected_sessions.append(checkbox.text())

        if not self.selected_sessions:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حصة واحدة على الأقل")
            return

        self.accept()

    def get_selected_sessions(self):
        """الحصول على الحصص المحددة"""
        return self.selected_sessions

    def get_notes(self):
        """الحصول على الملاحظات"""
        return self.notes_text.toPlainText().strip()

class SimpleAbsenceReportDialog(QDialog):
    """نافذة تقرير الغياب البسيطة"""
    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_ui()
        self.load_report_data()

    def init_ui(self):
        self.setWindowTitle("📊 تقرير الغياب")
        self.setGeometry(200, 200, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel("📊 تقرير الغياب الشامل")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول التقرير
        self.report_table = QTableWidget()
        self.report_table.setColumnCount(6)
        self.report_table.setHorizontalHeaderLabels([
            "اسم التلميذ", "القسم", "عدد أيام الغياب", "عدد الحصص المتغيب عنها", "نسبة الحضور", "آخر غياب"
        ])

        # تنسيق رؤوس الجدول
        header = self.report_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
            }
        """)

        self.report_table.setAlternatingRowColors(True)
        self.report_table.setSortingEnabled(True)
        header.setStretchLastSection(True)

        layout.addWidget(self.report_table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def load_report_data(self):
        """تحميل بيانات التقرير"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب إحصائيات الغياب
            cursor.execute("""
                SELECT
                    jb.اسم_التلميذ,
                    jb.القسم,
                    COUNT(DISTINCT tg.تاريخ_الغياب) as ايام_الغياب,
                    SUM(tg.عدد_الحصص_المتغيب_عنها) as حصص_الغياب,
                    MAX(tg.تاريخ_الغياب) as آخر_غياب
                FROM جدول_البيانات jb
                LEFT JOIN تدوين_الغياب tg ON jb.id = tg.معرف_التلميذ
                WHERE tg.معرف_التلميذ IS NOT NULL
                GROUP BY jb.id, jb.اسم_التلميذ, jb.القسم
                ORDER BY حصص_الغياب DESC
            """)

            records = cursor.fetchall()
            conn.close()

            self.report_table.setRowCount(len(records))

            for row, record in enumerate(records):
                اسم_التلميذ, القسم, ايام_الغياب, حصص_الغياب, آخر_غياب = record

                # حساب نسبة الحضور (افتراض 60 حصة شهرياً)
                total_sessions = 60
                attendance_rate = ((total_sessions - (حصص_الغياب or 0)) / total_sessions) * 100

                # اسم التلميذ
                name_item = QTableWidgetItem(اسم_التلميذ or "")
                name_item.setFont(QFont("Calibri", 12, QFont.Bold))
                self.report_table.setItem(row, 0, name_item)

                # القسم
                section_item = QTableWidgetItem(القسم or "")
                section_item.setFont(QFont("Calibri", 12, QFont.Bold))
                self.report_table.setItem(row, 1, section_item)

                # أيام الغياب
                days_item = QTableWidgetItem(str(ايام_الغياب or 0))
                days_item.setFont(QFont("Calibri", 12, QFont.Bold))
                days_item.setTextAlignment(Qt.AlignCenter)
                self.report_table.setItem(row, 2, days_item)

                # حصص الغياب
                sessions_item = QTableWidgetItem(str(حصص_الغياب or 0))
                sessions_item.setFont(QFont("Calibri", 12, QFont.Bold))
                sessions_item.setTextAlignment(Qt.AlignCenter)
                self.report_table.setItem(row, 3, sessions_item)

                # نسبة الحضور
                rate_item = QTableWidgetItem(f"{attendance_rate:.1f}%")
                rate_item.setFont(QFont("Calibri", 12, QFont.Bold))
                rate_item.setTextAlignment(Qt.AlignCenter)
                # تلوين حسب النسبة
                if attendance_rate >= 90:
                    rate_item.setBackground(QColor(213, 244, 230))  # أخضر
                elif attendance_rate >= 75:
                    rate_item.setBackground(QColor(255, 243, 205))  # أصفر
                else:
                    rate_item.setBackground(QColor(248, 215, 218))  # أحمر
                self.report_table.setItem(row, 4, rate_item)

                # آخر غياب
                last_item = QTableWidgetItem(آخر_غياب or "لا يوجد")
                last_item.setFont(QFont("Calibri", 12, QFont.Bold))
                last_item.setTextAlignment(Qt.AlignCenter)
                self.report_table.setItem(row, 5, last_item)

            self.report_table.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات التقرير:\n{str(e)}")

class SimpleStatisticsDialog(QDialog):
    """نافذة الإحصائيات البسيطة"""
    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_ui()
        self.load_statistics()

    def init_ui(self):
        self.setWindowTitle("📈 إحصائيات الغياب")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel("📈 إحصائيات الغياب الشاملة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # منطقة الإحصائيات
        self.stats_label = QLabel()
        self.stats_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.stats_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 20px;
                margin: 10px;
            }
        """)
        self.stats_label.setWordWrap(True)
        layout.addWidget(self.stats_label)

        # جدول الإحصائيات الشهرية
        self.monthly_table = QTableWidget()
        self.monthly_table.setColumnCount(5)
        self.monthly_table.setHorizontalHeaderLabels([
            "الشهر", "عدد التلاميذ الغائبين", "إجمالي أيام الغياب", "إجمالي حصص الغياب", "متوسط نسبة الحضور"
        ])

        # تنسيق رؤوس الجدول
        header = self.monthly_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
            }
        """)

        self.monthly_table.setAlternatingRowColors(True)
        header.setStretchLastSection(True)

        layout.addWidget(self.monthly_table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إحصائيات عامة
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                    COUNT(DISTINCT تاريخ_الغياب) as عدد_ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب,
                    AVG(عدد_الحصص_المتغيب_عنها) as متوسط_حصص_الغياب
                FROM تدوين_الغياب
            """)

            general_stats = cursor.fetchone()

            if general_stats and general_stats[0]:
                عدد_التلاميذ, عدد_الايام, اجمالي_الحصص, متوسط_الحصص = general_stats

                # حساب نسبة الحضور العامة
                total_possible_sessions = عدد_التلاميذ * 60  # افتراض 60 حصة شهرياً
                overall_attendance = ((total_possible_sessions - اجمالي_الحصص) / total_possible_sessions) * 100 if total_possible_sessions > 0 else 100

                stats_text = f"""
📊 الإحصائيات العامة:
👥 عدد التلاميذ الذين لديهم غياب: {عدد_التلاميذ}
📅 عدد أيام الغياب المسجلة: {عدد_الايام}
📚 إجمالي حصص الغياب: {اجمالي_الحصص}
📈 متوسط حصص الغياب لكل تلميذ: {متوسط_الحصص:.1f}
🎯 نسبة الحضور العامة: {overall_attendance:.1f}%
                """
            else:
                stats_text = "📊 لا توجد بيانات غياب مسجلة حتى الآن"

            self.stats_label.setText(stats_text)

            # إحصائيات شهرية
            cursor.execute("""
                SELECT
                    strftime('%Y-%m', تاريخ_الغياب) as الشهر,
                    COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ,
                    COUNT(DISTINCT تاريخ_الغياب) as ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as حصص_الغياب
                FROM تدوين_الغياب
                GROUP BY strftime('%Y-%m', تاريخ_الغياب)
                ORDER BY الشهر DESC
            """)

            monthly_stats = cursor.fetchall()
            conn.close()

            self.monthly_table.setRowCount(len(monthly_stats))

            for row, record in enumerate(monthly_stats):
                الشهر, عدد_التلاميذ, ايام_الغياب, حصص_الغياب = record

                # حساب متوسط نسبة الحضور للشهر
                total_sessions_month = عدد_التلاميذ * 60
                attendance_rate = ((total_sessions_month - حصص_الغياب) / total_sessions_month) * 100 if total_sessions_month > 0 else 100

                # الشهر
                month_item = QTableWidgetItem(الشهر)
                month_item.setFont(QFont("Calibri", 12, QFont.Bold))
                month_item.setTextAlignment(Qt.AlignCenter)
                self.monthly_table.setItem(row, 0, month_item)

                # عدد التلاميذ
                students_item = QTableWidgetItem(str(عدد_التلاميذ))
                students_item.setFont(QFont("Calibri", 12, QFont.Bold))
                students_item.setTextAlignment(Qt.AlignCenter)
                self.monthly_table.setItem(row, 1, students_item)

                # أيام الغياب
                days_item = QTableWidgetItem(str(ايام_الغياب))
                days_item.setFont(QFont("Calibri", 12, QFont.Bold))
                days_item.setTextAlignment(Qt.AlignCenter)
                self.monthly_table.setItem(row, 2, days_item)

                # حصص الغياب
                sessions_item = QTableWidgetItem(str(حصص_الغياب))
                sessions_item.setFont(QFont("Calibri", 12, QFont.Bold))
                sessions_item.setTextAlignment(Qt.AlignCenter)
                self.monthly_table.setItem(row, 3, sessions_item)

                # نسبة الحضور
                rate_item = QTableWidgetItem(f"{attendance_rate:.1f}%")
                rate_item.setFont(QFont("Calibri", 12, QFont.Bold))
                rate_item.setTextAlignment(Qt.AlignCenter)
                # تلوين حسب النسبة
                if attendance_rate >= 90:
                    rate_item.setBackground(QColor(213, 244, 230))  # أخضر
                elif attendance_rate >= 75:
                    rate_item.setBackground(QColor(255, 243, 205))  # أصفر
                else:
                    rate_item.setBackground(QColor(248, 215, 218))  # أحمر
                self.monthly_table.setItem(row, 4, rate_item)

            self.monthly_table.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الإحصائيات:\n{str(e)}")

class AttendanceProcessingWindow(QMainWindow):
    def __init__(self, db_path="data.db", parent=None):
        print("🔧 بدء إنشاء نافذة معالجة الغياب...")
        super().__init__(parent)
        print("✅ تم استدعاء الكونستركتور الأساسي")

        self.db_path = db_path
        print(f"📁 مسار قاعدة البيانات: {db_path}")

        # متغيرات الخيوط
        self.data_loader_thread = None
        self.is_loading = False

        print("🗄️ إنشاء قاعدة البيانات...")
        self.init_database()
        print("✅ تم إنشاء قاعدة البيانات")

        print("🖥️ إنشاء واجهة المستخدم...")
        self.init_ui()
        print("✅ تم إنشاء واجهة المستخدم")

        print("📚 تحميل الأقسام...")
        self.load_sections()
        print("✅ تم تحميل الأقسام")

        print("👥 الجدول جاهز - يرجى اختيار قسم لعرض البيانات")
        # لا نحمل البيانات عند الفتح - الجدول يبقى فارغ حتى يختار المستخدم قسم
        print("✅ النافذة جاهزة - اختر قسم لعرض التلاميذ")

    def closeEvent(self, event):
        """معالج إغلاق النافذة - إيقاف الخيوط"""
        print("🔚 إغلاق النافذة...")

        # إيقاف خيط التحميل إذا كان يعمل
        if self.data_loader_thread and self.data_loader_thread.isRunning():
            print("⏹️ إيقاف خيط التحميل...")
            self.data_loader_thread.cancel()
            self.data_loader_thread.wait(2000)  # انتظار ثانيتين

        # إيقاف التنظيف الدوري
        if hasattr(self, 'cleanup_timer'):
            self.cleanup_timer.stop()

        print("✅ تم إيقاف جميع الخيوط")
        event.accept()

    def init_database(self):
        """إنشاء جداول قاعدة البيانات المطلوبة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جدول absence_records لم يعد مستخدماً - تم الاستغناء عنه
            # نستخدم فقط جدول تدوين_الغياب

            # إنشاء جدول تدوين الغياب المطلوب
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS تدوين_الغياب (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    معرف_التلميذ INTEGER,
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT,
                    القسم TEXT,
                    تاريخ_الغياب DATE NOT NULL,
                    الحصة_الاولى INTEGER DEFAULT 0,
                    الحصة_الثانية INTEGER DEFAULT 0,
                    الحصة_الثالثة INTEGER DEFAULT 0,
                    عدد_الحصص_المتغيب_عنها INTEGER DEFAULT 0,
                    ملاحظات TEXT,
                    نوع_الغياب TEXT DEFAULT 'عادي',
                    تاريخ_التسجيل TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (معرف_التلميذ) REFERENCES جدول_البيانات(id)
                )
            """)

            # إنشاء جدول إحصائيات الغياب الشهرية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS احصائيات_الغياب_الشهرية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    معرف_التلميذ INTEGER,
                    اسم_التلميذ TEXT,
                    رمز_التلميذ TEXT,
                    القسم TEXT,
                    السنة INTEGER,
                    الشهر INTEGER,
                    اسم_الشهر TEXT,
                    اجمالي_الحصص INTEGER DEFAULT 0,
                    الحصص_المتغيب_عنها INTEGER DEFAULT 0,
                    نسبة_الحضور REAL DEFAULT 100.0,
                    نسبة_الغياب REAL DEFAULT 0.0,
                    عدد_ايام_الغياب INTEGER DEFAULT 0,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (معرف_التلميذ) REFERENCES جدول_البيانات(id)
                )
            """)

            # إنشاء جدول إحصائيات الغياب السنوية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS احصائيات_الغياب_السنوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    معرف_التلميذ INTEGER,
                    اسم_التلميذ TEXT,
                    رمز_التلميذ TEXT,
                    القسم TEXT,
                    السنة INTEGER,
                    اجمالي_الحصص INTEGER DEFAULT 0,
                    الحصص_المتغيب_عنها INTEGER DEFAULT 0,
                    نسبة_الحضور REAL DEFAULT 100.0,
                    نسبة_الغياب REAL DEFAULT 0.0,
                    عدد_ايام_الغياب INTEGER DEFAULT 0,
                    اسوأ_شهر TEXT,
                    افضل_شهر TEXT,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (معرف_التلميذ) REFERENCES جدول_البيانات(id)
                )
            """)

            # لا نحتاج للتحقق من جدول absence_records - تم الاستغناء عنه

            # فهارس جدول تدوين الغياب
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_تدوين_تاريخ ON تدوين_الغياب(تاريخ_الغياب)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_تدوين_تلميذ ON تدوين_الغياب(معرف_التلميذ)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_تدوين_قسم ON تدوين_الغياب(القسم)")

            # فهارس جداول الإحصائيات
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_احصائيات_شهرية ON احصائيات_الغياب_الشهرية(معرف_التلميذ, السنة, الشهر)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_احصائيات_سنوية ON احصائيات_الغياب_السنوية(معرف_التلميذ, السنة)")

            conn.commit()
            conn.close()
            print("✅ تم إنشاء جميع جداول الغياب بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        self.setWindowTitle("معالجة الغياب والتقارير")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجيت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان الرئيسي
        title_label = QLabel("🎯 معالجة الغياب والتقارير")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة البحث والتصفية
        self.create_search_group(main_layout)
        
        # مجموعة الأزرار
        self.create_buttons_group(main_layout)
        
        # جدول الطلاب
        self.create_students_table(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز")

    def create_search_group(self, main_layout):
        """إنشاء مجموعة البحث والتصفية"""
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)
        
        # البحث بالاسم
        name_label = QLabel("اسم التلميذ:")
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(name_label)
        
        self.student_search = QLineEdit()
        self.student_search.setPlaceholderText("ابحث عن تلميذ...")
        self.student_search.setFont(QFont("Calibri", 14, QFont.Bold))
        self.student_search.textChanged.connect(self.load_students_data)
        search_layout.addWidget(self.student_search)
        
        # اختيار القسم
        section_label = QLabel("القسم:")
        section_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.section_combo.currentTextChanged.connect(self.load_students_data)
        search_layout.addWidget(self.section_combo)
        
        # اختيار التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(date_label)
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 14, QFont.Bold))
        self.date_edit.setCalendarPopup(True)

        # إضافة تأخير لمنع التشنج عند تغيير التاريخ
        self.date_timer = QTimer()
        self.date_timer.setSingleShot(True)
        self.date_timer.timeout.connect(self.load_students_data_delayed)

        # معالج آمن لتغيير التاريخ - معطل مؤقت<|im_end|>
        # self.date_edit.dateChanged.connect(self.on_date_changed_threaded)
        self.date_edit.dateChanged.connect(self.on_date_changed_test)
        search_layout.addWidget(self.date_edit)
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_all_data)
        search_layout.addWidget(refresh_btn)
        
        main_layout.addWidget(search_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)
        
        # زر تسجيل غياب فردي
        absent_btn = QPushButton("❌ تسجيل غياب")
        absent_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        absent_btn.clicked.connect(self.mark_student_absent)
        buttons_layout.addWidget(absent_btn)
        
        # زر غياب جماعي
        bulk_absent_btn = QPushButton("📋 غياب جماعي")
        bulk_absent_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        bulk_absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        bulk_absent_btn.clicked.connect(self.bulk_mark_absent)
        buttons_layout.addWidget(bulk_absent_btn)
        
        # زر تقرير الغياب
        report_btn = QPushButton("📊 تقرير الغياب")
        report_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        report_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        report_btn.clicked.connect(self.generate_absence_report)
        buttons_layout.addWidget(report_btn)
        
        # زر إحصائيات
        stats_btn = QPushButton("📈 الإحصائيات")
        stats_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        stats_btn.clicked.connect(self.show_statistics)
        buttons_layout.addWidget(stats_btn)



        # زر ورقة الغياب الشهرية
        monthly_sheet_btn = QPushButton("� ورقة الغياب الشهرية")
        monthly_sheet_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        monthly_sheet_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        monthly_sheet_btn.clicked.connect(self.generate_monthly_attendance_sheet)
        buttons_layout.addWidget(monthly_sheet_btn)

        # زر تقرير الغياب التفصيلي
        detailed_report_btn = QPushButton("📊 تقرير الغياب التفصيلي")
        detailed_report_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        detailed_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #7d3c98;
            }
        """)
        detailed_report_btn.clicked.connect(self.show_detailed_report_menu)
        buttons_layout.addWidget(detailed_report_btn)

        # زر حذف الغياب
        delete_absence_btn = QPushButton("🗑️ حذف الغياب")
        delete_absence_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        delete_absence_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_absence_btn.clicked.connect(self.show_delete_absence_dialog)
        buttons_layout.addWidget(delete_absence_btn)

        # زر تنظيف المؤشرات (مخفي - للطوارئ)
        clear_cursors_btn = QPushButton("🔄 تنظيف المؤشرات")
        clear_cursors_btn.setFont(QFont("Calibri", 10))
        clear_cursors_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_cursors_btn.clicked.connect(self.clear_all_cursors)
        clear_cursors_btn.setVisible(False)  # مخفي افتراضياً
        buttons_layout.addWidget(clear_cursors_btn)

        # حفظ مرجع للزر للوصول إليه لاحقاً
        self.clear_cursors_btn = clear_cursors_btn

        main_layout.addWidget(buttons_group)

        # إضافة اختصار لوحة مفاتيح لإظهار زر تنظيف المؤشرات
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence
        shortcut = QShortcut(QKeySequence("Ctrl+Shift+C"), self)
        shortcut.activated.connect(self.toggle_clear_cursors_btn)

        # تنظيف دوري للمؤشرات كل 5 ثواني
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.periodic_cursor_cleanup)
        self.cleanup_timer.start(5000)  # كل 5 ثواني

    def create_students_table(self, main_layout):
        """إنشاء جدول الطلاب"""
        table_group = QGroupBox("👥 قائمة التلاميذ")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)
        
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(4)
        self.students_table.setHorizontalHeaderLabels([
            "اسم التلميذ", "الرمز", "القسم", "رقم الترتيب"
        ])
        
        # تنسيق رؤوس الجدول
        header = self.students_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
                font-weight: bold;
            }
        """)
        
        # تنسيق الجدول
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.students_table.setSelectionMode(QAbstractItemView.MultiSelection)
        self.students_table.setSortingEnabled(True)

        # منع التعديل على البيانات - فقط التحديد والتحديد المتعدد
        self.students_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        # تعيين عرض الأعمدة - اسم التلميذ هو الأعرض
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # اسم التلميذ - الأعرض
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # القسم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # رقم الترتيب
        
        # تنسيق الجدول
        self.students_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
                text-align: right;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        table_layout.addWidget(self.students_table)
        main_layout.addWidget(table_group)

    def load_sections(self):
        """تحميل الأقسام"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            self.section_combo.clear()
            self.section_combo.addItem("جميع الأقسام")
            
            # جلب الأقسام من جدول البيانات
            cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != '' ORDER BY القسم")
            sections = cursor.fetchall()
            
            for section in sections:
                if section[0]:
                    self.section_combo.addItem(section[0])
            
            conn.close()
            print(f"✅ تم تحميل {len(sections)} قسم")
            
        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل الأقسام: {str(e)}")
            print(f"❌ خطأ في تحميل الأقسام: {e}")

    def clear_all_cursors(self):
        """إزالة جميع مؤشرات التحميل المتراكمة - حل شامل"""
        try:
            # إزالة جميع المؤشرات المتراكمة
            for i in range(20):  # محاولة إزالة حتى 20 مؤشر متراكم
                try:
                    QApplication.restoreOverrideCursor()
                except:
                    break

            # فرض تعيين المؤشر العادي
            QApplication.setOverrideCursor(Qt.ArrowCursor)
            QApplication.restoreOverrideCursor()

            # معالجة الأحداث لضمان التطبيق
            QApplication.processEvents()

            print("🔄 تم تنظيف جميع مؤشرات التحميل")

        except Exception as e:
            print(f"⚠️ خطأ في تنظيف المؤشرات: {e}")

        # تأكيد نهائي - فرض المؤشر العادي
        try:
            QApplication.setOverrideCursor(Qt.ArrowCursor)
            QApplication.restoreOverrideCursor()
        except:
            pass

    def toggle_clear_cursors_btn(self):
        """تبديل إظهار/إخفاء زر تنظيف المؤشرات"""
        if hasattr(self, 'clear_cursors_btn'):
            current_visible = self.clear_cursors_btn.isVisible()
            self.clear_cursors_btn.setVisible(not current_visible)
            if not current_visible:
                print("🔧 تم إظهار زر تنظيف المؤشرات (Ctrl+Shift+C)")
            else:
                print("🔧 تم إخفاء زر تنظيف المؤشرات")

    def periodic_cursor_cleanup(self):
        """تنظيف دوري للمؤشرات لمنع التراكم"""
        try:
            # تنظيف صامت بدون رسائل
            for _ in range(5):
                try:
                    QApplication.restoreOverrideCursor()
                except:
                    break
        except:
            pass

    def on_date_changed_threaded(self):
        """معالج تغيير التاريخ باستخدام خيط منفصل - لا تشنج"""
        print("📅 تم تغيير التاريخ - بدء تحميل بخيط منفصل...")

        # إيقاف أي تحميل جاري
        if self.data_loader_thread and self.data_loader_thread.isRunning():
            print("⏹️ إيقاف التحميل الجاري...")
            self.data_loader_thread.cancel()
            self.data_loader_thread.wait(1000)  # انتظار ثانية واحدة

        # بدء تحميل جديد
        self.load_students_data_threaded()

        print("✅ تم بدء التحميل في خيط منفصل")

    def on_date_changed_test(self):
        """معالج اختبار لتغيير التاريخ - بدون تحميل بيانات"""
        selected_date = self.date_edit.date().toString("yyyy-MM-dd")
        print(f"📅 تم تغيير التاريخ إلى: {selected_date} (بدون تحميل بيانات)")

        # تحديث شريط الحالة فقط
        self.statusBar().showMessage(f"تم تغيير التاريخ إلى: {selected_date}")

        # لا نحمل أي بيانات - فقط اختبار تغيير التاريخ
        print(f"✅ تم معالجة تغيير التاريخ بدون تحميل: {selected_date}")

    def load_students_data_threaded(self):
        """تحميل البيانات باستخدام خيط منفصل - لا تشنج"""
        if self.is_loading:
            print("⚠️ التحميل جاري بالفعل...")
            return

        # التحقق من اختيار القسم - لا نحمل البيانات إذا لم يختر المستخدم قسم
        selected_section = self.section_combo.currentText() if hasattr(self, 'section_combo') else "جميع الأقسام"
        if selected_section == "جميع الأقسام":
            print("📋 لم يتم اختيار قسم محدد - الجدول يبقى فارغ")
            self.students_table.setRowCount(0)
            self.statusBar().showMessage("يرجى اختيار قسم لعرض التلاميذ")
            return

        print(f"🚀 بدء تحميل البيانات للقسم: {selected_section}")
        self.is_loading = True

        # تحديث شريط الحالة
        self.statusBar().showMessage(f"جاري تحميل بيانات القسم: {selected_section}...")

        # الحصول على معايير البحث
        search_text = self.student_search.text().strip() if hasattr(self, 'student_search') else ""

        # إنشاء وتشغيل خيط التحميل
        self.data_loader_thread = DataLoaderThread(self.db_path, search_text, selected_section)

        # ربط الإشارات
        self.data_loader_thread.data_loaded.connect(self.on_data_loaded)
        self.data_loader_thread.progress_update.connect(self.on_progress_update)
        self.data_loader_thread.error_occurred.connect(self.on_loading_error)
        self.data_loader_thread.finished_loading.connect(self.on_loading_finished)

        # بدء التحميل
        self.data_loader_thread.start()

        print("✅ تم بدء خيط التحميل")

    def on_data_loaded(self, students_data):
        """معالج استلام البيانات من الخيط المنفصل"""
        print(f"📊 تم استلام {len(students_data)} تلميذ من الخيط")

        try:
            # عرض البيانات في الجدول (الآن سريع بدون حساب الغياب)
            self.display_students_data(students_data)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل {len(students_data)} تلميذ")

            print(f"✅ تم عرض {len(students_data)} تلميذ في الجدول")

        except Exception as e:
            print(f"❌ خطأ في عرض البيانات: {e}")
            self.statusBar().showMessage(f"خطأ في عرض البيانات: {str(e)}")

    def on_progress_update(self, message):
        """معالج تحديث التقدم"""
        print(f"📈 {message}")
        self.statusBar().showMessage(message)

    def on_loading_error(self, error_message):
        """معالج خطأ التحميل"""
        print(f"❌ {error_message}")
        self.statusBar().showMessage(error_message)
        QMessageBox.critical(self, "خطأ", error_message)

    def on_loading_finished(self):
        """معالج انتهاء التحميل"""
        print("🏁 انتهاء عملية التحميل")
        self.is_loading = False

        # تنظيف الخيط
        if self.data_loader_thread:
            self.data_loader_thread.deleteLater()
            self.data_loader_thread = None

    def display_students_data(self, students_data):
        """عرض البيانات في الجدول بدون حساب الغياب - سريع"""
        try:
            print(f"🚀 عرض {len(students_data)} تلميذ في الجدول...")

            # إيقاف التحديثات أثناء التحميل لتسريع العملية
            self.students_table.setUpdatesEnabled(False)

            # تعيين عدد الصفوف
            self.students_table.setRowCount(len(students_data))

            # عرض البيانات بدون حساب الغياب
            for row, student in enumerate(students_data):
                # اسم التلميذ (العمود الأول)
                name_item = QTableWidgetItem(str(student[1]))  # student[1] = اسم_التلميذ
                name_item.setFont(QFont("Calibri", 13, QFont.Bold))
                name_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)  # محاذاة لليمين
                self.students_table.setItem(row, 0, name_item)

                # رمز التلميذ (العمود الثاني)
                code_item = QTableWidgetItem(str(student[2]) if student[2] else "غير محدد")  # student[2] = رمز_التلميذ
                code_item.setFont(QFont("Calibri", 13, QFont.Bold))
                code_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)  # محاذاة لليمين
                self.students_table.setItem(row, 1, code_item)

                # القسم (العمود الثالث)
                section_item = QTableWidgetItem(str(student[3]) if student[3] else "غير محدد")  # student[3] = القسم
                section_item.setFont(QFont("Calibri", 13, QFont.Bold))
                section_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)  # محاذاة لليمين
                self.students_table.setItem(row, 2, section_item)

                # رقم الترتيب (العمود الرابع)
                order_item = QTableWidgetItem(str(row + 1))
                order_item.setFont(QFont("Calibri", 13, QFont.Bold))
                order_item.setTextAlignment(Qt.AlignCenter)  # الأرقام في الوسط
                self.students_table.setItem(row, 3, order_item)

            # إعادة تفعيل التحديثات
            self.students_table.setUpdatesEnabled(True)

            print(f"✅ تم عرض {len(students_data)} تلميذ في الجدول بسرعة")

        except Exception as e:
            # إعادة تفعيل التحديثات في حالة الخطأ
            self.students_table.setUpdatesEnabled(True)
            print(f"❌ خطأ في عرض البيانات: {e}")
            raise

    def load_students_data_immediate(self):
        """تحميل فوري للبيانات بدون مؤشر تحميل"""
        try:
            print("🚀 تحميل فوري للبيانات...")

            # لا نعرض مؤشر تحميل أبداً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إعداد التصفية
            search_text = self.student_search.text().strip() if hasattr(self, 'student_search') else ""
            selected_section = self.section_combo.currentText() if hasattr(self, 'section_combo') else "جميع الأقسام"

            # بناء الاستعلام
            where_conditions = ["اسم_التلميذ IS NOT NULL", "اسم_التلميذ != ''"]
            params = []

            if search_text:
                where_conditions.append("اسم_التلميذ LIKE ?")
                params.append(f"%{search_text}%")

            if selected_section != "جميع الأقسام":
                where_conditions.append("القسم = ?")
                params.append(selected_section)

            query = f"""
                SELECT id, اسم_التلميذ, رمز_التلميذ
                FROM جدول_البيانات
                WHERE {' AND '.join(where_conditions)}
                ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
            """

            cursor.execute(query, params)
            students_basic = cursor.fetchall()

            # معالجة البيانات بسرعة - بدون حساب الغياب
            students = []
            for student_id, student_name, student_code in students_basic:
                # لا نحسب الغياب هنا - فقط البيانات الأساسية
                students.append((student_name, student_code or "غير محدد", 0, student_id))

            # عرض البيانات في الجدول
            self.students_table.setRowCount(len(students))
            for row, student in enumerate(students):
                # عدد حصص الغياب
                absence_item = QTableWidgetItem(str(student[2]))
                absence_item.setFont(QFont("Calibri", 14, QFont.Bold))
                absence_item.setTextAlignment(Qt.AlignCenter)

                if student[2] > 10:
                    absence_item.setBackground(QColor(250, 219, 216))
                    absence_item.setForeground(QColor(231, 76, 60))
                elif student[2] > 5:
                    absence_item.setBackground(QColor(255, 243, 205))
                    absence_item.setForeground(QColor(133, 100, 4))
                else:
                    absence_item.setBackground(QColor(213, 244, 230))
                    absence_item.setForeground(QColor(39, 174, 96))

                self.students_table.setItem(row, 0, absence_item)

                # اسم التلميذ
                name_item = QTableWidgetItem(str(student[0]))
                name_item.setFont(QFont("Calibri", 14, QFont.Bold))
                self.students_table.setItem(row, 1, name_item)

                # رمز التلميذ
                code_item = QTableWidgetItem(str(student[1]))
                code_item.setFont(QFont("Calibri", 14, QFont.Bold))
                code_item.setTextAlignment(Qt.AlignCenter)
                self.students_table.setItem(row, 2, code_item)

                # رقم الترتيب
                order_item = QTableWidgetItem(str(row + 1))
                order_item.setFont(QFont("Calibri", 14, QFont.Bold))
                order_item.setTextAlignment(Qt.AlignCenter)
                self.students_table.setItem(row, 3, order_item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(students)} تلميذ")
            print(f"✅ تم تحميل {len(students)} تلميذ فوراً")

        except Exception as e:
            print(f"❌ خطأ في التحميل الفوري: {e}")
            self.statusBar().showMessage(f"خطأ: {str(e)}")
        finally:
            # تأكد من عدم وجود مؤشر تحميل
            try:
                for _ in range(5):
                    QApplication.restoreOverrideCursor()
            except:
                pass

    def on_date_changed_safe(self):
        """معالج آمن لتغيير التاريخ - للتوافق"""
        self.on_date_changed_immediate()

    def on_date_changed(self):
        """معالج تغيير التاريخ القديم - للتوافق"""
        self.on_date_changed_immediate()

    def load_students_data_delayed(self):
        """تحميل البيانات مع تأخير - آمن من المؤشر الدوار"""
        print("⏰ انتهاء التأخير - تحميل فوري...")

        # تحميل فوري بدون مؤشر
        self.load_students_data_immediate()

        # إعادة تشغيل التنظيف الدوري
        if hasattr(self, 'cleanup_timer'):
            self.cleanup_timer.start(5000)

    def load_students_data_safe(self):
        """تحميل بيانات الطلاب بأمان - بدون مؤشر تحميل في البداية"""
        try:
            import time
            total_start = time.time()
            print("🔧 بدء تحميل بيانات التلاميذ الآمن...")

            # لا نعرض مؤشر التحميل في البداية لمنع المشكلة
            print("✅ تحميل آمن بدون مؤشر في البداية")

            # الاتصال بقاعدة البيانات
            db_start = time.time()
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            db_time = time.time() - db_start
            print(f"💾 الاتصال بقاعدة البيانات: {db_time:.3f} ثانية")

            # إعداد التصفية
            search_text = self.student_search.text().strip() if hasattr(self, 'student_search') else ""
            selected_section = self.section_combo.currentText() if hasattr(self, 'section_combo') else "جميع الأقسام"

            # بناء الاستعلام
            where_conditions = ["اسم_التلميذ IS NOT NULL", "اسم_التلميذ != ''"]
            params = []

            # تصفية بالاسم
            if search_text:
                where_conditions.append("اسم_التلميذ LIKE ?")
                params.append(f"%{search_text}%")

            # تصفية بالقسم
            if selected_section != "جميع الأقسام":
                where_conditions.append("القسم = ?")
                params.append(selected_section)

            query = f"""
                SELECT
                    id,
                    اسم_التلميذ,
                    رمز_التلميذ
                FROM جدول_البيانات
                WHERE {' AND '.join(where_conditions)}
                ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
            """

            print(f"🔍 الاستعلام: {query}")
            print(f"🔍 المعاملات: {params}")

            query_start = time.time()
            cursor.execute(query, params)
            students_basic = cursor.fetchall()
            query_time = time.time() - query_start

            print(f"📊 تم جلب {len(students_basic)} تلميذ من قاعدة البيانات")
            print(f"⏱️ وقت تنفيذ الاستعلام الأساسي: {query_time:.3f} ثانية")

            # الآن فقط نعرض مؤشر التحميل للعمليات الثقيلة
            if len(students_basic) > 100:
                print("⏳ عرض مؤشر التحميل للعمليات الثقيلة...")
                QApplication.setOverrideCursor(Qt.WaitCursor)

            # باقي الكود كما هو...
            self._process_students_data(students_basic, cursor, total_start)

            conn.close()

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل البيانات: {str(e)}")
            print(f"❌ خطأ في تحميل البيانات: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # تنظيف جميع مؤشرات التحميل مع تأخير
            QApplication.processEvents()
            self.clear_all_cursors()
            # تأخير إضافي لضمان التطبيق
            QTimer.singleShot(100, self.clear_all_cursors)

    def _process_students_data(self, students_basic, cursor, total_start):
        """معالجة بيانات التلاميذ"""
        import time

        if len(students_basic) == 0:
            print("⚠️ لا توجد بيانات تلاميذ!")
            # اختبار استعلام بسيط
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL")
            total_count = cursor.fetchone()[0]
            print(f"📊 إجمالي التلاميذ في قاعدة البيانات: {total_count}")

            if total_count > 0:
                cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL LIMIT 5")
                sample = cursor.fetchall()
                print(f"📝 عينة من البيانات: {sample}")

        # معالجة البيانات وحساب الغياب
        process_start = time.time()
        students = []
        print(f"🔄 بدء معالجة {len(students_basic)} تلميذ لحساب الغياب...")

        for i, student_basic in enumerate(students_basic):
            if i < 5:  # طباعة أول 5 للتشخيص
                print(f"   📋 معالجة التلميذ {i+1}: {student_basic}")

            student_id, student_name, student_code = student_basic

            # حساب عدد حصص الغياب من جدول تدوين_الغياب
            absence_start = time.time()
            cursor.execute("""
                SELECT SUM(عدد_الحصص_المتغيب_عنها)
                FROM تدوين_الغياب
                WHERE معرف_التلميذ = ?
            """, (student_id,))

            result = cursor.fetchone()[0]
            absence_count = result if result is not None else 0

            if i < 3:  # طباعة وقت أول 3 استعلامات
                absence_time = time.time() - absence_start
                print(f"     ⏱️ وقت حساب الغياب للتلميذ {i+1}: {absence_time:.3f} ثانية")

            students.append((
                student_name,
                student_code or "غير محدد",
                absence_count,
                student_id  # إضافة معرف التلميذ
            ))

            # معالجة الأحداث كل 100 تلميذ
            if i % 100 == 0 and i > 0:
                QApplication.processEvents()
                print(f"   🔄 تمت معالجة {i} تلميذ...")

        process_time = time.time() - process_start
        print(f"📊 تم معالجة {len(students)} تلميذ")
        print(f"⏱️ وقت معالجة جميع التلاميذ: {process_time:.3f} ثانية")

        # عرض البيانات في الجدول
        display_start = time.time()
        self.students_table.setRowCount(len(students))
        print(f"🔧 تم تعيين عدد الصفوف: {len(students)}")
        print(f"🖥️ بدء عرض البيانات في الجدول...")

        for row, student in enumerate(students):
            try:
                if row < 3:  # طباعة أول 3 صفوف للتشخيص
                    print(f"   📋 عرض الصف {row}: {student}")

                # عدد حصص الغياب (العمود الأول)
                absence_item = QTableWidgetItem(str(student[2]))
                absence_item.setFont(QFont("Calibri", 14, QFont.Bold))
                absence_item.setTextAlignment(Qt.AlignCenter)

                # تلوين حسب عدد الغياب باستخدام setBackground و setForeground
                if student[2] > 10:
                    absence_item.setBackground(QColor(250, 219, 216))  # أحمر فاتح
                    absence_item.setForeground(QColor(231, 76, 60))    # أحمر داكن
                elif student[2] > 5:
                    absence_item.setBackground(QColor(255, 243, 205))  # أصفر فاتح
                    absence_item.setForeground(QColor(133, 100, 4))    # أصفر داكن
                else:
                    absence_item.setBackground(QColor(213, 244, 230))  # أخضر فاتح
                    absence_item.setForeground(QColor(39, 174, 96))    # أخضر داكن

                self.students_table.setItem(row, 0, absence_item)

                # اسم التلميذ (العمود الثاني)
                name_item = QTableWidgetItem(str(student[0]))
                name_item.setFont(QFont("Calibri", 14, QFont.Bold))
                self.students_table.setItem(row, 1, name_item)

                # رمز التلميذ (العمود الثالث)
                code_item = QTableWidgetItem(str(student[1]))
                code_item.setFont(QFont("Calibri", 14, QFont.Bold))
                code_item.setTextAlignment(Qt.AlignCenter)
                self.students_table.setItem(row, 2, code_item)

                # رقم الترتيب (العمود الرابع)
                order_item = QTableWidgetItem(str(row + 1))
                order_item.setFont(QFont("Calibri", 14, QFont.Bold))
                order_item.setTextAlignment(Qt.AlignCenter)
                self.students_table.setItem(row, 3, order_item)

                if row < 3:
                    print(f"      ✅ تم عرض الصف {row}: {student[0]} | {student[1]} | {student[2]} غياب")

            except Exception as e:
                print(f"❌ خطأ في عرض الصف {row}: {e}")
                # إضافة صفوف فارغة عند الخطأ
                for col in range(4):
                    try:
                        empty_item = QTableWidgetItem("خطأ")
                        self.students_table.setItem(row, col, empty_item)
                    except:
                        pass

        # فحص نهائي
        display_time = time.time() - display_start
        actual_rows = self.students_table.rowCount()
        print(f"🔍 فحص نهائي: الجدول يحتوي على {actual_rows} صف")
        print(f"⏱️ وقت عرض البيانات في الجدول: {display_time:.3f} ثانية")

        total_time = time.time() - total_start
        print(f"⏱️ إجمالي وقت تحميل البيانات: {total_time:.3f} ثانية")
        self.statusBar().showMessage(f"تم تحميل {len(students)} تلميذ")

    def load_students_data(self):
        """تحميل بيانات الطلاب - يستخدم الخيط المنفصل"""
        self.load_students_data_threaded()

    def mark_student_absent(self):
        """تسجيل غياب التلميذ المحدد"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تلميذ لتسجيل غيابه")
            return
        
        self._process_absence(selected_rows)

    def bulk_mark_absent(self):
        """تسجيل غياب جماعي"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار التلاميذ لتسجيل غيابهم")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الغياب الجماعي",
            f"هل أنت متأكد من تسجيل غياب {len(selected_rows)} تلميذ؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._process_absence(selected_rows)

    def _process_absence(self, selected_rows):
        """معالجة تسجيل الغياب المحسنة والسريعة"""
        try:
            import time
            total_start = time.time()
            # إظهار مؤشر التحميل
            QApplication.setOverrideCursor(Qt.WaitCursor)
            print(f"🔧 بدء معالجة تسجيل الغياب لـ {len(selected_rows)} تلميذ")
            print(f"⏳ تم عرض مؤشر التحميل")

            # إزالة مؤشر التحميل مؤقتاً لعرض النافذة
            QApplication.restoreOverrideCursor()

            # فتح نافذة واحدة لتحديد الحصص لجميع التلاميذ
            session_dialog = AbsenceSessionDialog("تسجيل غياب جماعي", self)
            if session_dialog.exec_() != QDialog.Accepted:
                print("❌ تم إلغاء تسجيل الغياب")
                return

            # إعادة إظهار مؤشر التحميل
            QApplication.setOverrideCursor(Qt.WaitCursor)

            selected_sessions = session_dialog.get_selected_sessions()
            notes = session_dialog.get_notes()

            if not selected_sessions:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار الحصص المتغيب عنها")
                return

            print(f"📝 الحصص المحددة: {selected_sessions}")

            # إنشاء شريط تقدم
            progress_start = time.time()
            progress = QProgressDialog("جاري تسجيل الغياب...", "إلغاء", 0, len(selected_rows), self)
            progress.setWindowTitle("تسجيل الغياب")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            print(f"📊 تم إنشاء شريط التقدم")

            # الاتصال بقاعدة البيانات
            db_start = time.time()
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            db_time = time.time() - db_start
            print(f"💾 الاتصال بقاعدة البيانات: {db_time:.3f} ثانية")

            current_date = self.date_edit.date().toString("yyyy-MM-dd")
            success_count = 0
            failed_students = []

            # تحديد الحصص المتغيب عنها مرة واحدة
            session_1 = 1 if "الحصة الأولى" in selected_sessions else 0
            session_2 = 1 if "الحصة الثانية" in selected_sessions else 0
            session_3 = 1 if "الحصة الثالثة" in selected_sessions else 0
            total_absent_sessions = session_1 + session_2 + session_3

            # 🚀 تحسين الأداء: تجميع بيانات التلاميذ المحددين مسبقاً
            print(f"🔍 جمع بيانات {len(selected_rows)} تلميذ محدد...")
            students_data = {}

            for index in selected_rows:
                row = index.row()
                if (self.students_table.item(row, 0) and
                    self.students_table.item(row, 1)):
                    student_name = self.students_table.item(row, 0).text()
                    student_code = self.students_table.item(row, 1).text()
                    students_data[student_name] = {
                        'code': student_code,
                        'row': row
                    }

            # جلب معرفات التلاميذ دفعة واحدة
            student_names = list(students_data.keys())
            if student_names:
                placeholders = ','.join(['?' for _ in student_names])
                cursor.execute(f"""
                    SELECT اسم_التلميذ, id, القسم
                    FROM جدول_البيانات
                    WHERE اسم_التلميذ IN ({placeholders})
                """, student_names)

                db_students = cursor.fetchall()
                for student_name, student_id, section in db_students:
                    if student_name in students_data:
                        students_data[student_name]['id'] = student_id
                        students_data[student_name]['section'] = section

            print(f"✅ تم جمع بيانات {len(db_students)} تلميذ من قاعدة البيانات")

            # معالجة سريعة للتلاميذ المحددين
            processing_start = time.time()
            print(f"🔄 بدء معالجة {len(students_data)} تلميذ...")

            for i, (student_name, data) in enumerate(students_data.items()):
                if progress.wasCanceled():
                    print("❌ تم إلغاء العملية من قبل المستخدم")
                    break

                progress.setValue(i)
                progress.setLabelText(f"معالجة التلميذ {i+1} من {len(students_data)}")

                # تحديث واجهة المستخدم كل 5 تلاميذ فقط
                if i % 5 == 0:
                    QApplication.processEvents()
                    if i > 0:
                        print(f"   🔄 تمت معالجة {i} تلميذ...")

                try:
                    # التحقق من وجود البيانات المطلوبة
                    if 'id' not in data or 'section' not in data:
                        failed_students.append(f"{student_name}: غير موجود في قاعدة البيانات")
                        continue

                    student_id = data['id']
                    student_code = data['code']
                    section = data['section']

                    # التحقق من وجود سجل في جدول تدوين الغياب لنفس اليوم
                    cursor.execute("""
                        SELECT id FROM تدوين_الغياب
                        WHERE معرف_التلميذ = ? AND تاريخ_الغياب = ?
                    """, (student_id, current_date))

                    existing_record = cursor.fetchone()

                    if existing_record:
                        # تحديث السجل الموجود
                        cursor.execute("""
                            UPDATE تدوين_الغياب
                            SET الحصة_الاولى = ?, الحصة_الثانية = ?, الحصة_الثالثة = ?,
                                عدد_الحصص_المتغيب_عنها = ?, ملاحظات = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (session_1, session_2, session_3, total_absent_sessions, notes, existing_record[0]))
                    else:
                        # إنشاء سجل جديد
                        cursor.execute("""
                            INSERT INTO تدوين_الغياب
                            (معرف_التلميذ, اسم_التلميذ, رمز_التلميذ, القسم, تاريخ_الغياب,
                             الحصة_الاولى, الحصة_الثانية, الحصة_الثالثة, عدد_الحصص_المتغيب_عنها, ملاحظات)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (student_id, student_name, student_code, section, current_date,
                              session_1, session_2, session_3, total_absent_sessions, notes))

                    success_count += 1

                except Exception as student_error:
                    failed_students.append(f"{student_name if 'student_name' in locals() else f'التلميذ {i+1}'}: {str(student_error)}")
                    continue

            conn.commit()
            conn.close()

            # تحديث الإحصائيات دفعة واحدة (اتصال منفصل)
            progress.setLabelText("تحديث الإحصائيات...")
            QApplication.processEvents()

            if success_count > 0:
                self.update_statistics_batch(current_date)

            progress.close()

            processing_time = time.time() - processing_start
            print(f"✅ انتهاء المعالجة - تم تسجيل {success_count} سجل غياب")
            print(f"⏱️ وقت معالجة جميع التلاميذ: {processing_time:.3f} ثانية")

            # 🚀 تحسين: لا نحتاج لإعادة تحميل جميع البيانات
            # فقط تحديث الجدول بدون إعادة تحميل من قاعدة البيانات
            refresh_start = time.time()
            print("🔄 تحديث سريع للجدول...")
            # لا حاجة لإعادة تحميل البيانات - الجدول محدث بالفعل
            refresh_time = time.time() - refresh_start
            print(f"🔄 وقت تحديث الجدول: {refresh_time:.3f} ثانية")

            total_time = time.time() - total_start
            print(f"⏱️ إجمالي وقت تسجيل الغياب: {total_time:.3f} ثانية")

            # عرض النتائج
            if success_count > 0:
                message = f"تم تسجيل الغياب لـ {success_count} تلميذ بنجاح"
                if failed_students:
                    message += f"\n\nفشل في تسجيل {len(failed_students)} تلميذ:\n" + "\n".join(failed_students[:5])
                    if len(failed_students) > 5:
                        message += f"\n... و {len(failed_students) - 5} آخرين"

                QMessageBox.information(self, "نتائج تسجيل الغياب", message)

                # إلغاء التحديد بعد تسجيل الغياب بنجاح
                self.students_table.clearSelection()
                print("🔄 تم إلغاء تحديد التلاميذ بعد تسجيل الغياب")
            else:
                error_message = "لم يتم تسجيل أي غياب"
                if failed_students:
                    error_message += f"\n\nالأخطاء:\n" + "\n".join(failed_students[:10])
                QMessageBox.warning(self, "تنبيه", error_message)

        except Exception as e:
            error_msg = f"حدث خطأ في تسجيل الغياب:\n{str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)
            import traceback
            traceback.print_exc()
        finally:
            # تنظيف جميع مؤشرات التحميل مع تأخير
            QApplication.processEvents()
            self.clear_all_cursors()
            # تأخير إضافي لضمان التطبيق
            QTimer.singleShot(200, self.clear_all_cursors)

    def update_statistics_batch(self, absence_date):
        """تحديث الإحصائيات دفعة واحدة لتجنب قفل قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على جميع التلاميذ الذين لديهم غياب في هذا التاريخ
            cursor.execute("""
                SELECT DISTINCT معرف_التلميذ FROM تدوين_الغياب
                WHERE تاريخ_الغياب = ?
            """, (absence_date,))

            updated_students = cursor.fetchall()

            # استخراج السنة والشهر من التاريخ
            from datetime import datetime
            date_obj = datetime.strptime(absence_date, "%Y-%m-%d")
            year = date_obj.year
            month = date_obj.month

            # أسماء الشهور بالعربية
            month_names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                          "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            month_name = month_names[month - 1]

            for (student_id,) in updated_students:
                # الحصول على بيانات التلميذ
                cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE id = ?", (student_id,))
                student_data = cursor.fetchone()

                if not student_data:
                    continue

                student_name, student_code, section = student_data

                # حساب إجمالي الحصص المتغيب عنها في الشهر
                cursor.execute("""
                    SELECT SUM(عدد_الحصص_المتغيب_عنها), COUNT(DISTINCT تاريخ_الغياب)
                    FROM تدوين_الغياب
                    WHERE معرف_التلميذ = ? AND strftime('%Y', تاريخ_الغياب) = ? AND strftime('%m', تاريخ_الغياب) = ?
                """, (student_id, str(year), f"{month:02d}"))

                result = cursor.fetchone()
                absent_sessions = result[0] if result[0] else 0
                absent_days = result[1] if result[1] else 0

                # افتراض 3 حصص يومياً × 20 يوم دراسي = 60 حصة شهرياً
                total_sessions = 60
                attendance_rate = ((total_sessions - absent_sessions) / total_sessions) * 100 if total_sessions > 0 else 100
                absence_rate = (absent_sessions / total_sessions) * 100 if total_sessions > 0 else 0

                # تحديث أو إدراج الإحصائيات الشهرية
                cursor.execute("""
                    INSERT OR REPLACE INTO احصائيات_الغياب_الشهرية
                    (معرف_التلميذ, اسم_التلميذ, رمز_التلميذ, القسم, السنة, الشهر, اسم_الشهر,
                     اجمالي_الحصص, الحصص_المتغيب_عنها, نسبة_الحضور, نسبة_الغياب, عدد_ايام_الغياب)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (student_id, student_name, student_code, section, year, month, month_name,
                      total_sessions, absent_sessions, attendance_rate, absence_rate, absent_days))

            conn.commit()
            conn.close()
            print(f"✅ تم تحديث إحصائيات {len(updated_students)} تلميذ")

        except Exception as e:
            print(f"❌ خطأ في تحديث الإحصائيات الدفعية: {e}")

    def update_monthly_statistics(self, student_id, absence_date):
        """تحديث الإحصائيات الشهرية للتلميذ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج السنة والشهر من التاريخ
            from datetime import datetime
            date_obj = datetime.strptime(absence_date, "%Y-%m-%d")
            year = date_obj.year
            month = date_obj.month

            # أسماء الشهور بالعربية
            month_names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                          "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            month_name = month_names[month - 1]

            # الحصول على بيانات التلميذ
            cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE id = ?", (student_id,))
            student_data = cursor.fetchone()

            if not student_data:
                return

            student_name, student_code, section = student_data

            # حساب إجمالي الحصص المتغيب عنها في الشهر
            cursor.execute("""
                SELECT SUM(عدد_الحصص_المتغيب_عنها), COUNT(DISTINCT تاريخ_الغياب)
                FROM تدوين_الغياب
                WHERE معرف_التلميذ = ? AND strftime('%Y', تاريخ_الغياب) = ? AND strftime('%m', تاريخ_الغياب) = ?
            """, (student_id, str(year), f"{month:02d}"))

            result = cursor.fetchone()
            absent_sessions = result[0] if result[0] else 0
            absent_days = result[1] if result[1] else 0

            # افتراض 3 حصص يومياً × 20 يوم دراسي = 60 حصة شهرياً
            total_sessions = 60
            attendance_rate = ((total_sessions - absent_sessions) / total_sessions) * 100 if total_sessions > 0 else 100
            absence_rate = (absent_sessions / total_sessions) * 100 if total_sessions > 0 else 0

            # التحقق من وجود سجل إحصائيات للشهر
            cursor.execute("""
                SELECT id FROM احصائيات_الغياب_الشهرية
                WHERE معرف_التلميذ = ? AND السنة = ? AND الشهر = ?
            """, (student_id, year, month))

            existing_stat = cursor.fetchone()

            if existing_stat:
                # تحديث الإحصائيات الموجودة
                cursor.execute("""
                    UPDATE احصائيات_الغياب_الشهرية
                    SET اجمالي_الحصص = ?, الحصص_المتغيب_عنها = ?, نسبة_الحضور = ?,
                        نسبة_الغياب = ?, عدد_ايام_الغياب = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (total_sessions, absent_sessions, attendance_rate, absence_rate, absent_days, existing_stat[0]))
            else:
                # إنشاء سجل إحصائيات جديد
                cursor.execute("""
                    INSERT INTO احصائيات_الغياب_الشهرية
                    (معرف_التلميذ, اسم_التلميذ, رمز_التلميذ, القسم, السنة, الشهر, اسم_الشهر,
                     اجمالي_الحصص, الحصص_المتغيب_عنها, نسبة_الحضور, نسبة_الغياب, عدد_ايام_الغياب)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (student_id, student_name, student_code, section, year, month, month_name,
                      total_sessions, absent_sessions, attendance_rate, absence_rate, absent_days))

            # تحديث الإحصائيات السنوية
            self.update_yearly_statistics(student_id, year)

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحديث الإحصائيات الشهرية: {e}")

    def update_yearly_statistics(self, student_id, year):
        """تحديث الإحصائيات السنوية للتلميذ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على بيانات التلميذ
            cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE id = ?", (student_id,))
            student_data = cursor.fetchone()

            if not student_data:
                return

            student_name, student_code, section = student_data

            # حساب الإحصائيات السنوية من البيانات الشهرية
            cursor.execute("""
                SELECT SUM(اجمالي_الحصص), SUM(الحصص_المتغيب_عنها), SUM(عدد_ايام_الغياب),
                       MIN(نسبة_الحضور), MAX(نسبة_الحضور), اسم_الشهر
                FROM احصائيات_الغياب_الشهرية
                WHERE معرف_التلميذ = ? AND السنة = ?
                GROUP BY اسم_الشهر
                ORDER BY نسبة_الحضور ASC
            """, (student_id, year))

            monthly_stats = cursor.fetchall()

            if not monthly_stats:
                return

            total_sessions = sum(stat[0] for stat in monthly_stats if stat[0])
            total_absent = sum(stat[1] for stat in monthly_stats if stat[1])
            total_absent_days = sum(stat[2] for stat in monthly_stats if stat[2])

            attendance_rate = ((total_sessions - total_absent) / total_sessions) * 100 if total_sessions > 0 else 100
            absence_rate = (total_absent / total_sessions) * 100 if total_sessions > 0 else 0

            # أسوأ وأفضل شهر
            worst_month = monthly_stats[0][5] if monthly_stats else ""
            best_month = monthly_stats[-1][5] if monthly_stats else ""

            # التحقق من وجود سجل إحصائيات للسنة
            cursor.execute("""
                SELECT id FROM احصائيات_الغياب_السنوية
                WHERE معرف_التلميذ = ? AND السنة = ?
            """, (student_id, year))

            existing_stat = cursor.fetchone()

            if existing_stat:
                # تحديث الإحصائيات الموجودة
                cursor.execute("""
                    UPDATE احصائيات_الغياب_السنوية
                    SET اجمالي_الحصص = ?, الحصص_المتغيب_عنها = ?, نسبة_الحضور = ?,
                        نسبة_الغياب = ?, عدد_ايام_الغياب = ?, اسوأ_شهر = ?, افضل_شهر = ?,
                        تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (total_sessions, total_absent, attendance_rate, absence_rate,
                      total_absent_days, worst_month, best_month, existing_stat[0]))
            else:
                # إنشاء سجل إحصائيات جديد
                cursor.execute("""
                    INSERT INTO احصائيات_الغياب_السنوية
                    (معرف_التلميذ, اسم_التلميذ, رمز_التلميذ, القسم, السنة,
                     اجمالي_الحصص, الحصص_المتغيب_عنها, نسبة_الحضور, نسبة_الغياب,
                     عدد_ايام_الغياب, اسوأ_شهر, افضل_شهر)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (student_id, student_name, student_code, section, year,
                      total_sessions, total_absent, attendance_rate, absence_rate,
                      total_absent_days, worst_month, best_month))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحديث الإحصائيات السنوية: {e}")

    def generate_absence_report(self):
        """إنشاء تقرير الغياب"""
        try:
            # إنشاء نافذة تقرير الغياب مدمجة
            report_dialog = SimpleAbsenceReportDialog(self.db_path, self)
            report_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الغياب: {str(e)}")

    def show_statistics(self):
        """عرض الإحصائيات المتقدمة"""
        try:
            # إنشاء نافذة الإحصائيات مدمجة
            stats_dialog = SimpleStatisticsDialog(self.db_path, self)
            stats_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات:\n{str(e)}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.load_sections()
        self.load_students_data()
        self.statusBar().showMessage("تم تحديث جميع البيانات")

    def delete_all_absence_records(self):
        """حذف جميع سجلات الغياب مع تأكيد برقم 12345"""
        try:
            # طلب رقم التأكيد من المستخدم
            confirmation_code, ok = QInputDialog.getText(
                self,
                "تأكيد حذف جميع سجلات الغياب",
                "⚠️ تحذير: هذه العملية ستحذف جميع سجلات الغياب نهائياً!\n\n"
                "لتأكيد العملية، يرجى إدخال رقم التأكيد:",
                QLineEdit.Password
            )

            if not ok:
                return

            # التحقق من رقم التأكيد
            if confirmation_code != "12345":
                QMessageBox.warning(
                    self,
                    "رقم تأكيد خاطئ",
                    "رقم التأكيد غير صحيح. لم يتم حذف أي سجلات."
                )
                return

            # تأكيد إضافي
            reply = QMessageBox.question(
                self,
                "تأكيد نهائي",
                "⚠️ هل أنت متأكد من حذف جميع سجلات الغياب؟\n\n"
                "هذه العملية لا يمكن التراجع عنها!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تنفيذ عملية الحذف
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حساب عدد السجلات قبل الحذف
            cursor.execute("SELECT COUNT(*) FROM تدوين_الغياب")
            total_records = cursor.fetchone()[0]

            if total_records == 0:
                QMessageBox.information(
                    self,
                    "لا توجد سجلات",
                    "لا توجد سجلات غياب للحذف."
                )
                conn.close()
                return

            # حذف جميع السجلات
            cursor.execute("DELETE FROM تدوين_الغياب")
            cursor.execute("DELETE FROM احصائيات_الغياب_الشهرية")
            cursor.execute("DELETE FROM احصائيات_الغياب_السنوية")

            conn.commit()
            conn.close()

            # إعادة تحميل البيانات
            self.load_students_data()

            # رسالة نجاح
            QMessageBox.information(
                self,
                "تم الحذف بنجاح",
                f"✅ تم حذف {total_records} سجل غياب بنجاح!\n\n"
                "تم أيضاً حذف جميع الإحصائيات المرتبطة."
            )

            print(f"✅ تم حذف {total_records} سجل غياب بنجاح")

        except Exception as e:
            error_msg = f"خطأ في حذف سجلات الغياب: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)



    def generate_monthly_attendance_sheet(self):
        """إنشاء ورقة الغياب الشهرية للقسم المحدد أو جميع الأقسام"""
        try:
            # الحصول على الشهر والسنة من التاريخ المحدد في النافذة
            selected_date = self.date_edit.date().toPyDate()
            year = selected_date.year
            month = selected_date.month

            # أسماء الشهور بالعربية
            month_names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                          "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            month_name = month_names[month - 1]

            selected_section = self.section_combo.currentText()

            # تحديد الأقسام المطلوب إنشاء تقارير لها
            if selected_section == "جميع الأقسام":
                # إنشاء تقارير لجميع الأقسام
                print(f"🔧 إنشاء أوراق الغياب الشهرية لجميع الأقسام لشهر: {month_name} {year}")

                # الحصول على جميع الأقسام
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != '' ORDER BY القسم")
                sections = [row[0] for row in cursor.fetchall()]
                conn.close()

                if not sections:
                    QMessageBox.warning(self, "تحذير", "لا توجد أقسام في قاعدة البيانات")
                    return

                print(f"📊 تم العثور على {len(sections)} قسم: {sections}")

                # إنشاء شريط التحميل
                progress = QProgressDialog(f"جاري إنشاء أوراق الغياب الشهرية لشهر {month_name} {year}...", "إلغاء", 0, len(sections), self)
                progress.setWindowTitle("إنشاء أوراق الغياب")
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                # استيراد نظام التقارير
                from attendance_sheet_report import create_attendance_sheet_report

                successful_reports = []
                failed_reports = []

                # إنشاء ورقة لكل قسم
                for i, section in enumerate(sections):
                    if progress.wasCanceled():
                        break

                    progress.setValue(i)
                    progress.setLabelText(f"إنشاء ورقة القسم: {section}\n({i+1} من {len(sections)})")
                    QApplication.processEvents()

                    try:
                        print(f"🔧 إنشاء ورقة للقسم: {section}")

                        success, output_path, message = create_attendance_sheet_report(
                            section, year, month, month_name, self.db_path
                        )

                        if success:
                            successful_reports.append((section, output_path))
                            print(f"✅ تم إنشاء ورقة القسم {section}: {output_path}")
                        else:
                            failed_reports.append((section, message))
                            print(f"❌ فشل إنشاء ورقة القسم {section}: {message}")

                    except Exception as e:
                        error_msg = f"خطأ في إنشاء ورقة القسم {section}: {str(e)}"
                        failed_reports.append((section, error_msg))
                        print(f"❌ {error_msg}")

                progress.setValue(len(sections))

                # عرض النتائج
                if successful_reports and not failed_reports:
                    # جميع الأوراق نجحت
                    report_list = "\n".join([f"• {section}" for section, _ in successful_reports])
                    QMessageBox.information(self, "نجح",
                        f"تم إنشاء جميع أوراق الغياب بنجاح! ✅\n\n"
                        f"الشهر: {month_name} {year}\n"
                        f"عدد الأقسام: {len(successful_reports)}\n\n"
                        f"الأقسام:\n{report_list}")

                elif successful_reports and failed_reports:
                    # بعض الأوراق نجحت وبعضها فشل
                    success_list = "\n".join([f"✅ {section}" for section, _ in successful_reports])
                    failed_list = "\n".join([f"❌ {section}: {error}" for section, error in failed_reports])
                    QMessageBox.warning(self, "نجح جزئياً",
                        f"تم إنشاء {len(successful_reports)} ورقة من أصل {len(sections)}\n\n"
                        f"الأوراق الناجحة:\n{success_list}\n\n"
                        f"الأوراق الفاشلة:\n{failed_list}")

                else:
                    # جميع الأوراق فشلت
                    failed_list = "\n".join([f"❌ {section}: {error}" for section, error in failed_reports])
                    QMessageBox.critical(self, "فشل",
                        f"فشل في إنشاء جميع أوراق الغياب!\n\n{failed_list}")

            else:
                # إنشاء ورقة لقسم محدد
                if not selected_section:
                    QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم")
                    return

                print(f"🔧 إنشاء ورقة الغياب الشهرية للقسم: {selected_section} لشهر: {month_name} {year}")

                # استيراد نظام التقارير
                from attendance_sheet_report import create_attendance_sheet_report

                success, output_path, message = create_attendance_sheet_report(
                    selected_section, year, month, month_name, self.db_path
                )

                if success:
                    QMessageBox.information(self, "نجح",
                        f"تم إنشاء ورقة الغياب بنجاح! ✅\n\n"
                        f"القسم: {selected_section}\n"
                        f"الشهر: {month_name} {year}\n\n"
                        f"المسار: {output_path}")
                else:
                    QMessageBox.critical(self, "خطأ", message)

        except Exception as e:
            error_msg = f"خطأ في إنشاء ورقة الغياب: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)

    def show_detailed_report_menu(self):
        """عرض قائمة التقارير التفصيلية"""
        from PyQt5.QtWidgets import QMenu

        menu = QMenu(self)
        menu.setFont(QFont("Calibri", 12, QFont.Bold))
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 2px solid #8e44ad;
                border-radius: 8px;
                padding: 5px;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                border-radius: 4px;
                color: #2c3e50;
            }
            QMenu::item:selected {
                background-color: #8e44ad;
                color: white;
            }
        """)

        # تقرير الغياب حسب التلميذ
        student_action = menu.addAction("👤 تقرير الغياب حسب التلميذ")
        student_action.triggered.connect(self.generate_student_absence_report)

        # تقرير الغياب حسب القسم
        section_action = menu.addAction("🏫 تقرير الغياب حسب القسم")
        section_action.triggered.connect(self.generate_section_absence_report)

        # إحصائيات الغياب
        stats_action = menu.addAction("📈 إحصائيات الغياب الورقية")
        stats_action.triggered.connect(self.generate_absence_statistics_report)

        # عرض القائمة
        menu.exec_(self.sender().mapToGlobal(self.sender().rect().bottomLeft()))

    def generate_student_absence_report(self):
        """إنشاء تقرير الغياب حسب التلميذ المحدد في الجدول"""
        try:
            # التحقق من وجود تلميذ محدد في الجدول
            current_row = self.students_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد تلميذ من الجدول أولاً")
                return

            # الحصول على اسم التلميذ المحدد
            student_name = self.students_table.item(current_row, 0).text()

            if not student_name:
                QMessageBox.warning(self, "تحذير", "اسم التلميذ غير صحيح")
                return

            # إنشاء التقرير مباشرة
            success, result = create_student_report_direct(student_name, self.db_path)

            if success:
                QMessageBox.information(
                    self,
                    "نجح",
                    f"✅ تم إنشاء تقرير الغياب للتلميذ: {student_name}\n\n"
                    f"📁 المسار: {result}"
                )

                # إلغاء التحديد بعد إنشاء التقرير بنجاح
                self.students_table.clearSelection()
                print("🔄 تم إلغاء تحديد التلميذ بعد إنشاء التقرير")
            else:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"❌ فشل في إنشاء التقرير:\n{result}"
                )

        except Exception as e:
            error_msg = f"خطأ في إنشاء تقرير الغياب حسب التلميذ: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)

    # تم نقل دوال إنشاء PDF إلى ملف print144.py
    def create_student_absence_pdf(self, selected_section, year, month, month_name):
        """إنشاء PDF لتقرير الغياب حسب التلميذ"""
        try:
            import os
            from datetime import datetime
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            # فئة PDF مع دعم العربية (نفس attendance_sheet_report.py)
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P', 'mm', 'A4')

                    # إضافة الخطوط
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                def ar_text(self, txt):
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

                def set_arabic_font(self, size=12, style='B'):
                    if style == 'B' and self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    elif style == '' and self.calibri_available:
                        self.set_font('Calibri', '', size)
                    else:
                        self.set_font('Arial', style, size)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()
            pdf.set_arabic_font(16, 'B')

            # العنوان
            pdf.cell(0, 10, pdf.ar_text(f"تقرير الغياب حسب التلميذ - {month_name} {year}"), 0, 1, 'C')
            pdf.ln(5)

            # جلب بيانات الغياب
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحديد الاستعلام حسب القسم المحدد
            if selected_section == "جميع الأقسام":
                query = """
                    SELECT
                        ت.اسم_التلميذ,
                        ت.رمز_التلميذ,
                        ت.القسم,
                        COUNT(غ.id) as عدد_ايام_الغياب,
                        SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_الحصص_المتغيب_عنها
                    FROM جدول_البيانات ت
                    LEFT JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
                        AND strftime('%Y', غ.تاريخ_الغياب) = ?
                        AND strftime('%m', غ.تاريخ_الغياب) = ?
                    GROUP BY ت.id, ت.اسم_التلميذ, ت.رمز_التلميذ, ت.القسم
                    HAVING عدد_ايام_الغياب > 0
                    ORDER BY ت.القسم, اجمالي_الحصص_المتغيب_عنها DESC
                """
                params = (str(year), f"{month:02d}")
            else:
                query = """
                    SELECT
                        ت.اسم_التلميذ,
                        ت.رمز_التلميذ,
                        ت.القسم,
                        COUNT(غ.id) as عدد_ايام_الغياب,
                        SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_الحصص_المتغيب_عنها
                    FROM جدول_البيانات ت
                    LEFT JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
                        AND strftime('%Y', غ.تاريخ_الغياب) = ?
                        AND strftime('%m', غ.تاريخ_الغياب) = ?
                    WHERE ت.القسم = ?
                    GROUP BY ت.id, ت.اسم_التلميذ, ت.رمز_التلميذ, ت.القسم
                    HAVING عدد_ايام_الغياب > 0
                    ORDER BY اجمالي_الحصص_المتغيب_عنها DESC
                """
                params = (str(year), f"{month:02d}", selected_section)

            cursor.execute(query, params)
            students_data = cursor.fetchall()

            if not students_data:
                pdf.set_arabic_font(12, '')
                pdf.cell(0, 10, pdf.ar_text("لا توجد بيانات غياب للفترة المحددة"), 0, 1, 'C')
            else:
                # رأس الجدول
                pdf.set_arabic_font(10, 'B')
                pdf.cell(40, 8, pdf.ar_text("اسم التلميذ"), 1, 0, 'C')
                pdf.cell(20, 8, pdf.ar_text("الرمز"), 1, 0, 'C')
                pdf.cell(30, 8, pdf.ar_text("القسم"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("حصص الغياب"), 1, 1, 'C')

                # بيانات التلاميذ
                pdf.set_arabic_font(9, '')
                for student in students_data:
                    name, code, section, absence_days, absence_sessions = student
                    pdf.cell(40, 6, pdf.ar_text(str(name or "")), 1, 0, 'C')
                    pdf.cell(20, 6, pdf.ar_text(str(code or "")), 1, 0, 'C')
                    pdf.cell(30, 6, pdf.ar_text(str(section or "")), 1, 0, 'C')
                    pdf.cell(25, 6, str(absence_days or 0), 1, 0, 'C')
                    pdf.cell(25, 6, str(absence_sessions or 0), 1, 1, 'C')

            conn.close()

            # حفظ الملف
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            section_name = selected_section.replace("/", "_").replace("\\", "_").replace(" ", "_")
            filename = f"تقرير_الغياب_حسب_التلميذ_{section_name}_{month_name}_{year}_{timestamp}.pdf"
            output_path = os.path.join(reports_dir, filename)

            pdf.output(output_path)

            # فتح الملف
            if os.name == 'nt':  # Windows
                os.startfile(output_path)

            return True, output_path

        except Exception as e:
            print(f"❌ خطأ في إنشاء PDF: {e}")
            return False, None

    def generate_section_absence_report(self):
        """إنشاء تقرير الغياب حسب القسم المحدد"""
        try:
            # التحقق من وجود قسم محدد
            selected_section = self.section_combo.currentText()

            if not selected_section or selected_section == "اختر القسم...":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم من القائمة أولاً")
                return

            # إنشاء التقرير مباشرة
            success, result = create_section_report_direct(selected_section, self.db_path)

            if success:
                QMessageBox.information(
                    self,
                    "نجح",
                    f"✅ تم إنشاء تقرير الغياب للقسم: {selected_section}\n\n"
                    f"📁 المسار: {result}"
                )

                # إلغاء التحديد بعد إنشاء التقرير بنجاح
                self.section_combo.setCurrentIndex(0)  # العودة لـ "اختر القسم..."
                print("🔄 تم إلغاء تحديد القسم بعد إنشاء التقرير")
            else:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"❌ فشل في إنشاء التقرير:\n{result}"
                )

        except Exception as e:
            error_msg = f"خطأ في إنشاء تقرير الغياب حسب القسم: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)

    def create_section_absence_pdf(self, year, month, month_name):
        """إنشاء PDF لتقرير الغياب حسب القسم"""
        try:
            import os
            from datetime import datetime
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            # فئة PDF مع دعم العربية (نفس الفئة المحسنة)
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P', 'mm', 'A4')

                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                def ar_text(self, txt):
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

                def set_arabic_font(self, size=12, style='B'):
                    if style == 'B' and self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    elif style == '' and self.calibri_available:
                        self.set_font('Calibri', '', size)
                    else:
                        self.set_font('Arial', style, size)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()
            pdf.set_arabic_font(16, 'B')

            # العنوان
            pdf.cell(0, 10, pdf.ar_text(f"تقرير الغياب حسب القسم - {month_name} {year}"), 0, 1, 'C')
            pdf.ln(5)

            # جلب بيانات الغياب حسب القسم
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = """
                SELECT
                    ت.القسم,
                    COUNT(DISTINCT ت.id) as عدد_التلاميذ,
                    COUNT(DISTINCT غ.معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                    COUNT(غ.id) as اجمالي_ايام_الغياب,
                    SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب,
                    ROUND(AVG(غ.عدد_الحصص_المتغيب_عنها), 2) as متوسط_حصص_الغياب_يوميا
                FROM جدول_البيانات ت
                LEFT JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
                    AND strftime('%Y', غ.تاريخ_الغياب) = ?
                    AND strftime('%m', غ.تاريخ_الغياب) = ?
                WHERE ت.القسم IS NOT NULL AND ت.القسم != ''
                GROUP BY ت.القسم
                ORDER BY اجمالي_حصص_الغياب DESC
            """

            cursor.execute(query, (str(year), f"{month:02d}"))
            sections_data = cursor.fetchall()

            if not sections_data:
                pdf.set_arabic_font(12, '')
                pdf.cell(0, 10, pdf.ar_text("لا توجد بيانات غياب للفترة المحددة"), 0, 1, 'C')
            else:
                # رأس الجدول
                pdf.set_arabic_font(8, 'B')
                pdf.cell(30, 8, pdf.ar_text("القسم"), 1, 0, 'C')
                pdf.cell(20, 8, pdf.ar_text("عدد التلاميذ"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("التلاميذ الغائبين"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("حصص الغياب"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("متوسط يومي"), 1, 1, 'C')

                # بيانات الأقسام
                pdf.set_arabic_font(8, '')
                for section in sections_data:
                    section_name, total_students, absent_students, absence_days, absence_sessions, avg_daily = section
                    pdf.cell(30, 6, pdf.ar_text(str(section_name or "")), 1, 0, 'C')
                    pdf.cell(20, 6, str(total_students or 0), 1, 0, 'C')
                    pdf.cell(25, 6, str(absent_students or 0), 1, 0, 'C')
                    pdf.cell(25, 6, str(absence_days or 0), 1, 0, 'C')
                    pdf.cell(25, 6, str(absence_sessions or 0), 1, 0, 'C')
                    pdf.cell(25, 6, str(avg_daily or 0), 1, 1, 'C')

            conn.close()

            # حفظ الملف
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"تقرير_الغياب_حسب_القسم_{month_name}_{year}_{timestamp}.pdf"
            output_path = os.path.join(reports_dir, filename)

            pdf.output(output_path)

            # فتح الملف
            if os.name == 'nt':  # Windows
                os.startfile(output_path)

            return True, output_path

        except Exception as e:
            print(f"❌ خطأ في إنشاء PDF: {e}")
            return False, None

    def generate_absence_statistics_report(self):
        """إنشاء تقرير إحصائيات الغياب الورقية"""
        try:
            # الحصول على التاريخ المحدد
            selected_date = self.date_edit.date().toPyDate()
            year = selected_date.year

            print(f"🔧 إنشاء تقرير إحصائيات الغياب للسنة: {year}")

            # إنشاء PDF للتقرير
            success, output_path = self.create_statistics_pdf(year)

            if success:
                QMessageBox.information(self, "نجح",
                    f"تم إنشاء تقرير إحصائيات الغياب بنجاح! ✅\n\n"
                    f"السنة: {year}\n"
                    f"المسار: {output_path}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء التقرير")

        except Exception as e:
            error_msg = f"خطأ في إنشاء تقرير إحصائيات الغياب: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)

    def create_statistics_pdf(self, year):
        """إنشاء PDF لتقرير إحصائيات الغياب"""
        try:
            import os
            from datetime import datetime
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            # فئة PDF مع دعم العربية (نفس الفئة المحسنة)
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P', 'mm', 'A4')

                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
                    calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

                    if os.path.exists(calibri_path):
                        self.add_font('Calibri', '', calibri_path)
                        self.calibri_available = True
                    else:
                        self.calibri_available = False

                    if os.path.exists(calibri_bold_path):
                        self.add_font('Calibri', 'B', calibri_bold_path)
                        self.calibri_bold_available = True
                    else:
                        self.calibri_bold_available = False

                def ar_text(self, txt):
                    reshaped = arabic_reshaper.reshape(str(txt))
                    return get_display(reshaped)

                def set_arabic_font(self, size=12, style='B'):
                    if style == 'B' and self.calibri_bold_available:
                        self.set_font('Calibri', 'B', size)
                    elif style == '' and self.calibri_available:
                        self.set_font('Calibri', '', size)
                    else:
                        self.set_font('Arial', style, size)

            # إنشاء PDF
            pdf = ArabicPDF()
            pdf.add_page()
            pdf.set_arabic_font(16, 'B')

            # العنوان
            pdf.cell(0, 10, pdf.ar_text(f"إحصائيات الغياب السنوية - {year}"), 0, 1, 'C')
            pdf.ln(5)

            # جلب الإحصائيات العامة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إحصائيات عامة
            pdf.set_arabic_font(14, 'B')
            pdf.cell(0, 10, pdf.ar_text("الإحصائيات العامة"), 0, 1, 'R')
            pdf.ln(3)

            # إجمالي التلاميذ
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL")
            total_students = cursor.fetchone()[0]

            # إجمالي أيام الغياب
            cursor.execute("""
                SELECT COUNT(*) FROM تدوين_الغياب
                WHERE strftime('%Y', تاريخ_الغياب) = ?
            """, (str(year),))
            total_absence_days = cursor.fetchone()[0]

            # إجمالي حصص الغياب
            cursor.execute("""
                SELECT SUM(عدد_الحصص_المتغيب_عنها) FROM تدوين_الغياب
                WHERE strftime('%Y', تاريخ_الغياب) = ?
            """, (str(year),))
            total_absence_sessions = cursor.fetchone()[0] or 0

            pdf.set_arabic_font(12, '')
            pdf.cell(0, 8, pdf.ar_text(f"إجمالي التلاميذ: {total_students}"), 0, 1, 'R')
            pdf.cell(0, 8, pdf.ar_text(f"إجمالي أيام الغياب: {total_absence_days}"), 0, 1, 'R')
            pdf.cell(0, 8, pdf.ar_text(f"إجمالي حصص الغياب: {total_absence_sessions}"), 0, 1, 'R')
            pdf.ln(5)

            # إحصائيات شهرية
            pdf.set_arabic_font(14, 'B')
            pdf.cell(0, 10, pdf.ar_text("الإحصائيات الشهرية"), 0, 1, 'R')
            pdf.ln(3)

            query = """
                SELECT
                    strftime('%m', تاريخ_الغياب) as الشهر,
                    COUNT(DISTINCT معرف_التلميذ) as عدد_التلاميذ_الغائبين,
                    COUNT(*) as اجمالي_ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
                FROM تدوين_الغياب
                WHERE strftime('%Y', تاريخ_الغياب) = ?
                GROUP BY strftime('%m', تاريخ_الغياب)
                ORDER BY الشهر
            """

            cursor.execute(query, (str(year),))
            monthly_data = cursor.fetchall()

            if monthly_data:
                # رأس الجدول الشهري
                pdf.set_arabic_font(10, 'B')
                pdf.cell(30, 8, pdf.ar_text("الشهر"), 1, 0, 'C')
                pdf.cell(35, 8, pdf.ar_text("التلاميذ الغائبين"), 1, 0, 'C')
                pdf.cell(30, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
                pdf.cell(30, 8, pdf.ar_text("حصص الغياب"), 1, 1, 'C')

                month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                              "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

                pdf.set_arabic_font(9, '')
                for month_data in monthly_data:
                    month_num, absent_students, absence_days, absence_sessions = month_data
                    month_name = month_names[int(month_num)]
                    pdf.cell(30, 6, pdf.ar_text(month_name), 1, 0, 'C')
                    pdf.cell(35, 6, str(absent_students), 1, 0, 'C')
                    pdf.cell(30, 6, str(absence_days), 1, 0, 'C')
                    pdf.cell(30, 6, str(absence_sessions), 1, 1, 'C')

            pdf.ln(5)

            # أكثر التلاميذ غياباً
            pdf.set_arabic_font(14, 'B')
            pdf.cell(0, 10, pdf.ar_text("أكثر التلاميذ غياباً"), 0, 1, 'R')
            pdf.ln(3)

            query = """
                SELECT
                    ت.اسم_التلميذ,
                    ت.رمز_التلميذ,
                    ت.القسم,
                    SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
                FROM جدول_البيانات ت
                JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
                WHERE strftime('%Y', غ.تاريخ_الغياب) = ?
                GROUP BY ت.id
                ORDER BY اجمالي_حصص_الغياب DESC
                LIMIT 10
            """

            cursor.execute(query, (str(year),))
            top_absent = cursor.fetchall()

            if top_absent:
                # رأس جدول أكثر التلاميذ غياباً
                pdf.set_arabic_font(9, 'B')
                pdf.cell(40, 8, pdf.ar_text("اسم التلميذ"), 1, 0, 'C')
                pdf.cell(20, 8, pdf.ar_text("الرمز"), 1, 0, 'C')
                pdf.cell(30, 8, pdf.ar_text("القسم"), 1, 0, 'C')
                pdf.cell(25, 8, pdf.ar_text("حصص الغياب"), 1, 1, 'C')

                pdf.set_arabic_font(8, '')
                for student in top_absent:
                    name, code, section, absence_sessions = student
                    pdf.cell(40, 6, pdf.ar_text(str(name or "")), 1, 0, 'C')
                    pdf.cell(20, 6, pdf.ar_text(str(code or "")), 1, 0, 'C')
                    pdf.cell(30, 6, pdf.ar_text(str(section or "")), 1, 0, 'C')
                    pdf.cell(25, 6, str(absence_sessions), 1, 1, 'C')

            conn.close()

            # حفظ الملف
            reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"احصائيات_الغياب_السنوية_{year}_{timestamp}.pdf"
            output_path = os.path.join(reports_dir, filename)

            pdf.output(output_path)

            # فتح الملف
            if os.name == 'nt':  # Windows
                os.startfile(output_path)

            return True, output_path

        except Exception as e:
            print(f"❌ خطأ في إنشاء PDF: {e}")
            return False, None

    def show_delete_absence_dialog(self):
        """عرض نافذة حذف الغياب"""
        try:
            dialog = DeleteAbsenceDialog(self.db_path, self)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث الجدول بعد الحذف
                self.load_students_data()
                QMessageBox.information(self, "نجح", "تم حذف الغياب بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في عرض نافذة حذف الغياب: {str(e)}")

class DeleteAbsenceDialog(QDialog):
    """نافذة حذف الغياب"""

    def __init__(self, db_path, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.setWindowTitle("حذف الغياب")
        self.setModal(True)
        self.resize(1000, 500)
        self.setup_ui()
        self.load_absence_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("حذف الغياب")
        title.setFont(QFont("Calibri", 15, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #e74c3c; margin: 10px;")
        layout.addWidget(title)

        # فلاتر البحث
        filters_group = QGroupBox("فلاتر البحث")
        filters_group.setFont(QFont("Calibri", 13, QFont.Bold))
        filters_layout = QHBoxLayout(filters_group)

        # فلتر التلميذ
        student_label = QLabel("التلميذ:")
        student_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.student_filter = QComboBox()
        self.student_filter.setFont(QFont("Calibri", 11))
        self.student_filter.addItem("جميع التلاميذ", "")
        self.student_filter.currentTextChanged.connect(self.on_filter_changed)

        # فلتر القسم
        section_label = QLabel("القسم:")
        section_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.section_filter = QComboBox()
        self.section_filter.setFont(QFont("Calibri", 11))
        self.section_filter.addItem("جميع الأقسام", "")
        self.section_filter.currentTextChanged.connect(self.on_filter_changed)

        # فلتر التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.date_filter = QDateEdit()
        self.date_filter.setFont(QFont("Calibri", 11))
        self.date_filter.setCalendarPopup(True)
        self.date_filter.setDate(QDate.currentDate())

        # إضافة خيار "جميع التواريخ"
        self.date_filter_enabled = QCheckBox("تصفية حسب التاريخ")
        self.date_filter_enabled.setFont(QFont("Calibri", 12, QFont.Bold))
        self.date_filter_enabled.setChecked(False)  # غير مفعل افتراضياً
        self.date_filter_enabled.stateChanged.connect(self.on_date_filter_toggle)

        # تعطيل التاريخ افتراضياً
        self.date_filter.setEnabled(False)

        # إضافة تأخير لفلترة الغياب
        self.filter_timer = QTimer()
        self.filter_timer.setSingleShot(True)
        self.filter_timer.timeout.connect(self.filter_absence_data)
        self.date_filter.dateChanged.connect(self.on_filter_changed)

        filters_layout.addWidget(student_label)
        filters_layout.addWidget(self.student_filter)
        filters_layout.addWidget(section_label)
        filters_layout.addWidget(self.section_filter)
        filters_layout.addWidget(date_label)
        filters_layout.addWidget(self.date_filter)
        filters_layout.addWidget(self.date_filter_enabled)

        layout.addWidget(filters_group)

        # جدول الغياب
        self.absence_table = QTableWidget()
        self.absence_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.absence_table.setAlternatingRowColors(True)

        # تعيين أعمدة الجدول حسب ترتيب SELECT * FROM تدوين_الغياب
        columns = [
            "ID",                    # 0 - id (مخفي)
            "معرف التلميذ",          # 1 - معرف_التلميذ (مخفي)
            "اسم التلميذ",           # 2 - اسم_التلميذ
            "رمز التلميذ",          # 3 - رمز_التلميذ
            "القسم",                # 4 - القسم
            "تاريخ الغياب",          # 5 - تاريخ_الغياب
            "الحصة الأولى",         # 6 - الحصة_الاولى
            "الحصة الثانية",        # 7 - الحصة_الثانية
            "الحصة الثالثة",        # 8 - الحصة_الثالثة
            "عدد الحصص",            # 9 - عدد_الحصص_المتغيب_عنها
            "ملاحظات"              # 10 - ملاحظات
        ]
        self.absence_table.setColumnCount(len(columns))
        self.absence_table.setHorizontalHeaderLabels(columns)

        # إخفاء الأعمدة غير المرغوب فيها
        self.absence_table.hideColumn(0)  # إخفاء عمود ID
        self.absence_table.hideColumn(1)  # إخفاء عمود معرف_التلميذ

        # تنسيق رؤوس الجدول
        header = self.absence_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
            }
        """)

        # تعيين عرض الأعمدة بشكل مخصص
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # اسم التلميذ
        self.absence_table.setColumnWidth(2, 150)  # عرض 150 نقطة لعمود اسم التلميذ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # رمز التلميذ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # القسم
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # تاريخ الغياب
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحصة الأولى
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحصة الثانية
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # الحصة الثالثة
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # عدد الحصص
        header.setSectionResizeMode(10, QHeaderView.Stretch)  # ملاحظات

        layout.addWidget(self.absence_table)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        # زر حذف السجل المحدد
        delete_selected_btn = QPushButton("🗑️ حذف السجل المحدد")
        delete_selected_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_selected_btn.clicked.connect(self.delete_selected_record)

        # زر حذف جميع سجلات التاريخ
        delete_date_btn = QPushButton("🗑️ حذف جميع سجلات التاريخ")
        delete_date_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_date_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        delete_date_btn.clicked.connect(self.delete_date_records)

        # زر حذف جميع سجلات الغياب
        delete_all_btn = QPushButton("🗑️ حذف جميع سجلات الغياب")
        delete_all_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #8b0000;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #660000;
            }
        """)
        delete_all_btn.clicked.connect(self.delete_all_absence_records)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(delete_selected_btn)
        buttons_layout.addWidget(delete_date_btn)
        buttons_layout.addWidget(delete_all_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def on_filter_changed(self):
        """معالج تغيير الفلتر مع تأخير لمنع التشنج"""
        self.filter_timer.stop()
        self.filter_timer.start(300)  # تأخير 300 مللي ثانية

    def on_date_filter_toggle(self):
        """تفعيل/تعطيل فلتر التاريخ"""
        is_enabled = self.date_filter_enabled.isChecked()
        self.date_filter.setEnabled(is_enabled)

        # إعادة تحميل البيانات
        self.on_filter_changed()

    def load_absence_data(self):
        """تحميل بيانات الغياب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل التلاميذ للفلتر
            cursor.execute("""
                SELECT DISTINCT اسم_التلميذ
                FROM تدوين_الغياب
                ORDER BY اسم_التلميذ
            """)
            students = cursor.fetchall()
            for student in students:
                self.student_filter.addItem(student[0])

            # تحميل الأقسام للفلتر
            cursor.execute("""
                SELECT DISTINCT القسم
                FROM تدوين_الغياب
                ORDER BY القسم
            """)
            sections = cursor.fetchall()
            for section in sections:
                self.section_filter.addItem(section[0])

            conn.close()

            # تحميل بيانات الغياب
            self.filter_absence_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def filter_absence_data(self):
        """فلترة بيانات الغياب"""
        try:
            # إظهار مؤشر التحميل
            QApplication.setOverrideCursor(Qt.WaitCursor)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = "SELECT * FROM تدوين_الغياب WHERE 1=1"
            params = []

            # فلتر التلميذ
            if self.student_filter.currentText() != "جميع التلاميذ":
                query += " AND اسم_التلميذ = ?"
                params.append(self.student_filter.currentText())

            # فلتر القسم
            if self.section_filter.currentText() != "جميع الأقسام":
                query += " AND القسم = ?"
                params.append(self.section_filter.currentText())

            # فلتر التاريخ (فقط إذا كان مفعل)
            if self.date_filter_enabled.isChecked():
                selected_date = self.date_filter.date().toString("yyyy-MM-dd")
                query += " AND تاريخ_الغياب = ?"
                params.append(selected_date)

            query += " ORDER BY تاريخ_الغياب DESC, اسم_التلميذ"

            cursor.execute(query, params)
            records = cursor.fetchall()

            # تحديث الجدول
            self.absence_table.setRowCount(len(records))

            for row, record in enumerate(records):
                # معالجة الأحداث كل 50 صف لمنع التشنج
                if row % 50 == 0:
                    QApplication.processEvents()

                for col, value in enumerate(record):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setFont(QFont("Calibri", 13, QFont.Bold))
                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    self.absence_table.setItem(row, col, item)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فلترة البيانات: {str(e)}")
        finally:
            # تنظيف جميع مؤشرات التحميل
            try:
                # إزالة جميع المؤشرات المتراكمة
                for _ in range(5):
                    QApplication.restoreOverrideCursor()
                print("🔄 تم تنظيف مؤشرات التحميل من فلترة الغياب")
            except:
                pass

    def delete_selected_record(self):
        """حذف السجل المحدد"""
        try:
            current_row = self.absence_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل للحذف")
                return

            # الحصول على ID السجل
            record_id = self.absence_table.item(current_row, 0).text()
            student_name = self.absence_table.item(current_row, 1).text()
            absence_date = self.absence_table.item(current_row, 4).text()

            # تأكيد الحذف
            reply = QMessageBox.question(self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف سجل الغياب؟\n\n"
                f"التلميذ: {student_name}\n"
                f"التاريخ: {absence_date}",
                QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM تدوين_الغياب WHERE id = ?", (record_id,))
                conn.commit()
                conn.close()

                # تحديث الجدول
                self.filter_absence_data()

                QMessageBox.information(self, "نجح", "تم حذف السجل بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف السجل: {str(e)}")

    def delete_date_records(self):
        """حذف جميع سجلات التاريخ المحدد"""
        try:
            selected_date = self.date_filter.date().toString("yyyy-MM-dd")

            # عد السجلات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM تدوين_الغياب WHERE تاريخ_الغياب = ?", (selected_date,))
            count = cursor.fetchone()[0]

            if count == 0:
                QMessageBox.information(self, "معلومة", "لا توجد سجلات غياب في التاريخ المحدد")
                conn.close()
                return

            # تأكيد الحذف
            reply = QMessageBox.question(self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف جميع سجلات الغياب؟\n\n"
                f"التاريخ: {selected_date}\n"
                f"عدد السجلات: {count}",
                QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                cursor.execute("DELETE FROM تدوين_الغياب WHERE تاريخ_الغياب = ?", (selected_date,))
                conn.commit()
                conn.close()

                # تحديث الجدول
                self.filter_absence_data()

                QMessageBox.information(self, "نجح", f"تم حذف {count} سجل بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف السجلات: {str(e)}")

    def delete_all_absence_records(self):
        """حذف جميع سجلات الغياب مع تأكيد برقم 12345"""
        try:
            # طلب رقم التأكيد من المستخدم
            confirmation_code, ok = QInputDialog.getText(
                self,
                "تأكيد حذف جميع سجلات الغياب",
                "⚠️ تحذير: هذه العملية ستحذف جميع سجلات الغياب نهائياً!\n\n"
                "لتأكيد العملية، يرجى إدخال رقم التأكيد:",
                QLineEdit.Password
            )

            if not ok:
                return

            # التحقق من رقم التأكيد
            if confirmation_code != "12345":
                QMessageBox.warning(
                    self,
                    "رقم تأكيد خاطئ",
                    "رقم التأكيد غير صحيح. لم يتم حذف أي سجلات."
                )
                return

            # تأكيد إضافي
            reply = QMessageBox.question(
                self,
                "تأكيد نهائي",
                "⚠️ هل أنت متأكد من حذف جميع سجلات الغياب؟\n\n"
                "هذه العملية لا يمكن التراجع عنها!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تنفيذ عملية الحذف
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حساب عدد السجلات قبل الحذف
            cursor.execute("SELECT COUNT(*) FROM تدوين_الغياب")
            total_records = cursor.fetchone()[0]

            if total_records == 0:
                QMessageBox.information(
                    self,
                    "لا توجد سجلات",
                    "لا توجد سجلات غياب للحذف."
                )
                conn.close()
                return

            # حذف جميع السجلات
            cursor.execute("DELETE FROM تدوين_الغياب")
            cursor.execute("DELETE FROM احصائيات_الغياب_الشهرية")
            cursor.execute("DELETE FROM احصائيات_الغياب_السنوية")

            conn.commit()
            conn.close()

            # تحديث الجدول
            self.filter_absence_data()

            # رسالة نجاح
            QMessageBox.information(
                self,
                "تم الحذف بنجاح",
                f"✅ تم حذف {total_records} سجل غياب بنجاح!\n\n"
                "تم أيضاً حذف جميع الإحصائيات المرتبطة."
            )

            print(f"✅ تم حذف {total_records} سجل غياب بنجاح")

        except Exception as e:
            error_msg = f"خطأ في حذف سجلات الغياب: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)


# تم حذف نافذة اختيار التلميذ - الآن نعتمد على التلميذ المحدد في الجدول الرئيسي


class StudentAbsenceReportDialog(QDialog):
    """نافذة تقرير الغياب للتلميذ"""

    def __init__(self, db_path, student_name, parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.student_name = student_name
        self.setWindowTitle(f"تقرير الغياب - {student_name}")
        self.setModal(True)
        self.resize(900, 700)

        # توسيط النافذة
        if parent:
            parent_geometry = parent.geometry()
            x = parent_geometry.x() + (parent_geometry.width() - 900) // 2
            y = parent_geometry.y() + (parent_geometry.height() - 700) // 2
            self.move(x, y)

        self.setup_ui()
        self.load_student_absence_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel(f"📊 تقرير الغياب - {self.student_name}")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول سجلات الغياب
        self.absence_table = QTableWidget()
        self.absence_table.setColumnCount(6)
        self.absence_table.setHorizontalHeaderLabels([
            "تاريخ الغياب", "الحصة الأولى", "الحصة الثانية", "الحصة الثالثة", "عدد الحصص", "ملاحظات"
        ])

        # تنسيق الجدول
        header = self.absence_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
            }
        """)

        self.absence_table.setAlternatingRowColors(True)
        self.absence_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.absence_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تعيين عرض الأعمدة
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الحصة الأولى
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحصة الثانية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الحصة الثالثة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # عدد الحصص
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # الملاحظات

        layout.addWidget(self.absence_table)

        # ملخص الإحصائيات
        self.stats_label = QLabel()
        self.stats_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.stats_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        self.stats_label.setWordWrap(True)
        layout.addWidget(self.stats_label)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def load_student_absence_data(self):
        """تحميل بيانات غياب التلميذ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب سجلات الغياب للتلميذ
            cursor.execute("""
                SELECT تاريخ_الغياب, الحصة_الاولى, الحصة_الثانية, الحصة_الثالثة,
                       عدد_الحصص_المتغيب_عنها, ملاحظات
                FROM تدوين_الغياب
                WHERE اسم_التلميذ = ?
                ORDER BY تاريخ_الغياب DESC
            """, (self.student_name,))

            records = cursor.fetchall()

            # عرض البيانات في الجدول
            self.absence_table.setRowCount(len(records))

            for row, record in enumerate(records):
                تاريخ_الغياب, الحصة_الاولى, الحصة_الثانية, الحصة_الثالثة, عدد_الحصص, ملاحظات = record

                # تاريخ الغياب
                date_item = QTableWidgetItem(تاريخ_الغياب or "")
                date_item.setFont(QFont("Calibri", 13, QFont.Bold))
                date_item.setTextAlignment(Qt.AlignCenter)
                self.absence_table.setItem(row, 0, date_item)

                # الحصص
                session1_item = QTableWidgetItem("✓" if الحصة_الاولى else "")
                session1_item.setFont(QFont("Calibri", 13, QFont.Bold))
                session1_item.setTextAlignment(Qt.AlignCenter)
                if الحصة_الاولى:
                    session1_item.setBackground(QColor(248, 215, 218))
                self.absence_table.setItem(row, 1, session1_item)

                session2_item = QTableWidgetItem("✓" if الحصة_الثانية else "")
                session2_item.setFont(QFont("Calibri", 13, QFont.Bold))
                session2_item.setTextAlignment(Qt.AlignCenter)
                if الحصة_الثانية:
                    session2_item.setBackground(QColor(248, 215, 218))
                self.absence_table.setItem(row, 2, session2_item)

                session3_item = QTableWidgetItem("✓" if الحصة_الثالثة else "")
                session3_item.setFont(QFont("Calibri", 13, QFont.Bold))
                session3_item.setTextAlignment(Qt.AlignCenter)
                if الحصة_الثالثة:
                    session3_item.setBackground(QColor(248, 215, 218))
                self.absence_table.setItem(row, 3, session3_item)

                # عدد الحصص
                count_item = QTableWidgetItem(str(عدد_الحصص or 0))
                count_item.setFont(QFont("Calibri", 13, QFont.Bold))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.absence_table.setItem(row, 4, count_item)

                # الملاحظات
                notes_item = QTableWidgetItem(ملاحظات or "")
                notes_item.setFont(QFont("Calibri", 13, QFont.Bold))
                notes_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.absence_table.setItem(row, 5, notes_item)

            # حساب الإحصائيات
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT تاريخ_الغياب) as ايام_الغياب,
                    SUM(عدد_الحصص_المتغيب_عنها) as اجمالي_الحصص,
                    AVG(عدد_الحصص_المتغيب_عنها) as متوسط_الحصص,
                    MIN(تاريخ_الغياب) as اول_غياب,
                    MAX(تاريخ_الغياب) as آخر_غياب
                FROM تدوين_الغياب
                WHERE اسم_التلميذ = ?
            """, (self.student_name,))

            stats = cursor.fetchone()
            conn.close()

            if stats and stats[0]:
                ايام_الغياب, اجمالي_الحصص, متوسط_الحصص, اول_غياب, آخر_غياب = stats

                # افتراض 60 حصة شهرياً
                total_sessions = 60
                attendance_rate = ((total_sessions - (اجمالي_الحصص or 0)) / total_sessions) * 100

                stats_text = f"""
📊 ملخص إحصائيات الغياب:

🗓️ عدد أيام الغياب: {ايام_الغياب or 0} يوم
📚 إجمالي الحصص المتغيب عنها: {اجمالي_الحصص or 0} حصة
📈 متوسط الحصص المتغيب عنها يومياً: {متوسط_الحصص:.1f if متوسط_الحصص else 0} حصة
📊 نسبة الحضور المقدرة: {attendance_rate:.1f}%

📅 أول غياب: {اول_غياب or 'لا يوجد'}
📅 آخر غياب: {آخر_غياب or 'لا يوجد'}
                """

                self.stats_label.setText(stats_text)
            else:
                self.stats_label.setText("📊 لا توجد سجلات غياب لهذا التلميذ")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الغياب: {str(e)}")


# ===== دوال إنشاء التقارير من print144.py =====

class ArabicPDF_Internal:
    """فئة PDF مع دعم العربية - نسخة داخلية"""
    def __init__(self):
        try:
            import os
            from fpdf import FPDF
            import arabic_reshaper
            from bidi.algorithm import get_display

            self.FPDF = FPDF
            self.arabic_reshaper = arabic_reshaper
            self.get_display = get_display

            self.pdf = FPDF('P', 'mm', 'A4')
            self.pdf.set_margins(5, 5, 5)
            self.pdf.set_auto_page_break(auto=True, margin=5)

            # إضافة الخطوط
            fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
            calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
            calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')

            if os.path.exists(calibri_path):
                self.pdf.add_font('Calibri', '', calibri_path)
                self.calibri_available = True
            else:
                self.calibri_available = False

            if os.path.exists(calibri_bold_path):
                self.pdf.add_font('Calibri', 'B', calibri_bold_path)
                self.calibri_bold_available = True
            else:
                self.calibri_bold_available = False

        except ImportError:
            QMessageBox.critical(None, "خطأ", "مكتبات PDF غير متوفرة. يرجى تثبيت fpdf2 و arabic-reshaper و python-bidi")
            raise

    def ar_text(self, txt):
        reshaped = self.arabic_reshaper.reshape(str(txt))
        return self.get_display(reshaped)

    def set_main_title_font(self):
        """خط العناوين الرئيسية - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.pdf.set_font('Calibri', 'B', 15)
        else:
            self.pdf.set_font('Arial', 'B', 15)

    def set_subtitle_font(self):
        """خط العناوين الفرعية - Calibri 14 Bold"""
        if self.calibri_bold_available:
            self.pdf.set_font('Calibri', 'B', 14)
        else:
            self.pdf.set_font('Arial', 'B', 14)

    def set_detail_font(self):
        """خط التفاصيل - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.pdf.set_font('Calibri', 'B', 13)
        else:
            self.pdf.set_font('Arial', 'B', 13)

    def set_table_header_font(self):
        """خط رؤوس الجدول - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.pdf.set_font('Calibri', 'B', 13)
        else:
            self.pdf.set_font('Arial', 'B', 13)

    def set_table_row_font(self):
        """خط صفوف الجدول - Calibri 12 Bold"""
        if self.calibri_bold_available:
            self.pdf.set_font('Calibri', 'B', 12)
        else:
            self.pdf.set_font('Arial', 'B', 12)

    def add_page(self):
        self.pdf.add_page()

    def set_xy(self, x, y):
        self.pdf.set_xy(x, y)

    def cell(self, w, h, txt, border=0, ln=0, align=''):
        self.pdf.cell(w, h, txt, border, ln, align)

    def ln(self, h=None):
        self.pdf.ln(h)

    def image(self, name, x=None, y=None, w=0, h=0):
        self.pdf.image(name, x, y, w, h)

    def output(self, name):
        self.pdf.output(name)


def create_student_report_direct(student_name, db_path):
    """إنشاء تقرير الغياب حسب التلميذ مباشرة"""
    try:
        import os
        from datetime import datetime

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # جلب بيانات التلميذ
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ, القسم
            FROM جدول_البيانات
            WHERE اسم_التلميذ = ?
        """, (student_name,))
        student_data = cursor.fetchone()

        if not student_data:
            return False, "التلميذ غير موجود"

        student_id, student_name, student_code, student_section = student_data

        # جلب معلومات القسم
        cursor.execute("""
            SELECT القسم, المادة, اسم_الاستاذ
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            LIMIT 1
        """, (student_section,))
        section_info = cursor.fetchone()
        if not section_info:
            section_info = (student_section, "غير محدد", "غير محدد")

        # جلب بيانات الغياب حسب اليوم (تفصيلي)
        cursor.execute("""
            SELECT
                تاريخ_الغياب,
                الحصة_الاولى,
                الحصة_الثانية,
                الحصة_الثالثة,
                عدد_الحصص_المتغيب_عنها,
                ملاحظات,
                strftime('%Y', تاريخ_الغياب) as السنة,
                strftime('%m', تاريخ_الغياب) as الشهر
            FROM تدوين_الغياب
            WHERE معرف_التلميذ = ?
            ORDER BY تاريخ_الغياب DESC
        """, (student_id,))
        absence_data = cursor.fetchall()

        conn.close()

        # إنشاء PDF
        pdf = ArabicPDF_Internal()
        pdf.add_page()

        # الشعار والعنوان
        add_header_internal(pdf, "تقرير الغياب حسب التلميذ")

        y = 40

        # معلومات التلميذ
        pdf.set_subtitle_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text(f"التلميذ: {student_name} - الرمز: {student_code}"), 0, 1, 'R')
        y += 10

        # جدول معلومات القسم
        y = add_section_table_internal(pdf, section_info, y)
        y += 10

        # جدول الغياب بالتفصيل
        add_student_absence_table_internal(pdf, absence_data, y)

        # حفظ الملف
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"تقرير_غياب_التلميذ_{student_name}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)

        pdf.output(output_path)

        # فتح الملف
        if os.name == 'nt':
            os.startfile(output_path)

        return True, output_path

    except Exception as e:
        print(f"خطأ في إنشاء تقرير التلميذ: {e}")
        return False, str(e)


def create_section_report_direct(section_name, db_path):
    """إنشاء تقرير الغياب حسب القسم مباشرة"""
    try:
        import os
        from datetime import datetime

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # جلب معلومات القسم
        cursor.execute("""
            SELECT القسم, المادة, اسم_الاستاذ
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            LIMIT 1
        """, (section_name,))
        section_info = cursor.fetchone()
        if not section_info:
            section_info = (section_name, "غير محدد", "غير محدد")

        # جلب تلاميذ القسم مع بيانات الغياب مجمعة حسب الشهر
        cursor.execute("""
            SELECT
                ت.اسم_التلميذ,
                ت.رمز_التلميذ,
                strftime('%Y', غ.تاريخ_الغياب) as السنة,
                strftime('%m', غ.تاريخ_الغياب) as الشهر,
                COUNT(*) as عدد_ايام_الغياب,
                SUM(غ.عدد_الحصص_المتغيب_عنها) as اجمالي_حصص_الغياب
            FROM جدول_البيانات ت
            LEFT JOIN تدوين_الغياب غ ON ت.id = غ.معرف_التلميذ
            WHERE ت.القسم = ? AND ت.اسم_التلميذ IS NOT NULL AND ت.اسم_التلميذ != ''
            GROUP BY ت.id, السنة, الشهر
            ORDER BY ت.اسم_التلميذ, السنة DESC, الشهر DESC
        """, (section_name,))
        students_data = cursor.fetchall()

        conn.close()

        # إنشاء PDF
        pdf = ArabicPDF_Internal()
        pdf.add_page()

        # الشعار والعنوان
        add_header_internal(pdf, f"تقرير الغياب حسب القسم - {section_name}")

        y = 40

        # جدول معلومات القسم
        y = add_section_table_internal(pdf, section_info, y)
        y += 10

        # جدول الغياب بالتفصيل حسب التلاميذ
        add_section_absence_table_internal(pdf, students_data, y)

        # حفظ الملف
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب التفصيلية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"تقرير_غياب_القسم_{section_name}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)

        pdf.output(output_path)

        # فتح الملف
        if os.name == 'nt':
            os.startfile(output_path)

        return True, output_path

    except Exception as e:
        print(f"خطأ في إنشاء تقرير القسم: {e}")
        return False, str(e)


def add_header_internal(pdf, title):
    """إضافة الشعار والعنوان"""
    import os

    # الشعار (إذا كان متوفراً)
    logo_path = None
    try:
        conn = sqlite3.connect("data.db")
        cursor = conn.cursor()
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        if logo_row and os.path.exists(logo_row[0]):
            logo_path = logo_row[0]
        conn.close()
    except:
        pass

    y = 5
    if logo_path:
        pdf.image(logo_path, x=85, y=y, w=40, h=20)
        y += 25
    else:
        # شعار نصي
        pdf.set_main_title_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 10, pdf.ar_text("🏫 مؤسسة التعليم"), 0, 1, 'C')
        y += 15

    # العنوان الرئيسي
    pdf.set_main_title_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 10, pdf.ar_text(title), 0, 1, 'C')


def add_section_table_internal(pdf, section_info, y):
    """إضافة جدول معلومات القسم"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("معلومات القسم"), 0, 1, 'R')
    y += 10

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(60, 8, pdf.ar_text("القسم"), 1, 0, 'C')
    pdf.cell(60, 8, pdf.ar_text("المادة"), 1, 0, 'C')
    pdf.cell(70, 8, pdf.ar_text("الأستاذ(ة)"), 1, 1, 'C')
    y += 8

    # بيانات القسم
    pdf.set_table_row_font()
    pdf.set_xy(5, y)
    pdf.cell(60, 8, pdf.ar_text(str(section_info[0])), 1, 0, 'C')
    pdf.cell(60, 8, pdf.ar_text(str(section_info[1])), 1, 0, 'C')
    pdf.cell(70, 8, pdf.ar_text(str(section_info[2])), 1, 1, 'C')

    return y + 8


def add_student_absence_table_internal(pdf, absence_data, y):
    """إضافة جدول الغياب للتلميذ تفصيلي حسب اليوم"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("تفاصيل الغياب حسب اليوم"), 0, 1, 'R')
    y += 10

    if not absence_data:
        pdf.set_detail_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text("لا توجد بيانات غياب لهذا التلميذ"), 0, 1, 'C')
        return

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(25, 8, pdf.ar_text("التاريخ"), 1, 0, 'C')
    pdf.cell(20, 8, pdf.ar_text("الحصة 1"), 1, 0, 'C')
    pdf.cell(20, 8, pdf.ar_text("الحصة 2"), 1, 0, 'C')
    pdf.cell(20, 8, pdf.ar_text("الحصة 3"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("عدد الحصص"), 1, 0, 'C')
    pdf.cell(80, 8, pdf.ar_text("ملاحظات"), 1, 1, 'C')
    y += 8

    # بيانات الغياب
    pdf.set_table_row_font()
    current_month = ""

    for row in absence_data:
        تاريخ_الغياب, الحصة_الاولى, الحصة_الثانية, الحصة_الثالثة, عدد_الحصص, ملاحظات, السنة, الشهر = row

        # إضافة فاصل شهري
        month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                       "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        month_year = f"{month_names[int(الشهر)]} {السنة}" if الشهر else ""

        if month_year != current_month and month_year:
            current_month = month_year
            # إضافة عنوان الشهر
            pdf.set_subtitle_font()
            pdf.set_xy(5, y)
            pdf.cell(0, 8, pdf.ar_text(f"--- {month_year} ---"), 0, 1, 'C')
            y += 8
            pdf.set_table_row_font()

        pdf.set_xy(5, y)
        pdf.cell(25, 8, pdf.ar_text(str(تاريخ_الغياب)), 1, 0, 'C')
        pdf.cell(20, 8, pdf.ar_text("✓" if الحصة_الاولى else ""), 1, 0, 'C')
        pdf.cell(20, 8, pdf.ar_text("✓" if الحصة_الثانية else ""), 1, 0, 'C')
        pdf.cell(20, 8, pdf.ar_text("✓" if الحصة_الثالثة else ""), 1, 0, 'C')
        pdf.cell(25, 8, pdf.ar_text(str(عدد_الحصص or 0)), 1, 0, 'C')

        # الملاحظات
        notes_text = str(ملاحظات or "")
        if len(notes_text) > 40:
            notes_text = notes_text[:37] + "..."
        pdf.cell(80, 8, pdf.ar_text(notes_text), 1, 1, 'R')
        y += 8


def add_section_absence_table_internal(pdf, students_data, y):
    """إضافة جدول الغياب للقسم"""
    pdf.set_subtitle_font()
    pdf.set_xy(5, y)
    pdf.cell(0, 8, pdf.ar_text("تفاصيل الغياب حسب التلاميذ"), 0, 1, 'R')
    y += 10

    if not students_data:
        pdf.set_detail_font()
        pdf.set_xy(5, y)
        pdf.cell(0, 8, pdf.ar_text("لا توجد بيانات غياب لهذا القسم"), 0, 1, 'C')
        return

    # رأس الجدول
    pdf.set_table_header_font()
    pdf.set_xy(5, y)
    pdf.cell(50, 8, pdf.ar_text("اسم التلميذ"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("الرمز"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("السنة"), 1, 0, 'C')
    pdf.cell(25, 8, pdf.ar_text("الشهر"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("أيام الغياب"), 1, 0, 'C')
    pdf.cell(30, 8, pdf.ar_text("حصص الغياب"), 1, 1, 'C')
    y += 8

    # بيانات التلاميذ
    pdf.set_table_row_font()
    month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

    for row in students_data:
        student_name, student_code, year, month, absence_days, absence_sessions = row

        # تخطي الصفوف التي لا تحتوي على بيانات غياب
        if not year or not month:
            continue

        month_name = month_names[int(month)] if month else ""

        pdf.set_xy(5, y)
        pdf.cell(50, 8, pdf.ar_text(str(student_name)), 1, 0, 'R')
        pdf.cell(30, 8, pdf.ar_text(str(student_code or "")), 1, 0, 'C')
        pdf.cell(25, 8, pdf.ar_text(str(year)), 1, 0, 'C')
        pdf.cell(25, 8, pdf.ar_text(month_name), 1, 0, 'C')
        pdf.cell(30, 8, pdf.ar_text(str(absence_days or 0)), 1, 0, 'C')
        pdf.cell(30, 8, pdf.ar_text(str(absence_sessions or 0)), 1, 1, 'C')
        y += 8


def main():
    import sys
    print("🚀 بدء تشغيل نافذة معالجة الغياب...")
    sys.stdout.flush()

    app = QApplication(sys.argv)
    print("✅ تم إنشاء التطبيق")
    sys.stdout.flush()

    app.setStyle('Fusion')
    print("✅ تم تعيين النمط")
    sys.stdout.flush()

    print("🔧 إنشاء النافذة الرئيسية...")
    sys.stdout.flush()

    window = AttendanceProcessingWindow()
    print("✅ تم إنشاء النافذة")
    sys.stdout.flush()

    print("🖥️ عرض النافذة...")
    sys.stdout.flush()

    window.show()
    print("✅ تم عرض النافذة - النافذة جاهزة للاستخدام")
    print("🎯 اختبر تغيير التاريخ الآن - يجب ألا ترى مؤشر دوار!")
    sys.stdout.flush()

    print("🔄 بدء حلقة الأحداث...")
    sys.stdout.flush()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()