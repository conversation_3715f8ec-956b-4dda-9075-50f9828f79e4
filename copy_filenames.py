import os
import sys
from pathlib import Path

def copy_filenames_to_clipboard():
    """نسخ أسماء جميع الملفات في المجلد الحالي إلى الحافظة"""
    try:
        # الحصول على مجلد الملف الحالي
        current_dir = Path(__file__).parent
        print(f"📁 المجلد الحالي: {current_dir}")
        
        # جمع جميع الملفات
        all_files = []
        
        # ملفات Python
        python_files = list(current_dir.glob("*.py"))
        python_files.sort()
        
        # ملفات أخرى
        other_files = []
        for pattern in ["*.ico", "*.db", "*.txt", "*.json", "*.ini", "*.cfg", "*.spec"]:
            other_files.extend(list(current_dir.glob(pattern)))
        other_files.sort()
        
        # المجلدات
        folders = [f for f in current_dir.iterdir() if f.is_dir() and not f.name.startswith('.')]
        folders.sort()
        
        print("\n📄 ملفات Python:")
        for file in python_files:
            filename = file.name
            all_files.append(f"    ('{filename}', '.'),")
            print(f"  - {filename}")
        
        print("\n📁 المجلدات:")
        for folder in folders:
            foldername = folder.name
            all_files.append(f"    ('{foldername}/', '{foldername}/'),")
            print(f"  - {foldername}/")
        
        print("\n📋 ملفات أخرى:")
        for file in other_files:
            filename = file.name
            if filename != "copy_filenames.py":  # تجنب إدراج هذا الملف نفسه
                all_files.append(f"    ('{filename}', '.'),")
                print(f"  - {filename}")
        
        # تجميع النتيجة النهائية
        result = "\n".join(all_files)
        
        # محاولة نسخ إلى الحافظة
        try:
            # للنوافذ
            import pyperclip
            pyperclip.copy(result)
            print(f"\n✅ تم نسخ {len(all_files)} عنصر إلى الحافظة بنجاح!")
        except ImportError:
            # إذا لم تكن pyperclip متوفرة، احفظ في ملف نصي
            output_file = current_dir / "filenames_list.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# قائمة الملفات للتحزيم\n")
                f.write("datas=[\n")
                f.write(result)
                f.write("\n]\n")
            print(f"📄 تم حفظ قائمة الملفات في: {output_file}")
        
        # طباعة النتيجة في الشاشة
        print(f"\n📝 قائمة الملفات للنسخ:")
        print("="*50)
        print(result)
        print("="*50)
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def generate_hiddenimports():
    """إنشاء قائمة hiddenimports من أسماء الملفات"""
    try:
        current_dir = Path(__file__).parent
        python_files = list(current_dir.glob("*.py"))
        
        hidden_imports = []
        
        for file in python_files:
            module_name = file.stem  # اسم الملف بدون امتداد .py
            if module_name != "copy_filenames":  # تجنب هذا الملف
                hidden_imports.append(f"        '{module_name}',")
        
        result = "\n".join(hidden_imports)
        print(f"\n🔍 قائمة Hidden Imports:")
        print("="*40)
        print(result)
        print("="*40)
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء hiddenimports: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة نسخ أسماء الملفات للتحزيم")
    print("="*60)
    
    # نسخ أسماء الملفات
    filenames = copy_filenames_to_clipboard()
    
    # إنشاء hiddenimports
    hidden_imports = generate_hiddenimports()
    
    # إنشاء ملف spec كامل
    if filenames and hidden_imports:
        try:
            spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # الأيقونة الرئيسية
    ('01.ico', '.'),
    
{filenames}
]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
{hidden_imports}
        
        # مكتبات PyQt5
        'PyQt5.QtWidgets',
        'PyQt5.QtGui', 
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        
        # مكتبات أساسية
        'sqlite3',
        'pandas',
        'openpyxl',
        'fpdf',
        'arabic_reshaper',
        'bidi.algorithm',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='برنامج_إدارة_الامتحانات_كامل',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    name='برنامج_إدارة_الامتحانات_كامل'
)"""
            
            # حفظ ملف spec محدث
            spec_file = Path(__file__).parent / "auto_generated.spec"
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)
            
            print(f"\n📄 تم إنشاء ملف spec محدث: {spec_file}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف spec: {e}")
    
    print(f"\n✨ انتهت العملية بنجاح!")

if __name__ == "__main__":
    # تثبيت pyperclip إذا لم تكن موجودة
    try:
        import pyperclip
    except ImportError:
        print("📦 جاري تثبيت pyperclip...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyperclip"])
        import pyperclip
    
    main()
    
    # انتظار المستخدم قبل الإغلاق
    input("\n⏳ اضغط Enter للإغلاق...")
