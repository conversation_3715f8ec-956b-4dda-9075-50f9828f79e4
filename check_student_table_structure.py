#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_student_table_structure():
    """فحص بنية جدول البيانات الفعلي"""
    print("🔍 فحص بنية جدول البيانات")
    print("=" * 60)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. عرض جميع الجداول
        print("\n📋 جميع الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        for i, table in enumerate(tables, 1):
            print(f"   {i}. {table[0]}")
        
        # 2. فحص جدول البيانات تفصيلياً
        if ('جدول_البيانات',) in tables:
            print(f"\n📊 تفاصيل جدول البيانات:")
            
            # بنية الجدول
            cursor.execute("PRAGMA table_info(جدول_البيانات)")
            columns = cursor.fetchall()
            
            print("   📋 الأعمدة:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = col
                print(f"      {col_id+1}. {col_name} ({col_type})")
                if primary_key:
                    print(f"         🔑 مفتاح أساسي")
                if not_null:
                    print(f"         ⚠️ مطلوب (NOT NULL)")
                if default_val:
                    print(f"         📝 القيمة الافتراضية: {default_val}")
            
            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            total_count = cursor.fetchone()[0]
            print(f"\n   📊 إجمالي السجلات: {total_count}")
            
            if total_count > 0:
                # عرض عينة من البيانات
                print(f"\n   📝 عينة من البيانات (أول 10 سجلات):")
                cursor.execute("SELECT * FROM جدول_البيانات LIMIT 10")
                sample_data = cursor.fetchall()
                
                # عرض أسماء الأعمدة
                column_names = [col[1] for col in columns]
                print(f"      الأعمدة: {' | '.join(column_names)}")
                print("      " + "-" * 80)
                
                for i, row in enumerate(sample_data, 1):
                    row_str = " | ".join([str(cell) if cell is not None else "NULL" for cell in row])
                    print(f"      {i:2d}. {row_str}")
                
                # فحص البيانات الفارغة
                print(f"\n   🔍 فحص البيانات الفارغة:")
                for col_name in column_names:
                    if col_name != 'id':  # تجاهل عمود المعرف
                        cursor.execute(f"SELECT COUNT(*) FROM جدول_البيانات WHERE `{col_name}` IS NULL OR `{col_name}` = ''")
                        null_count = cursor.fetchone()[0]
                        if null_count > 0:
                            print(f"      ⚠️ {col_name}: {null_count} سجل فارغ")
                        else:
                            print(f"      ✅ {col_name}: جميع السجلات مملوءة")
                
                # فحص القيم المميزة للأقسام والمجموعات
                print(f"\n   📊 القيم المميزة:")
                
                # البحث عن أعمدة الأقسام
                section_columns = [col for col in column_names if any(keyword in col.lower() for keyword in ['قسم', 'section'])]
                for col in section_columns:
                    cursor.execute(f"SELECT DISTINCT `{col}` FROM جدول_البيانات WHERE `{col}` IS NOT NULL AND `{col}` != ''")
                    distinct_values = cursor.fetchall()
                    print(f"      🏫 {col}: {[val[0] for val in distinct_values]}")
                
                # البحث عن أعمدة المجموعات
                group_columns = [col for col in column_names if any(keyword in col.lower() for keyword in ['مجموعة', 'group'])]
                for col in group_columns:
                    cursor.execute(f"SELECT DISTINCT `{col}` FROM جدول_البيانات WHERE `{col}` IS NOT NULL AND `{col}` != ''")
                    distinct_values = cursor.fetchall()
                    print(f"      👥 {col}: {[val[0] for val in distinct_values]}")
                
                # البحث عن أعمدة الأسماء
                name_columns = [col for col in column_names if any(keyword in col.lower() for keyword in ['اسم', 'name', 'طالب', 'student'])]
                for col in name_columns:
                    cursor.execute(f"SELECT COUNT(DISTINCT `{col}`) FROM جدول_البيانات WHERE `{col}` IS NOT NULL AND `{col}` != ''")
                    distinct_count = cursor.fetchone()[0]
                    print(f"      👤 {col}: {distinct_count} اسم مميز")
            
            else:
                print("   ⚠️ الجدول فارغ - لا توجد بيانات")
        
        else:
            print(f"\n❌ جدول البيانات غير موجود")
            print("   💡 الجداول المتاحة التي قد تحتوي على بيانات الطلاب:")
            
            for table in tables:
                table_name = table[0]
                if any(keyword in table_name.lower() for keyword in ['طالب', 'student', 'بيانات', 'data']):
                    print(f"      🔍 {table_name}")
                    
                    # فحص محتوى الجدول
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        count = cursor.fetchone()[0]
                        print(f"         📊 عدد السجلات: {count}")
                        
                        if count > 0:
                            cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                            table_columns = cursor.fetchall()
                            column_names = [col[1] for col in table_columns]
                            print(f"         📋 الأعمدة: {column_names}")
                            
                            # عرض عينة
                            cursor.execute(f"SELECT * FROM `{table_name}` LIMIT 3")
                            sample = cursor.fetchall()
                            print(f"         📝 عينة:")
                            for j, row in enumerate(sample, 1):
                                print(f"            {j}. {row}")
                    
                    except Exception as e:
                        print(f"         ❌ خطأ في قراءة الجدول: {e}")
        
        # 3. فحص جدول المواد والأقسام
        print(f"\n🏫 فحص جدول المواد والأقسام:")
        if ('جدول_المواد_والاقسام',) in tables:
            cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
            sections_count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {sections_count}")
            
            if sections_count > 0:
                cursor.execute("SELECT * FROM جدول_المواد_والاقسام LIMIT 5")
                sections_data = cursor.fetchall()
                print(f"   📝 عينة من البيانات:")
                for i, row in enumerate(sections_data, 1):
                    print(f"      {i}. {row}")
        else:
            print(f"   ❌ جدول المواد والأقسام غير موجود")
        
        conn.close()
        
        # 4. تشخيص المشكلة
        print(f"\n🔧 تشخيص المشكلة:")
        print("=" * 40)
        
        if total_count == 0:
            print("❌ المشكلة: جدول البيانات فارغ")
            print("💡 الحل: تشغيل python fix_absence_tables.py")
        
        elif not any('قسم' in col.lower() or 'section' in col.lower() for col in column_names):
            print("❌ المشكلة: لا يوجد عمود للقسم في جدول البيانات")
            print("💡 الحل: إضافة عمود القسم أو تعديل الاستعلام")
        
        else:
            print("✅ البنية تبدو صحيحة")
            print("💡 قد تكون المشكلة في الاستعلام أو التصفية")
        
        print(f"\n🚀 الخطوات التالية:")
        print("1. تشغيل python fix_absence_tables.py")
        print("2. تشغيل python absence_management_window.py")
        print("3. الضغط على زر 'تحديث البيانات'")
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_student_table_structure()
