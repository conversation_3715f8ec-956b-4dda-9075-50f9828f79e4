#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from PyQt5.QtWidgets import (
    QDialog, QWidget, QVBoxLayout, QHBoxLayout,
    QGroupBox, QLabel, QLineEdit, QPushButton, QFormLayout,
    QGraphicsDropShadowEffect, QMessageBox
)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt

class DefaultSettingsDialog(QDialog):  # تغيير من QMessageBox إلى QDialog
    def __init__(self, parent=None):
        super(DefaultSettingsDialog, self).__init__(parent)

        # تعيين حجم ثابت للنافذة
        self.resize(600, 600)
        self.setFixedSize(600, 600)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)  # إزالة زر المساعدة

        self.setWindowTitle("الإعدادات الافتراضية")

        # إضافة أيقونة البرنامج
        try:
            from PyQt5.QtGui import QIcon
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # إعداد الخطوط
        self.font_calibri_13_bold = QFont('Calibri', 13)
        self.font_calibri_13_bold.setBold(True)
        self.font_calibri_13_normal = QFont('Calibri', 13)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # --- مجموعة عتبات الغياب ---
        absence_group = QGroupBox("عتبات الغياب")
        absence_group.setFont(self.font_calibri_13_bold)
        absence_group.setStyleSheet("""
            QGroupBox {
                background-color: #f0f8ff;
                border-radius: 10px;
                margin-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                color: #0066cc;
            }
        """)
        absence_layout = QFormLayout(absence_group)
        absence_layout.setContentsMargins(20, 30, 20, 15)
        absence_layout.setSpacing(12)

        # عتبة الغياب الأسبوعي
        self.weekly_threshold = QLineEdit()
        self.weekly_threshold.setFont(self.font_calibri_13_normal)
        self.weekly_threshold.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.weekly_threshold.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #0066cc;
            }
        """)
        weekly_label = QLabel("عتبة الغياب الأسبوعي:")
        weekly_label.setFont(self.font_calibri_13_normal)
        absence_layout.addRow(weekly_label, self.weekly_threshold)

        # عتبة الغياب الشهري
        self.monthly_threshold = QLineEdit()
        self.monthly_threshold.setFont(self.font_calibri_13_normal)
        self.monthly_threshold.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.monthly_threshold.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #0066cc;
            }
        """)
        monthly_label = QLabel("عتبة الغياب الشهري:")
        monthly_label.setFont(self.font_calibri_13_normal)
        absence_layout.addRow(monthly_label, self.monthly_threshold)

        main_layout.addWidget(absence_group)

        # --- مجموعة إعدادات التنقيط ---
        points_group = QGroupBox("إعدادات التنقيط")
        points_group.setFont(self.font_calibri_13_bold)
        points_group.setStyleSheet("""
            QGroupBox {
                background-color: #f0fff0;
                border-radius: 10px;
                margin-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                color: #008800;
            }
        """)
        points_layout = QFormLayout(points_group)
        points_layout.setContentsMargins(20, 30, 20, 15)
        points_layout.setSpacing(12)

        # نقطة المواظبة
        self.attendance_points = QLineEdit()
        self.attendance_points.setFont(self.font_calibri_13_normal)
        self.attendance_points.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.attendance_points.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #008800;
            }
        """)
        attendance_label = QLabel("نقطة المواظبة:")
        attendance_label.setFont(self.font_calibri_13_normal)
        points_layout.addRow(attendance_label, self.attendance_points)

        # نقطة السلوك
        self.behavior_points = QLineEdit()
        self.behavior_points.setFont(self.font_calibri_13_normal)
        self.behavior_points.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.behavior_points.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #008800;
            }
        """)
        behavior_label = QLabel("نقطة السلوك:")
        behavior_label.setFont(self.font_calibri_13_normal)
        points_layout.addRow(behavior_label, self.behavior_points)

        # معامل المواظبة
        self.attendance_factor = QLineEdit()
        self.attendance_factor.setFont(self.font_calibri_13_normal)
        self.attendance_factor.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.attendance_factor.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #008800;
            }
        """)
        att_factor_label = QLabel("معامل المواظبة:")
        att_factor_label.setFont(self.font_calibri_13_normal)
        points_layout.addRow(att_factor_label, self.attendance_factor)

        main_layout.addWidget(points_group)

        # --- مجموعة المخالفات ---
        violations_group = QGroupBox("المخالفات")
        violations_group.setFont(self.font_calibri_13_bold)
        violations_group.setStyleSheet("""
            QGroupBox {
                background-color: #fff0f0;
                border-radius: 10px;
                margin-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                color: #cc0000;
            }
        """)
        violations_layout = QFormLayout(violations_group)
        violations_layout.setContentsMargins(20, 30, 20, 15)
        violations_layout.setSpacing(12)

        # المخالفة الأولى
        self.violation1 = QLineEdit()
        self.violation1.setFont(self.font_calibri_13_normal)
        self.violation1.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.violation1.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #cc0000;
            }
        """)
        violation1_label = QLabel("المخالفة الأولى:")
        violation1_label.setFont(self.font_calibri_13_normal)
        violations_layout.addRow(violation1_label, self.violation1)

        # المخالفة الثانية
        self.violation2 = QLineEdit()
        self.violation2.setFont(self.font_calibri_13_normal)
        self.violation2.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.violation2.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #cc0000;
            }
        """)
        violation2_label = QLabel("المخالفة الثانية:")
        violation2_label.setFont(self.font_calibri_13_normal)
        violations_layout.addRow(violation2_label, self.violation2)

        # المخالفة الثالثة
        self.violation3 = QLineEdit()
        self.violation3.setFont(self.font_calibri_13_normal)
        self.violation3.setFixedHeight(30)  # تعيين ارتفاع مربع النص إلى 30 نقطة
        self.violation3.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #cc0000;
            }
        """)
        violation3_label = QLabel("المخالفة الثالثة:")
        violation3_label.setFont(self.font_calibri_13_normal)
        violations_layout.addRow(violation3_label, self.violation3)

        main_layout.addWidget(violations_group)

        # --- أزرار الحفظ والإلغاء ---
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        buttons_layout.addStretch(1)

        save_button = QPushButton("حفظ الإعدادات")
        save_button.setFont(self.font_calibri_13_bold)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        save_button.clicked.connect(self.saveSettings)
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(self.font_calibri_13_normal)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e0e0e0;
                color: #333;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #d0d0d0;
            }
            QPushButton:pressed {
                background-color: #c0c0c0;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        main_layout.addLayout(buttons_layout)

        # إضافة تأثير الظل على النافذة
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(2, 2)
        self.setGraphicsEffect(shadow)

        # تطبيق نمط عام للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLabel {
                color: #333;
            }
        """)

        # تحميل البيانات الحالية
        self.loadCurrentSettings()

    def loadCurrentSettings(self):
        """تحميل الإعدادات الحالية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # التأكد من وجود جدول الإعدادات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS اعدادات_البرنامج (
                    المعرف INTEGER PRIMARY KEY,
                    الشهر_الحالي TEXT UNIQUE,
                    عتبة_الغياب_الأسبوعي INTEGER,
                    عتبة_الغياب_الشهري INTEGER,
                    نقطة_المواظبة INTEGER,
                    نقطة_السلوك INTEGER,
                    معامل_المواظبة INTEGER,
                    المخالفة_الاولى INTEGER,
                    المخالفة_الثانية INTEGER,
                    المخالفة_الثالثة INTEGER,
                    تاريخ_التعديل DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # التحقق من وجود سجل في الجدول
            cursor.execute("SELECT COUNT(*) FROM اعدادات_البرنامج")
            count = cursor.fetchone()[0]

            if count == 0:
                # إنشاء سجل افتراضي إذا لم يكن هناك سجل
                cursor.execute("""
                    INSERT INTO اعدادات_البرنامج (
                        الشهر_الحالي, عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري,
                        نقطة_المواظبة, نقطة_السلوك, معامل_المواظبة,
                        المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
                    ) VALUES ('شتنبر', 3, 8, 20, 20, 2, 1, 2, 3)
                """)
                conn.commit()

            # استرجاع الإعدادات
            cursor.execute("""
                SELECT
                    عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري,
                    نقطة_المواظبة, نقطة_السلوك, معامل_المواظبة,
                    المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
                FROM اعدادات_البرنامج LIMIT 1
            """)

            settings = cursor.fetchone()

            if settings:
                # تعبئة الحقول بالقيم المسترجعة
                self.weekly_threshold.setText(str(settings[0]))
                self.monthly_threshold.setText(str(settings[1]))
                self.attendance_points.setText(str(settings[2]))
                self.behavior_points.setText(str(settings[3]))
                self.attendance_factor.setText(str(settings[4]))
                self.violation1.setText(str(settings[5]))
                self.violation2.setText(str(settings[6]))
                self.violation3.setText(str(settings[7]))

            conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل الإعدادات:\n{str(e)}")

    def show_custom_success_message(self, message, title="نجاح"):
        """عرض رسالة نجاح مخصصة"""
        success_dialog = QDialog(self)
        success_dialog.setWindowTitle(title)
        success_dialog.setFixedSize(450, 250)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة رأس النافذة مع أيقونة
        header_layout = QHBoxLayout()

        # إضافة أيقونة النجاح
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setStyleSheet("""
            background-color: #27ae60;
            border-radius: 24px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setText("✓")
        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #27ae60;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة النجاح
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219653;
            }
        """)
        ok_button.clicked.connect(success_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        success_dialog.exec_()
        return True

    def saveSettings(self):
        """حفظ الإعدادات في قاعدة البيانات"""
        try:
            # قراءة القيم من الحقول
            weekly_threshold = self.weekly_threshold.text().strip() or "0"
            monthly_threshold = self.monthly_threshold.text().strip() or "0"
            attendance_points = self.attendance_points.text().strip() or "0"
            behavior_points = self.behavior_points.text().strip() or "0"
            attendance_factor = self.attendance_factor.text().strip() or "0"
            violation1 = self.violation1.text().strip()
            violation2 = self.violation2.text().strip()
            violation3 = self.violation3.text().strip()

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            # تحديث الإعدادات
            cursor.execute("""
                UPDATE اعدادات_البرنامج
                SET
                    عتبة_الغياب_الأسبوعي = ?,
                    عتبة_الغياب_الشهري = ?,
                    نقطة_المواظبة = ?,
                    نقطة_السلوك = ?,
                    معامل_المواظبة = ?,
                    المخالفة_الاولى = ?,
                    المخالفة_الثانية = ?,
                    المخالفة_الثالثة = ?
            """, (
                weekly_threshold, monthly_threshold,
                attendance_points, behavior_points, attendance_factor,
                violation1, violation2, violation3
            ))

            conn.commit()
            conn.close()

            # عرض رسالة نجاح مخصصة بدلاً من الرسالة العادية
            self.show_custom_success_message("تم حفظ الإعدادات بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")


# هذا الجزء سيتم تنفيذه فقط عند تشغيل الملف مباشرة وليس عند استيراده
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    window = DefaultSettingsDialog()
    window.show()
    sys.exit(app.exec_())