#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# إعدادات جدول إحصاءات المستويات (أفقي) - مساوي لعرض جدول العنوان (280)
LEVEL_COL_WIDTHS = [30, 30, 30, 30, 40, 30, 90]  # إجمالي العرض = 280
LEVEL_HEADERS = ['إلى رقم', 'من رقم', 'الإناث', 'الذكور', 'إجمالي المترشحين', 'عدد القاعات', 'المستوى']

# إعدادات جدول تفاصيل القاعات (عمودي)
ROOM_COL_WIDTHS = [70, 30, 30, 30, 30]
ROOM_HEADERS = ['المستوى', 'نهاية رقم الامتحان', 'بداية رقم الامتحان', 'عدد المترشحين', 'رقم القاعة']

# إعدادات الجدول العلوي للصفحة الأفقية (مركز الامتحان والعنوان)
HEADER_COL_WIDTHS_LANDSCAPE = [140, 140]  # عرض أكبر للصفحة الأفقية

# إعدادات الجدول العلوي للصفحة العمودية (مركز الامتحان والعنوان)
HEADER_COL_WIDTHS_PORTRAIT = [90, 100]   # عرض مناسب للصفحة العمودية

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_HEADER = 15   # ارتفاع صف الجدول العلوي
ROW_HEIGHT_DATA_LEVELS = 15     # ارتفاع صفوف البيانات لجدول المستويات (6+4)
ROW_HEIGHT_DATA = 6     # ارتفاع صفوف البيانات العادية
ROW_HEIGHT_TABLE_HEADER_LEVELS = 16  # ارتفاع صف عناوين جدول المستويات (12+4)
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف عناوين الجداول العادية

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

# إعدادات الشعار
PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W_PT, BOX2_W_PT, BOX3_W_PT = 320, 80, 150
TITLE_H_PT = 40
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX3_W = BOX3_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # جلب بيانات الامتحانات مع إضافة عمود الجنس
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, الجنس
    FROM امتحانات
    ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
    '''
    cur.execute(query)
    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, subject_data=None):
    pdf = ArabicPDF()
    margin = 10

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان2 في جدول_الامتحان
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT العنوان2 FROM جدول_الامتحان LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # إنشاء إحصاءات شاملة حسب المستوى
    level_stats = {}
    room_stats = {}
    total_candidates = 0
    
    for rec in records:
        level = rec.get('المستوى', '')
        room_num = rec.get('القاعة', '')
        exam_num = int(rec.get('رقم_الامتحان', '0'))
        gender = rec.get('الجنس', '')
        
        # إحصاءات المستوى
        if level not in level_stats:
            level_stats[level] = {
                'total_candidates': 0,
                'rooms': set(),
                'min_exam': exam_num,
                'max_exam': exam_num,
                'male_count': 0,
                'female_count': 0
            }
        
        level_stats[level]['total_candidates'] += 1
        level_stats[level]['rooms'].add(room_num)
        level_stats[level]['min_exam'] = min(level_stats[level]['min_exam'], exam_num)
        level_stats[level]['max_exam'] = max(level_stats[level]['max_exam'], exam_num)
        
        # تحديد الجنس بناءً على عمود الجنس في قاعدة البيانات
        if gender == 'أنثى':
            level_stats[level]['female_count'] += 1
        elif gender == 'ذكر':
            level_stats[level]['male_count'] += 1
        else:
            # في حالة عدم تحديد الجنس أو قيمة غير معروفة، افتراضي ذكر
            level_stats[level]['male_count'] += 1
        
        # إحصاءات القاعات (الكود الموجود)
        if room_num not in room_stats:
            room_stats[room_num] = {
                'count': 0,
                'min_exam': exam_num,
                'max_exam': exam_num,
                'level': rec.get('المستوى', ''),
                'center': rec.get('مركز_الامتحان', '')
            }
        
        room_stats[room_num]['count'] += 1
        room_stats[room_num]['min_exam'] = min(room_stats[room_num]['min_exam'], exam_num)
        room_stats[room_num]['max_exam'] = max(room_stats[room_num]['max_exam'], exam_num)
        total_candidates += 1

    # ==== الصفحة الأولى: جدول إحصاءات المستويات (أفقي) ====[]
    pdf.add_page('L')  # صفحة أفقية
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 5

    # الجدول العلوي: مركز الامتحان وعنوان التقرير (للصفحة الأفقية)
    first_record = records[0] if records else {}
    title_text = report_title if report_title else "عنوان التقرير"
    center_text = f"مركز الامتحان: {first_record.get('مركز_الامتحان','')}"

    header_data = [center_text, title_text]
    
    pdf.set_font('Arial','B',17)  # تغيير الخط إلى Arial حجم 18
    pdf.set_text_color(0, 0, 128)  # أزرق غامق
    pdf.set_fill_color(230,230,230)
    x = margin
    for i, cell in enumerate(header_data):
        pdf.set_xy(x, y)
        pdf.cell(HEADER_COL_WIDTHS_LANDSCAPE[i], ROW_HEIGHT_HEADER, pdf.ar_text(cell), border=1, align='C', fill=True)
        x += HEADER_COL_WIDTHS_LANDSCAPE[i]
    y += ROW_HEIGHT_HEADER + 12

    # إعادة لون النص للعناصر الأخرى
    pdf.set_text_color(0, 0, 0)

    # جدول إحصاءات المستويات
    pdf.set_font('Arial','B',16)
    pdf.set_text_color(0, 0, 128)  # أزرق غامق
    pdf.set_fill_color(150,150,150)
    pdf.set_xy(margin, y)
    pdf.cell(sum(LEVEL_COL_WIDTHS), 10, pdf.ar_text("إحصاءات المستويات"), border=1, align='C', fill=True)
    y += 10

    # عناوين جدول المستويات
    pdf.set_font('Arial','B',15)  # حجم الخط 16 بدلاً من 12 (زيادة 4 نقاط)
    pdf.set_fill_color(200,200,200)
    x = margin
    for i, header in enumerate(LEVEL_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(LEVEL_COL_WIDTHS[i], ROW_HEIGHT_TABLE_HEADER_LEVELS, pdf.ar_text(header), border=1, align='C', fill=True)
        x += LEVEL_COL_WIDTHS[i]

    y += ROW_HEIGHT_TABLE_HEADER_LEVELS
    pdf.set_font('Arial','',15)  # حجم الخط 16 بدلاً من 12 (زيادة 4 نقاط)

    # بيانات المستويات
    sorted_levels = sorted(level_stats.keys())
    for level in sorted_levels:
        stats = level_stats[level]
        x = margin
        
        level_data = [
            str(stats['max_exam']),
            str(stats['min_exam']),
            str(stats['female_count']),
            str(stats['male_count']),
            str(stats['total_candidates']),
            str(len(stats['rooms'])),
            str(level)
        ]

        for j, cell in enumerate(level_data):
            pdf.set_xy(x, y)
            pdf.cell(LEVEL_COL_WIDTHS[j], ROW_HEIGHT_DATA_LEVELS, pdf.ar_text(cell), border=1, align='C')
            x += LEVEL_COL_WIDTHS[j]

        y += ROW_HEIGHT_DATA_LEVELS

    # ==== الصفحة الثانية: جدول تفاصيل القاعات (عمودي) ====[]
    pdf.add_page('P')  # صفحة عمودية
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 5

    # الجدول العلوي: مركز الامتحان وعنوان التقرير (للصفحة العمودية)
    pdf.set_font('Arial','B',13)  # تغيير الخط إلى Arial حجم 18
    pdf.set_text_color(0, 0, 128)  # أزرق غامق
    pdf.set_fill_color(230,230,230)
    x = margin
    for i, cell in enumerate(header_data):
        pdf.set_xy(x, y)
        pdf.cell(HEADER_COL_WIDTHS_PORTRAIT[i], ROW_HEIGHT_HEADER, pdf.ar_text(cell), border=1, align='C', fill=True)
        x += HEADER_COL_WIDTHS_PORTRAIT[i]
    y += ROW_HEIGHT_HEADER + 12

    # إعادة لون النص للعناصر الأخرى
    pdf.set_text_color(0, 0, 0)

    # عنوان جدول تفاصيل القاعات
    pdf.set_font('Arial','B',14)
    pdf.set_fill_color(150,150,150)
    pdf.set_xy(margin, y)
    pdf.cell(sum(ROOM_COL_WIDTHS), 10, pdf.ar_text("تفاصيل القاعات"), border=1, align='C', fill=True)
    y += 10

    # عناوين جدول القاعات
    pdf.set_font('Arial','B',13)  # تغيير الخط إلى Arial حجم 15
    pdf.set_text_color(0, 0, 128)  # أزرق غامق
    pdf.set_fill_color(200,200,200)
    x = margin
    for i, header in enumerate(ROOM_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(ROOM_COL_WIDTHS[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += ROOM_COL_WIDTHS[i]

    y += ROW_HEIGHT_TABLE_HEADER
    pdf.set_font('Arial','',12)  # تغيير الخط إلى Arial حجم 15
    pdf.set_text_color(0, 0, 128)  # أزرق غامق

    # ترتيب القاعات رقمياً
    sorted_room_keys = sorted(room_stats.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))

    # محتوى جدول القاعات
    for room_key in sorted_room_keys:
        stats = room_stats[room_key]
        x = margin

        room_data = [
            str(stats['level']),     # المستوى
            str(stats['max_exam']),  # نهاية رقم الامتحان
            str(stats['min_exam']),  # بداية رقم الامتحان
            str(stats['count']),     # عدد المترشحين
            str(room_key)            # رقم القاعة
        ]

        for j, cell in enumerate(room_data):
            pdf.set_xy(x, y)
            pdf.cell(ROOM_COL_WIDTHS[j], ROW_HEIGHT_DATA, pdf.ar_text(cell), border=1, align='C')
            x += ROOM_COL_WIDTHS[j]

        y += ROW_HEIGHT_DATA

        # التحقق من الحاجة لصفحة جديدة
        if y > pdf.h - 20:
            pdf.add_page('P')  # صفحة عمودية جديدة
            y = pdf.get_y()

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")

def print_exams_report(parent=None, level=None, report_title=None, subject_data=None):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        subject_data: بيانات المادة (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # تخصيص استعلام SQL حسب المستوى إذا تم تحديده
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استعلام مخصص إذا تم تحديد المستوى مع إضافة عمود الجنس
        if level:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, الجنس
            FROM امتحانات
            WHERE المستوى = ?
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query, (level,))
        else:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, الجنس
            FROM امتحانات
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query)

        cols = [c[0] for c in cursor.description]
        records = [dict(zip(cols, row)) for row in cursor.fetchall()]

        # جلب شعار المؤسسة
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

        conn.close()

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف بناءً على المستوى
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        level_suffix = f"_{level}" if level else ""
        output_path = os.path.join(reports_dir, f"تقرير_الاحصاءات{level_suffix}_{timestamp}.pdf")

        # إنشاء التقرير مع تمرير عنوان التقرير وبيانات المادة
        generate_report(logo_path, records, output_path, report_title, subject_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

if __name__=='__main__':
    try:
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)
        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"report_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out)
        
        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(out)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', out])
            else:  # Linux
                subprocess.call(['xdg-open', out])
        except Exception as e:
            print(f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}")
            
    except Exception as e:
        print(f"خطأ: {e}")
        traceback.print_exc()
