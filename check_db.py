#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3

def check_database():
    """فحص بنية قاعدة البيانات"""
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص بنية جدول الأساتذة
        print('=== بنية جدول الأساتذة ===')
        cursor.execute('PRAGMA table_info(الاساتذة)')
        columns = cursor.fetchall()
        for col in columns:
            print(f'{col[1]} ({col[2]})')
        
        print('\n=== فحص البيانات في جدول الأساتذة ===')
        cursor.execute('SELECT * FROM الاساتذة LIMIT 5')
        teachers = cursor.fetchall()
        for teacher in teachers:
            print(teacher)
        
        print('\n=== فحص بنية جدول الأقسام ===')
        cursor.execute('PRAGMA table_info(الاقسام)')
        sections_columns = cursor.fetchall()
        for col in sections_columns:
            print(f'{col[1]} ({col[2]})')
        
        print('\n=== عدد الأقسام ===')
        cursor.execute('SELECT COUNT(*) FROM الاقسام')
        sections_count = cursor.fetchone()[0]
        print(f'عدد الأقسام: {sections_count}')
        
        print('\n=== فحص الأقسام الموجودة (أول 10) ===')
        cursor.execute('SELECT * FROM الاقسام LIMIT 10')
        sections = cursor.fetchall()
        for section in sections:
            print(section)
        
        # التحقق من وجود أساتذة مرتبطين بأقسام
        print('\n=== فحص الربط بين الأساتذة والأقسام ===')
        cursor.execute('''
            SELECT COUNT(*) FROM الاساتذة WHERE قسم_id IS NOT NULL
        ''')
        linked_teachers = cursor.fetchone()[0]
        print(f'عدد الأساتذة المرتبطين بأقسام: {linked_teachers}')
        
        conn.close()
        
    except Exception as e:
        print(f'خطأ في فحص قاعدة البيانات: {e}')

if __name__ == "__main__":
    check_database()
