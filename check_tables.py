#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    print("🔍 فحص جداول قاعدة البيانات")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # عرض جميع الجداول
        print("\n📋 جميع الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        if not tables:
            print("   ❌ لا توجد جداول في قاعدة البيانات")
            return
        
        for i, table in enumerate(tables, 1):
            table_name = table[0]
            print(f"   {i}. {table_name}")
            
            # عرض عدد السجلات في كل جدول
            try:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                print(f"      📊 عدد السجلات: {count}")
                
                # عرض أعمدة الجدول
                cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                columns = cursor.fetchall()
                print(f"      📋 الأعمدة:")
                for col in columns:
                    print(f"         - {col[1]} ({col[2]})")
                
                # عرض عينة من البيانات إذا كان الجدول يحتوي على بيانات
                if count > 0:
                    cursor.execute(f"SELECT * FROM `{table_name}` LIMIT 3")
                    sample_data = cursor.fetchall()
                    print(f"      📝 عينة من البيانات:")
                    for j, row in enumerate(sample_data, 1):
                        print(f"         {j}. {row}")
                
                print()  # سطر فارغ
                
            except Exception as e:
                print(f"      ❌ خطأ في قراءة الجدول: {e}")
        
        # البحث عن جداول الطلاب المحتملة
        print("\n🔍 البحث عن جداول الطلاب:")
        student_tables = []
        for table in tables:
            table_name = table[0]
            if any(keyword in table_name.lower() for keyword in ['طالب', 'student', 'بيانات', 'data']):
                student_tables.append(table_name)
                print(f"   ✅ جدول محتمل للطلاب: {table_name}")
        
        if not student_tables:
            print("   ❌ لم يتم العثور على جداول طلاب واضحة")
        
        # البحث عن جداول الأقسام المحتملة
        print("\n🏫 البحث عن جداول الأقسام:")
        section_tables = []
        for table in tables:
            table_name = table[0]
            if any(keyword in table_name.lower() for keyword in ['قسم', 'section', 'مواد', 'subject']):
                section_tables.append(table_name)
                print(f"   ✅ جدول محتمل للأقسام: {table_name}")
        
        if not section_tables:
            print("   ❌ لم يتم العثور على جداول أقسام واضحة")
        
        conn.close()
        
        # اقتراحات للإصلاح
        print("\n💡 اقتراحات للإصلاح:")
        if student_tables:
            print(f"   📝 استخدم جدول الطلاب: {student_tables[0]}")
        if section_tables:
            print(f"   🏫 استخدم جدول الأقسام: {section_tables[0]}")
        
        print("\n🔧 لإصلاح نافذة الغياب:")
        print("   1. حدد الجدول الصحيح للطلاب")
        print("   2. حدد الجدول الصحيح للأقسام")
        print("   3. تأكد من وجود أعمدة الأسماء والأقسام")
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_tables()
