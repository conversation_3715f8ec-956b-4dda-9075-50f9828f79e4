#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_current_filter():
    """اختبار التصفية الحالية"""
    print("🧪 اختبار التصفية الحالية")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار مجموعات التصفية
        print("🔍 مجموعات التصفية (من جدول_المواد_والاقسام):")
        cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL ORDER BY المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        for i, group in enumerate(filter_groups, 1):
            print(f"   {i}. {group}")
        
        # اختبار مجموعات البيانات
        print("\n📋 مجموعات البيانات (من جدول_البيانات):")
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        data_groups = [row[0] for row in cursor.fetchall()]
        for i, group in enumerate(data_groups, 1):
            print(f"   {i}. {group}")
        
        # اختبار التصفية لكل مجموعة
        print("\n🔍 اختبار التصفية:")
        for group in filter_groups:
            print(f"\n📂 المجموعة: {group}")
            
            # جلب الأقسام للمجموعة
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_المواد_والاقسام 
                WHERE المجموعة = ? AND القسم IS NOT NULL 
                ORDER BY القسم
            """, (group,))
            sections = [row[0] for row in cursor.fetchall()]
            print(f"   📋 الأقسام المتاحة: {sections}")
            
            # اختبار التصفية (نفس الاستعلام المستخدم في التطبيق)
            cursor.execute("""
                SELECT COUNT(*) 
                FROM جدول_البيانات 
                WHERE اسم_المجموعة = ?
            """, (group,))
            count = cursor.fetchone()[0]
            print(f"   📊 عدد الطلاب في المجموعة: {count}")
            
            # اختبار التصفية لكل قسم
            for section in sections:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM جدول_البيانات 
                    WHERE اسم_المجموعة = ? AND القسم = ?
                """, (group, section))
                section_count = cursor.fetchone()[0]
                print(f"      📊 {section}: {section_count} طالب")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_current_filter()
