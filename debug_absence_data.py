#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def debug_absence_data():
    """تشخيص بيانات الغياب في قاعدة البيانات"""
    print("🔍 تشخيص بيانات الغياب")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص الجداول الموجودة
        print("\n📋 الجداول الموجودة:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"   ✅ {table[0]}")
        
        # فحص جدول البيانات
        print("\n👥 جدول البيانات:")
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        student_count = cursor.fetchone()[0]
        print(f"   📊 عدد الطلاب: {student_count}")
        
        if student_count > 0:
            cursor.execute("SELECT اسم_الطالب, اسم_المجموعة FROM جدول_البيانات LIMIT 5")
            students = cursor.fetchall()
            print("   📝 عينة من الطلاب:")
            for i, student in enumerate(students, 1):
                print(f"      {i}. {student[0]} - {student[1]}")
        
        # فحص جدول المواد والأقسام
        print("\n🏫 جدول المواد والأقسام:")
        cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
        section_count = cursor.fetchone()[0]
        print(f"   📊 عدد الأقسام: {section_count}")
        
        if section_count > 0:
            cursor.execute("SELECT القسم, المجموعة, اسم_الاستاذ FROM جدول_المواد_والاقسام")
            sections = cursor.fetchall()
            print("   📝 الأقسام والمجموعات:")
            for i, section in enumerate(sections, 1):
                print(f"      {i}. {section[0]} - {section[1]} - {section[2]}")
        
        # فحص جدول الغياب
        print("\n❌ جدول الغياب:")
        try:
            cursor.execute("SELECT COUNT(*) FROM absence_records")
            absence_count = cursor.fetchone()[0]
            print(f"   📊 عدد سجلات الغياب: {absence_count}")
            
            if absence_count > 0:
                cursor.execute("""
                    SELECT student_name, absence_date, notes 
                    FROM absence_records 
                    ORDER BY absence_date DESC 
                    LIMIT 5
                """)
                absences = cursor.fetchall()
                print("   📝 آخر سجلات الغياب:")
                for i, absence in enumerate(absences, 1):
                    print(f"      {i}. {absence[0]} - {absence[1]} - {absence[2]}")
            else:
                print("   ⚠️ لا توجد سجلات غياب")
                
        except sqlite3.OperationalError as e:
            print(f"   ❌ خطأ في جدول الغياب: {e}")
        
        # اختبار الاستعلام المبسط
        print("\n🔍 اختبار الاستعلام:")
        try:
            cursor.execute("""
                SELECT 
                    ب.اسم_الطالب,
                    ب.اسم_المجموعة,
                    م.القسم
                FROM جدول_البيانات ب
                LEFT JOIN جدول_المواد_والاقسام م ON ب.اسم_المجموعة = م.المجموعة
                LIMIT 5
            """)
            
            results = cursor.fetchall()
            print("   📝 نتائج الاستعلام:")
            for i, result in enumerate(results, 1):
                print(f"      {i}. {result[0]} - {result[1]} - {result[2]}")
                
        except Exception as e:
            print(f"   ❌ خطأ في الاستعلام: {e}")
        
        # إضافة بيانات تجريبية إذا لم تكن موجودة
        if student_count == 0:
            print("\n➕ إضافة بيانات تجريبية:")
            
            # إضافة طلاب
            test_students = [
                ("أحمد محمد علي", "المجموعة الأولى"),
                ("فاطمة حسن أحمد", "المجموعة الثانية"),
                ("محمد علي حسن", "المجموعة الأولى"),
                ("عائشة محمود علي", "المجموعة الثالثة"),
                ("علي أحمد محمد", "المجموعة الثانية")
            ]
            
            cursor.executemany("""
                INSERT INTO جدول_البيانات (اسم_الطالب, اسم_المجموعة)
                VALUES (?, ?)
            """, test_students)
            
            print(f"   ✅ تم إضافة {len(test_students)} طالب")
            
            # إضافة أقسام
            test_sections = [
                ("القسم الأول", "المجموعة الأولى", "أستاذ أحمد"),
                ("القسم الثاني", "المجموعة الثانية", "أستاذة فاطمة"),
                ("القسم الثالث", "المجموعة الثالثة", "أستاذ محمد")
            ]
            
            cursor.executemany("""
                INSERT INTO جدول_المواد_والاقسام (القسم, المجموعة, اسم_الاستاذ)
                VALUES (?, ?, ?)
            """, test_sections)
            
            print(f"   ✅ تم إضافة {len(test_sections)} قسم")
            
            # إضافة بعض سجلات الغياب التجريبية
            cursor.execute("SELECT id, اسم_الطالب FROM جدول_البيانات LIMIT 2")
            students_for_absence = cursor.fetchall()
            
            test_absences = []
            for student in students_for_absence:
                test_absences.append((student[0], student[1], "2024-01-15", "غياب تجريبي"))
                test_absences.append((student[0], student[1], "2024-01-16", "غياب تجريبي"))
            
            cursor.executemany("""
                INSERT INTO absence_records (student_id, student_name, absence_date, notes)
                VALUES (?, ?, ?, ?)
            """, test_absences)
            
            print(f"   ✅ تم إضافة {len(test_absences)} سجل غياب تجريبي")
            
            conn.commit()
        
        conn.close()
        
        print("\n✅ انتهى التشخيص")
        print("\n🚀 يمكنك الآن تشغيل نافذة الغياب:")
        print("   python absence_management_window.py")
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_absence_data()
