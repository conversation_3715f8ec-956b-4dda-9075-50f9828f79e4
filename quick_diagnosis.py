#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def quick_diagnosis():
    """تشخيص سريع لمشكلة عدم ظهور الطلاب"""
    print("⚡ تشخيص سريع لمشكلة عدم ظهور الطلاب")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        print("💡 الحل: تشغيل python fix_absence_tables.py")
        return
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص وجود الجداول
        print("\n📋 فحص الجداول:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        required_tables = ['جدول_البيانات', 'جدول_المواد_والاقسام']
        for table in required_tables:
            if table in tables:
                print(f"   ✅ {table} موجود")
            else:
                print(f"   ❌ {table} مفقود")
        
        # 2. فحص جدول البيانات
        if 'جدول_البيانات' in tables:
            print(f"\n👥 فحص جدول البيانات:")
            
            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {count}")
            
            if count > 0:
                # فحص الأعمدة
                cursor.execute("PRAGMA table_info(جدول_البيانات)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"   📋 الأعمدة: {columns}")
                
                # فحص البيانات
                cursor.execute("SELECT * FROM جدول_البيانات LIMIT 3")
                sample = cursor.fetchall()
                print(f"   📝 عينة من البيانات:")
                for i, row in enumerate(sample, 1):
                    print(f"      {i}. {row}")
                
                # فحص الأسماء الفارغة
                name_columns = [col for col in columns if 'اسم' in col.lower() or 'name' in col.lower()]
                for col in name_columns:
                    cursor.execute(f"SELECT COUNT(*) FROM جدول_البيانات WHERE `{col}` IS NOT NULL AND `{col}` != ''")
                    valid_names = cursor.fetchone()[0]
                    print(f"   👤 {col}: {valid_names} اسم صحيح")
                
                # فحص الأقسام
                section_columns = [col for col in columns if 'قسم' in col.lower() or 'section' in col.lower()]
                for col in section_columns:
                    cursor.execute(f"SELECT DISTINCT `{col}` FROM جدول_البيانات WHERE `{col}` IS NOT NULL AND `{col}` != ''")
                    sections = [row[0] for row in cursor.fetchall()]
                    print(f"   🏫 {col}: {sections}")
            
            else:
                print("   ⚠️ الجدول فارغ!")
        
        # 3. اختبار الاستعلام المستخدم في النافذة
        print(f"\n🔍 اختبار الاستعلام:")
        
        try:
            # محاكاة الاستعلام من النافذة
            query = """
                SELECT 
                    COALESCE(id, rowid) as id,
                    اسم_الطالب as student_name,
                    اسم_المجموعة as group_name
                FROM جدول_البيانات
                WHERE اسم_الطالب IS NOT NULL AND اسم_الطالب != ''
                ORDER BY اسم_الطالب
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            print(f"   📊 نتائج الاستعلام: {len(results)} طالب")
            
            if len(results) > 0:
                print(f"   📝 أول 3 نتائج:")
                for i, row in enumerate(results[:3], 1):
                    print(f"      {i}. ID: {row[0]}, الاسم: {row[1]}, المجموعة: {row[2]}")
            else:
                print("   ❌ لا توجد نتائج!")
                
                # اختبار استعلام أبسط
                print("   🔧 اختبار استعلام أبسط:")
                cursor.execute("SELECT اسم_الطالب FROM جدول_البيانات WHERE اسم_الطالب IS NOT NULL")
                simple_results = cursor.fetchall()
                print(f"      📊 الأسماء فقط: {len(simple_results)} نتيجة")
                
                if len(simple_results) > 0:
                    print(f"      📝 الأسماء: {[row[0] for row in simple_results[:5]]}")
        
        except Exception as e:
            print(f"   ❌ خطأ في الاستعلام: {e}")
        
        # 4. فحص جدول المواد والأقسام
        if 'جدول_المواد_والاقسام' in tables:
            print(f"\n🏫 فحص جدول المواد والأقسام:")
            
            cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
            sections_count = cursor.fetchone()[0]
            print(f"   📊 عدد السجلات: {sections_count}")
            
            if sections_count > 0:
                cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL")
                sections = [row[0] for row in cursor.fetchall()]
                print(f"   🏫 الأقسام المتاحة: {sections}")
                
                cursor.execute("SELECT DISTINCT المجموعة FROM جدول_المواد_والاقسام WHERE المجموعة IS NOT NULL")
                groups = [row[0] for row in cursor.fetchall()]
                print(f"   👥 المجموعات المتاحة: {groups}")
        
        conn.close()
        
        # 5. التشخيص والحلول
        print(f"\n🎯 التشخيص والحلول:")
        print("=" * 30)
        
        if 'جدول_البيانات' not in tables:
            print("❌ المشكلة: جدول البيانات غير موجود")
            print("💡 الحل: python fix_absence_tables.py")
        
        elif count == 0:
            print("❌ المشكلة: جدول البيانات فارغ")
            print("💡 الحل: python fix_absence_tables.py")
        
        elif len(results) == 0:
            print("❌ المشكلة: الاستعلام لا يجد بيانات صحيحة")
            print("💡 الحل: فحص أسماء الأعمدة أو البيانات الفارغة")
        
        else:
            print("✅ البيانات موجودة والاستعلام يعمل")
            print("💡 المشكلة قد تكون في النافذة أو التصفية")
        
        print(f"\n🚀 خطوات الإصلاح المقترحة:")
        print("1. python fix_absence_tables.py")
        print("2. python check_student_table_structure.py")
        print("3. python absence_management_window.py")
        print("4. اضغط زر 'تحديث البيانات' في النافذة")
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_diagnosis()
