#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QMessageBox, QSpinBox, QComboBox, QTableWidget, QTableWidgetItem,
    QCheckBox, QGroupBox, QFrame
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt

class LevelSelectionDialog(QDialog):
    """نافذة حوارية لاختيار وترتيب المستويات"""
    
    def __init__(self, parent=None, available_levels=None, db_path="data.db"):
        super().__init__(parent)
        self.available_levels = available_levels or []
        self.selected_levels = []
        self.parent_window = parent
        self.db_path = db_path
        self.setupUI()
        
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختيار وترتيب المستويات")
        self.setMinimumSize(900, 400)
        self.setStyleSheet("background-color: #f0f8ff;")
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel("اختر المستويات وحدد ترتيبها")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; padding: 10px; border: 2px solid #0066cc; border-radius: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التخطيط المباشر للمحتوى
        content_layout = QHBoxLayout()
        
        # القائمة اليسرى - المستويات المتاحة
        left_group = QGroupBox("المستويات المتاحة")
        left_group.setFont(QFont("Calibri", 13, QFont.Bold))
        left_layout = QVBoxLayout(left_group)
        
        self.available_list = QTableWidget()
        self.available_list.setColumnCount(2)
        self.available_list.setHorizontalHeaderLabels(["اختيار", "المستوى"])
        self.available_list.setRowCount(len(self.available_levels))
        self.available_list.setEditTriggers(QTableWidget.NoEditTriggers)
        self.available_list.setSelectionBehavior(QTableWidget.SelectRows)
        self.available_list.setFrameStyle(QFrame.NoFrame)
        
        # ملء قائمة المستويات المتاحة
        for row, level in enumerate(self.available_levels):
            # مربع اختيار
            checkbox = QCheckBox()
            self.available_list.setCellWidget(row, 0, checkbox)
            
            # اسم المستوى
            item = QTableWidgetItem(level)
            item.setTextAlignment(Qt.AlignCenter)
            item.setFont(QFont("Calibri", 12))
            self.available_list.setItem(row, 1, item)
        
        # تعيين عرض الأعمدة
        self.available_list.setColumnWidth(0, 80)
        self.available_list.setColumnWidth(1, 250)
        
        left_layout.addWidget(self.available_list)
        content_layout.addWidget(left_group)
        
        # الأزرار الوسطى
        middle_layout = QVBoxLayout()
        middle_layout.addStretch()
        
        add_button = QPushButton(">>>")
        add_button.setFont(QFont("Calibri", 14, QFont.Bold))
        add_button.setFixedSize(60, 40)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_button.clicked.connect(self.add_selected_levels)
        middle_layout.addWidget(add_button)
        
        remove_button = QPushButton("<<<")
        remove_button.setFont(QFont("Calibri", 14, QFont.Bold))
        remove_button.setFixedSize(60, 40)
        remove_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        remove_button.clicked.connect(self.remove_selected_levels)
        middle_layout.addWidget(remove_button)
        
        middle_layout.addStretch()
        content_layout.addLayout(middle_layout)
        
        # القائمة اليمنى - المستويات المحددة
        right_group = QGroupBox("المستويات المحددة (حسب الترتيب)")
        right_group.setFont(QFont("Calibri", 13, QFont.Bold))
        right_layout = QVBoxLayout(right_group)
        
        self.selected_list = QTableWidget()
        self.selected_list.setColumnCount(2)
        self.selected_list.setHorizontalHeaderLabels(["الترتيب", "المستوى"])
        self.selected_list.setEditTriggers(QTableWidget.NoEditTriggers)
        self.selected_list.setSelectionBehavior(QTableWidget.SelectRows)
        self.selected_list.setFrameStyle(QFrame.NoFrame)

        # تعيين عرض الأعمدة
        self.selected_list.setColumnWidth(0, 80)
        self.selected_list.setColumnWidth(1, 250)
        
        right_layout.addWidget(self.selected_list)
        
        # أزرار ترتيب المستويات المحددة
        order_buttons_layout = QHBoxLayout()
        
        move_up_button = QPushButton("تحريك لأعلى")
        move_up_button.setFont(QFont("Calibri", 11, QFont.Bold))
        move_up_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border-radius: 3px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        move_up_button.clicked.connect(self.move_level_up)
        order_buttons_layout.addWidget(move_up_button)
        
        move_down_button = QPushButton("تحريك لأسفل")
        move_down_button.setFont(QFont("Calibri", 11, QFont.Bold))
        move_down_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 3px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        move_down_button.clicked.connect(self.move_level_down)
        order_buttons_layout.addWidget(move_down_button)
        
        right_layout.addLayout(order_buttons_layout)
        content_layout.addWidget(right_group)
        
        layout.addLayout(content_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 13, QFont.Bold))
        ok_button.setFixedHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setFixedHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 5px;
                padding: 10px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
    def add_selected_levels(self):
        """إضافة المستويات المحددة إلى القائمة اليمنى"""
        for row in range(self.available_list.rowCount()):
            checkbox = self.available_list.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                level_item = self.available_list.item(row, 1)
                if level_item:
                    level_name = level_item.text()
                    if level_name not in self.selected_levels:
                        self.selected_levels.append(level_name)
                        checkbox.setChecked(False)  # إلغاء التحديد بعد الإضافة
        
        self.update_selected_list()
        
    def remove_selected_levels(self):
        """إزالة المستويات المحددة من القائمة اليمنى"""
        current_row = self.selected_list.currentRow()
        if current_row >= 0 and current_row < len(self.selected_levels):
            del self.selected_levels[current_row]
            self.update_selected_list()
            
    def move_level_up(self):
        """تحريك المستوى المحدد لأعلى"""
        current_row = self.selected_list.currentRow()
        if current_row > 0:
            # تبديل المواضع
            self.selected_levels[current_row], self.selected_levels[current_row - 1] = \
                self.selected_levels[current_row - 1], self.selected_levels[current_row]
            self.update_selected_list()
            self.selected_list.selectRow(current_row - 1)
            
    def move_level_down(self):
        """تحريك المستوى المحدد لأسفل"""
        current_row = self.selected_list.currentRow()
        if current_row >= 0 and current_row < len(self.selected_levels) - 1:
            # تبديل المواضع
            self.selected_levels[current_row], self.selected_levels[current_row + 1] = \
                self.selected_levels[current_row + 1], self.selected_levels[current_row]
            self.update_selected_list()
            self.selected_list.selectRow(current_row + 1)

    def update_selected_list(self):
        """تحديث قائمة المستويات المحددة"""
        self.selected_list.setRowCount(len(self.selected_levels))
        
        for row, level in enumerate(self.selected_levels):
            # رقم الترتيب
            order_item = QTableWidgetItem(str(row + 1))
            order_item.setTextAlignment(Qt.AlignCenter)
            order_item.setFont(QFont("Calibri", 12, QFont.Bold))
            self.selected_list.setItem(row, 0, order_item)
            
            # اسم المستوى
            level_item = QTableWidgetItem(level)
            level_item.setTextAlignment(Qt.AlignCenter)
            level_item.setFont(QFont("Calibri", 12))
            self.selected_list.setItem(row, 1, level_item)
            
    def get_selected_levels(self):
        """الحصول على المستويات المحددة بالترتيب"""
        return self.selected_levels.copy()

    def accept(self):
        """تنفيذ العملية عند الضغط على موافق"""
        if not self.selected_levels:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مستوى واحد على الأقل.")
            return
            
        try:
            # فتح نافذة sub24_window
            from sub24_window import ExamNumbersDialog
            
            # قائمة الأعمدة المتاحة للترتيب
            available_columns = [
                "القسم",
                "ر.ت", 
                "الجنس",
                "الاسم_الكامل",
                "تاريخ_الازدياد",
                "الرمز",
                "المؤسسة_الأصلية"
            ]
            
            exam_dialog = ExamNumbersDialog(
                parent=self.parent_window,
                start_number=1,  # رقم البداية الافتراضي
                available_columns=available_columns,
                ordered_levels=self.selected_levels,
                db_path=self.db_path
            )
            
            # إغلاق النافذة الحالية
            super().accept()
            
            # عرض نافذة أرقام الامتحان
            exam_dialog.exec_()
            
        except ImportError:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على ملف sub24_window.py")
            super().accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة أرقام الامتحان: {str(e)}")
            super().accept()

def select_and_order_levels(parent, db_path):
    """فتح نافذة لاختيار وترتيب المستويات"""
    try:
        # الحصول على المستويات المتاحة من قاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
        available_levels = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not available_levels:
            QMessageBox.warning(parent, "تنبيه", "لا توجد مستويات متاحة في قاعدة البيانات.")
            return []
            
        dialog = LevelSelectionDialog(parent, available_levels, db_path)
        if dialog.exec_() == QDialog.Accepted:
            return dialog.get_selected_levels()
        else:
            return []
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في جلب المستويات: {e}")
        return []


def add_exam_numbers_advanced(parent, db_path, exam_number_spin):
    """إضافة أرقام الامتحان للطلاب مع إمكانية اختيار طريقة الترتيب والمستويات المتعددة"""
    start_number = exam_number_spin.value()

    # عرض مربع حوار لاختيار وترتيب المستويات
    ordered_levels = select_and_order_levels(parent, db_path)

    if not ordered_levels:
        return

    # قائمة الأعمدة المتاحة للترتيب
    available_columns = [
        "القسم",
        "ر.ت",
        "الجنس",
        "الاسم_الكامل",
        "تاريخ_الازدياد",
        "الرمز",
        "المؤسسة_الأصلية"
    ]

    # إنشاء نافذة إضافة أرقام الامتحان
    try:
        from sub24_window import ExamNumbersDialog
        
        sort_dialog = ExamNumbersDialog(
            parent=parent,
            start_number=start_number,
            available_columns=available_columns,
            ordered_levels=ordered_levels
        )

        # عرض مربع الحوار
        if sort_dialog.exec_() != QDialog.Accepted:
            return

        # الحصول على الأعمدة المحددة بالترتيب
        sort_columns = sort_dialog.get_selected_columns()

        if not sort_columns:
            QMessageBox.warning(parent, "تنبيه", "الرجاء اختيار عمود واحد على الأقل للترتيب.")
            return

        # الحصول على رقم البداية من الحقل الجديد
        start_number = sort_dialog.get_start_number()
        
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة إضافة أرقام الامتحان: {e}")
        return

    # تأكيد العملية
    columns_text = "\n".join([f"{i+1}. {col}" for i, col in enumerate(sort_columns)])
    levels_text = "\n".join([f"{i+1}. {level}" for i, level in enumerate(ordered_levels)])

    reply = QMessageBox.question(
        parent,
        "تأكيد إضافة أرقام الامتحان",
        f"هل تريد توليد أرقام امتحان للمستويات التالية بدءًا من الرقم {start_number}؟\n\n"
        f"المستويات المحددة:\n{levels_text}\n\n"
        f"سيتم ترتيب المترشحين حسب الأعمدة التالية:\n{columns_text}",
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )

    if reply != QMessageBox.Yes:
        return

    QMessageBox.information(parent, "معلومات", "هذه الوظيفة قيد التطوير.")


# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import os
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    
    # تحديد مسار قاعدة البيانات للاختبار
    db_path = os.path.join(os.path.dirname(__file__), "data.db")
    
    try:
        # الحصول على المستويات من جدول امتحانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
        test_levels = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not test_levels:
            # في حالة عدم وجود مستويات في قاعدة البيانات، استخدم مستويات تجريبية
            test_levels = ["الجذع المشترك علوم", "الجذع المشترك آداب", "الأولى باك علوم تجريبية", "الأولى باك آداب", "الثانية باك علوم فيزيائية", "الثانية باك آداب"]
            QMessageBox.information(None, "معلومة", f"لا توجد مستويات في قاعدة البيانات {db_path}\nسيتم استخدام مستويات تجريبية للاختبار.")
            
    except Exception as e:
        # في حالة عدم وجود قاعدة البيانات أو حدوث خطأ، استخدم مستويات تجريبية
        test_levels = ["الجذع المشترك علوم", "الجذع المشترك آداب", "الأولى باك علوم تجريبية", "الأولى باك آداب", "الثانية باك علوم فيزيائية", "الثانية باك آداب"]
        QMessageBox.warning(None, "تنبيه", f"خطأ في الوصول لقاعدة البيانات: {str(e)}\nسيتم استخدام مستويات تجريبية للاختبار.")
    
    # اختبار نافذة اختيار المستويات
    dialog = LevelSelectionDialog(None, test_levels)
    dialog.show()
    
    sys.exit(app.exec_())
