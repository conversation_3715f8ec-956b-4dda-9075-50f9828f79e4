#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_monthly_duties_teacher_update():
    """اختبار تحديث جدول monthly_duties بمعلومات الأستاذ والقسم"""
    print("🧪 اختبار تحديث جدول monthly_duties بمعلومات الأستاذ والقسم")
    print("=" * 70)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # 1. فحص هيكل جدول monthly_duties
        print("1️⃣ فحص هيكل جدول monthly_duties:")
        cursor.execute("PRAGMA table_info(monthly_duties)")
        columns = cursor.fetchall()
        
        has_teacher_column = False
        has_section_column = False
        
        print("   📋 الأعمدة الموجودة:")
        for col in columns:
            print(f"      - {col[1]} ({col[2]})")
            if col[1] == 'اسم_الاستاذ':
                has_teacher_column = True
            if col[1] == 'القسم':
                has_section_column = True
        
        if has_teacher_column and has_section_column:
            print("   ✅ الأعمدة الجديدة موجودة!")
        else:
            print("   ⚠️ الأعمدة الجديدة غير موجودة، سيتم إضافتها...")
            
            if not has_teacher_column:
                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN اسم_الاستاذ TEXT")
                    print("   ✅ تم إضافة عمود اسم_الاستاذ")
                except Exception as e:
                    print(f"   ❌ خطأ في إضافة عمود اسم_الاستاذ: {e}")
            
            if not has_section_column:
                try:
                    cursor.execute("ALTER TABLE monthly_duties ADD COLUMN القسم TEXT")
                    print("   ✅ تم إضافة عمود القسم")
                except Exception as e:
                    print(f"   ❌ خطأ في إضافة عمود القسم: {e}")
        
        # 2. اختبار الربط بين الجداول
        print("\n2️⃣ اختبار الربط بين الجداول:")
        cursor.execute("""
            SELECT 
                ب.id,
                ب.اسم_التلميذ,
                ب.القسم as student_section,
                م.اسم_الاستاذ,
                م.القسم as materials_section
            FROM جدول_البيانات ب
            LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
            LIMIT 5
        """)
        
        test_records = cursor.fetchall()
        print("   📋 عينة من الربط:")
        for record in test_records:
            print(f"      - ID: {record[0]}, الطالب: {record[1]}, القسم: {record[2]}")
            print(f"        الأستاذ: {record[3]}, قسم المواد: {record[4]}")
        
        # 3. محاكاة إدراج واجب شهري جديد
        print("\n3️⃣ محاكاة إدراج واجب شهري جديد:")
        if test_records:
            student_id = test_records[0][0]
            student_name = test_records[0][1]
            teacher_name = test_records[0][3] if test_records[0][3] else "غير محدد"
            section_name = test_records[0][4] if test_records[0][4] else "غير محدد"
            
            print(f"   📝 الطالب: {student_name} (ID: {student_id})")
            print(f"   👨‍🏫 الأستاذ: {teacher_name}")
            print(f"   📚 القسم: {section_name}")
            
            # محاكاة الإدراج (بدون تنفيذ فعلي)
            insert_query = """
                INSERT INTO monthly_duties 
                (student_id, month, year, amount_required, amount_paid, amount_remaining,
                 payment_date, payment_status, notes, اسم_الاستاذ, القسم)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            print(f"   📋 استعلام الإدراج جاهز ✅")
            print(f"   📊 البيانات: student_id={student_id}, teacher={teacher_name}, section={section_name}")
        
        # 4. فحص السجلات الموجودة
        print("\n4️⃣ فحص السجلات الموجودة في monthly_duties:")
        cursor.execute("SELECT COUNT(*) FROM monthly_duties")
        count = cursor.fetchone()[0]
        print(f"   📊 عدد السجلات الموجودة: {count}")
        
        if count > 0:
            cursor.execute("""
                SELECT student_id, month, year, اسم_الاستاذ, القسم
                FROM monthly_duties
                LIMIT 5
            """)
            existing_records = cursor.fetchall()
            print("   📋 عينة من السجلات الموجودة:")
            for record in existing_records:
                print(f"      - طالب ID: {record[0]}, شهر: {record[1]}/{record[2]}")
                print(f"        أستاذ: {record[3]}, قسم: {record[4]}")
        
        # 5. اختبار تحديث السجلات الموجودة
        print("\n5️⃣ اختبار تحديث السجلات الموجودة:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM monthly_duties 
            WHERE اسم_الاستاذ IS NULL OR القسم IS NULL
        """)
        null_records = cursor.fetchone()[0]
        
        if null_records > 0:
            print(f"   ⚠️ يوجد {null_records} سجل بحاجة لتحديث معلومات الأستاذ/القسم")
            
            # محاكاة تحديث السجلات
            update_query = """
                UPDATE monthly_duties 
                SET اسم_الاستاذ = (
                    SELECT م.اسم_الاستاذ 
                    FROM جدول_البيانات ب
                    LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                    WHERE ب.id = monthly_duties.student_id
                ),
                القسم = (
                    SELECT م.القسم 
                    FROM جدول_البيانات ب
                    LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                    WHERE ب.id = monthly_duties.student_id
                )
                WHERE اسم_الاستاذ IS NULL OR القسم IS NULL
            """
            print("   📋 استعلام التحديث جاهز ✅")
            print("   💡 يمكن تشغيله لتحديث السجلات الموجودة")
        else:
            print("   ✅ جميع السجلات محدثة!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def update_existing_records():
    """تحديث السجلات الموجودة بمعلومات الأستاذ والقسم"""
    print("\n🔄 تحديث السجلات الموجودة بمعلومات الأستاذ والقسم")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص السجلات التي تحتاج تحديث
        cursor.execute("""
            SELECT COUNT(*) 
            FROM monthly_duties 
            WHERE اسم_الاستاذ IS NULL OR القسم IS NULL
        """)
        null_records = cursor.fetchone()[0]
        
        if null_records > 0:
            print(f"📊 يوجد {null_records} سجل بحاجة للتحديث")
            
            # تحديث السجلات
            cursor.execute("""
                UPDATE monthly_duties 
                SET اسم_الاستاذ = (
                    SELECT م.اسم_الاستاذ 
                    FROM جدول_البيانات ب
                    LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                    WHERE ب.id = monthly_duties.student_id
                ),
                القسم = (
                    SELECT م.القسم 
                    FROM جدول_البيانات ب
                    LEFT JOIN جدول_المواد_والاقسام م ON ب.القسم = م.القسم
                    WHERE ب.id = monthly_duties.student_id
                )
                WHERE اسم_الاستاذ IS NULL OR القسم IS NULL
            """)
            
            updated_count = cursor.rowcount
            conn.commit()
            
            print(f"✅ تم تحديث {updated_count} سجل بنجاح!")
        else:
            print("✅ جميع السجلات محدثة بالفعل!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحديث: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لتحديث جدول monthly_duties")
    print("=" * 80)
    
    test_monthly_duties_teacher_update()
    
    # سؤال المستخدم عن التحديث
    response = input("\n❓ هل تريد تحديث السجلات الموجودة؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم']:
        update_existing_records()
    
    print("\n✅ تم الانتهاء من الاختبار!")
    print("🎯 الآن عند تسجيل دفع جديد سيتم حفظ معلومات الأستاذ والقسم تلقائياً")
