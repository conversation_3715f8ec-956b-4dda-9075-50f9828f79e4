#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التقرير المحسن
"""

import sys
import os

def test_report_generation():
    """اختبار إنشاء التقرير المحسن"""
    try:
        print("🔧 اختبار إنشاء التقرير المحسن...")
        
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود: data.db")
            return False
        
        # استيراد نظام التقارير
        from attendance_sheet_report import create_attendance_sheet_report
        print("✅ تم استيراد نظام التقارير")
        
        # اختبار إنشاء تقرير
        print("📄 إنشاء تقرير تجريبي...")
        success, output_path, message = create_attendance_sheet_report(
            "قسم / 01",  # قسم تجريبي
            2024,        # سنة
            12,          # ديسمبر
            "ديسمبر"    # اسم الشهر
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير بنجاح: {output_path}")
            
            # التحقق من وجود الملف
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                if file_size > 1000:  # ملف معقول الحجم
                    print("✅ الملف يبدو صحيحاً")
                    return True
                else:
                    print("⚠️ الملف صغير جداً، قد يكون هناك مشكلة")
                    return False
            else:
                print("❌ الملف لم يُنشأ فعلياً")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_features():
    """اختبار ميزات PDF"""
    try:
        print("\n🔧 اختبار ميزات PDF...")
        
        # اختبار استيراد المكتبات
        try:
            from fpdf import FPDF
            print("✅ مكتبة FPDF متوفرة")
        except ImportError:
            print("❌ مكتبة FPDF غير متوفرة")
            print("   قم بتثبيتها: pip install fpdf2")
            return False
        
        try:
            import arabic_reshaper
            from bidi.algorithm import get_display
            print("✅ مكتبات النص العربي متوفرة")
        except ImportError:
            print("❌ مكتبات النص العربي غير متوفرة")
            print("   قم بتثبيتها: pip install arabic-reshaper python-bidi")
            return False
        
        # اختبار إنشاء PDF بسيط
        from attendance_sheet_report import ArabicPDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # اختبار الخطوط
        pdf.set_main_title_font()
        print("✅ خط العناوين الرئيسية يعمل")
        
        pdf.set_subtitle_font()
        print("✅ خط العناوين الفرعية يعمل")
        
        pdf.set_detail_font()
        print("✅ خط التفاصيل يعمل")
        
        pdf.set_table_header_font()
        print("✅ خط رؤوس الجدول يعمل")
        
        pdf.set_table_row_font()
        print("✅ خط صفوف الجدول يعمل")
        
        # اختبار النص العربي
        arabic_text = pdf.ar_text("اختبار النص العربي")
        print(f"✅ تحويل النص العربي يعمل: {arabic_text[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ميزات PDF: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("\n🔧 اختبار قاعدة البيانات...")
        
        import sqlite3
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار الأقسام
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL LIMIT 5")
        sections = cursor.fetchall()
        print(f"✅ الأقسام المتاحة: {[s[0] for s in sections]}")
        
        # اختبار التلاميذ
        if sections:
            test_section = sections[0][0]
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE القسم = ?", (test_section,))
            student_count = cursor.fetchone()[0]
            print(f"✅ عدد التلاميذ في {test_section}: {student_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار التقرير المحسن...")
    print("=" * 50)
    
    # اختبار قاعدة البيانات
    db_ok = test_database_connection()
    
    # اختبار ميزات PDF
    pdf_ok = test_pdf_features()
    
    # اختبار إنشاء التقرير
    report_ok = False
    if db_ok and pdf_ok:
        report_ok = test_report_generation()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   قاعدة البيانات: {'✅ نجح' if db_ok else '❌ فشل'}")
    print(f"   ميزات PDF: {'✅ نجح' if pdf_ok else '❌ فشل'}")
    print(f"   إنشاء التقرير: {'✅ نجح' if report_ok else '❌ فشل'}")
    
    if all([db_ok, pdf_ok, report_ok]):
        print("\n🎯 جميع الاختبارات نجحت!")
        print("\n📋 التحسينات المطبقة:")
        print("   ✅ الورقة عمودية")
        print("   ✅ الهوامش 0.2 من جميع الجوانب")
        print("   ✅ ترتيب الأعمدة معكوس")
        print("   ✅ خطوط Calibri مباشرة")
        print("   ✅ حدود سوداء متصلة 0.5")
        print("\n🚀 لتشغيل النافذة:")
        print("   python attendance_processing_window.py")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        
        if not pdf_ok:
            print("\n💡 لإصلاح مشاكل PDF:")
            print("   pip install fpdf2 arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
