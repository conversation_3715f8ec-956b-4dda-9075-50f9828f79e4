#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام معالجة الغياب
"""

import sys
import os

def main():
    """تشغيل نظام معالجة الغياب"""
    try:
        print("🚀 بدء تشغيل نظام معالجة الغياب...")
        
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود: data.db")
            print("   تأكد من وجود الملف في نفس مجلد البرنامج")
            input("اضغط Enter للخروج...")
            return
        
        # استيراد وتشغيل النافذة
        from PyQt5.QtWidgets import QApplication
        from attendance_processing_window import AttendanceProcessingWindow
        
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # إنشاء النافذة
        window = AttendanceProcessingWindow()
        window.show()
        
        print("✅ تم تشغيل النافذة بنجاح")
        print("📋 الميزات المتاحة:")
        print("   • عرض التلاميذ مرتبين حسب الرمز")
        print("   • تسجيل الغياب بالحصص")
        print("   • إنشاء تقارير PDF")
        print("   • ورقة متابعة الغياب الشهرية")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("   تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
