2025-06-04 18:38:29 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:38:30 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:38:30 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:38:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:38:30 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:38:30 - SubjectsTeachersWindow - DEBUG - إن<PERSON><PERSON><PERSON> جدول الأساتذة
2025-06-04 18:38:30 - SubjectsTeachersWindow - DEBUG - إض<PERSON><PERSON>ة 19 مادة افتراضية
2025-06-04 18:38:30 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:38:30 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:38:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:38:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:38:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:38:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:38:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 قسم في قاعدة البيانات
2025-06-04 18:38:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 0 قسم
2025-06-04 18:38:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:38:34 - SubjectsTeachersWindow - ERROR - خطأ في قاعدة البيانات أثناء تحميل الأساتذة: no such column: a.قسم_id
2025-06-04 18:38:34 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 850, in load_teachers
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        SELECT a.id, a.اسم_الاستاذ, m.اسم_المادة, s.اسم_القسم, a.تاريخ_الاضافة
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        ORDER BY a.اسم_الاستاذ
        ^^^^^^^^^^^^^^^^^^^^^^
    ''')
    ^^^^
sqlite3.OperationalError: no such column: a.قسم_id

2025-06-04 18:38:36 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:38:38 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:41:54 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:41:55 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:41:55 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:41:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:41:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:41:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:41:55 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:41:55 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:41:55 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:41:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:41:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 قسم في قاعدة البيانات
2025-06-04 18:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 0 قسم
2025-06-04 18:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:41:58 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:41:58 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:41:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:41:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:41:58 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:46:13 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:46:17 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:46:18 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:46:18 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:46:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:46:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:46:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:46:18 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:46:18 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:46:18 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:46:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:46:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 قسم في قاعدة البيانات
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 0 قسم
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:46:21 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:46:21 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:46:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:46:21 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:46:23 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:47:56 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:47:56 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:47:57 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:47:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:47:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:47:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:47:57 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:47:57 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:47:57 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:48:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:48:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 قسم في قاعدة البيانات
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 0 قسم
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:48:01 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:48:01 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:48:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:48:01 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:48:03 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:49:03 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:49:04 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:49:04 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:49:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:49:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:49:04 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:49:04 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:49:04 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:49:04 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:49:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:49:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 قسم في قاعدة البيانات
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 0 قسم
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:49:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:49:06 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:49:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:49:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:49:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:49:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 قسم في قاعدة البيانات
2025-06-04 18:49:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 1 قسم
2025-06-04 18:49:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:49:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:49:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:49:57 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:50:01 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:50:02 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:50:02 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:50:02 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:50:02 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:50:02 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:50:02 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:50:02 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:50:02 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:50:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:50:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:50:08 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:50:08 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:50:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:50:08 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:50:10 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:51:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:51:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:51:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:51:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:51:06 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:51:06 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:51:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:51:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:51:09 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:51:09 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:51:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:51:09 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:51:11 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:52:15 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:52:15 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:52:15 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:52:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:52:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:52:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:52:15 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:52:16 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:52:16 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:52:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:52:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:52:18 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:52:18 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:52:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:52:18 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:52:19 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:52:24 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:52:25 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:52:25 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:52:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:52:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:52:25 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:52:25 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:52:25 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:52:25 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:52:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:52:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:52:27 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:52:27 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:52:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:52:27 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:52:29 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:54:35 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:54:36 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:54:36 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:54:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:54:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:54:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:54:36 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:54:36 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:54:36 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:54:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:54:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:54:41 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:54:41 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:54:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:54:41 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:54:43 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:58:23 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:58:24 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:58:24 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:58:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:58:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:58:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:58:24 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:58:24 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:58:24 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:58:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:58:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:58:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:58:30 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:58:30 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:58:30 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:58:30 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:58:32 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 18:58:42 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 18:58:43 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 18:58:43 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 18:58:43 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 18:58:43 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 18:58:43 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 18:58:43 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 18:58:43 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 18:58:43 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 18:58:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 18:58:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 18:58:45 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 18:58:45 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 18:58:45 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 18:58:45 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 18:58:47 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:00:50 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:00:51 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:00:51 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:00:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:00:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:00:51 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:00:51 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:00:51 - SubjectsTeachersWindow - CRITICAL - خطأ في قاعدة البيانات أثناء الإعداد: no such column: ؟
2025-06-04 19:00:51 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 739, in setup_database
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        VALUES (؟)
        ^^^^^^^^^^
    ''', (subject,))
    ^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: ؟

2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:00:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:00:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 قسم في قاعدة البيانات
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 2 قسم
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:00:53 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:00:53 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:00:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:00:53 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:00:54 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:05:39 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:05:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:05:39 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:05:46 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:06:59 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:07:00 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:07:00 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:07:00 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:07:17 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:07:43 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:07:44 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:07:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:07:44 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:10:09 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:10:17 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:10:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:10:17 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:10:28 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:14:24 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:14:24 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:14:24 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:14:32 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:14:58 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.42.0
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة']
2025-06-04 19:14:59 - SubjectsTeachersWindow - WARNING - جدول الأساتذة لا يحتوي على عمود الأقسام - سيتم عرض 'غير محدد'
2025-06-04 19:14:59 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:14:59 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:15:08 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:22:33 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:22:33 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - إضافة عمود قسم_id إلى جدول الأساتذة
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - تم إضافة عمود قسم_id بنجاح
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 52, الأساتذة: 1
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 52 قسم في قاعدة البيانات
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 52 قسم
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:22:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 19:22:34 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - تم حذف الأساتذة المرتبطين بالقسم 55555
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:22:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 51 قسم في قاعدة البيانات
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 51 قسم
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:22:40 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:22:40 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 19:22:40 - SubjectsTeachersWindow - INFO - العملية: تم حذف القسم بنجاح - التفاصيل: 55555
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - تم حذف الأساتذة المرتبطين بالقسم 999999
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:22:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:22:44 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:22:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 19:22:44 - SubjectsTeachersWindow - INFO - العملية: تم حذف القسم بنجاح - التفاصيل: 999999
2025-06-04 19:23:06 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:23:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 19:23:07 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:23:55 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: محمد الطاهري, المادة: التربية الإسلامية, القسم: قسم / 01
2025-06-04 19:23:55 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:23:55 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:23:55 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:23:55 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:23:55 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: محمد الطاهري
2025-06-04 19:24:01 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:24:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:24:15 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:24:23 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:24:55 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:24:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:24:56 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:25:10 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:25:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:25:27 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:25:34 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:26:08 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:26:09 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:26:09 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:26:22 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:43:27 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:43:27 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 2
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:44:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 19:44:23 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 19:56:46 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: حسن الطاهري, المادة: التربية الإسلامية, القسم: قسم / 02
2025-06-04 19:56:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 19:56:46 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 19:56:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 19:56:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 19:56:46 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: حسن الطاهري
2025-06-04 20:03:20 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:03:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:03:21 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:03:25 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:11:32 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:11:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:11:33 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:11:48 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:12:34 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:12:35 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:12:35 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:12:56 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:17:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:17:29 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:17:54 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:18:07 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:18:08 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:18:08 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:18:27 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:18:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:18:33 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:19:06 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:19:36 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:19:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:19:37 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:19:49 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:29:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:29:47 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:30:10 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:31:13 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:31:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:31:14 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:31:25 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:31:57 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:31:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 20:31:58 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:32:03 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:42:31 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:42:32 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'QWidgets' is not defined
2025-06-04 20:42:32 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 506, in setup_teachers_tab
    spacer.setSizePolicy(QWidgets.QSizePolicy.Expanding, QWidgets.QSizePolicy.Expanding)
                         ^^^^^^^^
NameError: name 'QWidgets' is not defined. Did you mean: 'QWidget'?

2025-06-04 20:42:38 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:42:44 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:42:45 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'QWidgets' is not defined
2025-06-04 20:42:45 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 506, in setup_teachers_tab
    spacer.setSizePolicy(QWidgets.QSizePolicy.Expanding, QWidgets.QSizePolicy.Expanding)
                         ^^^^^^^^
NameError: name 'QWidgets' is not defined. Did you mean: 'QWidget'?

2025-06-04 20:43:06 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 3
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:43:41 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 20:43:41 - SubjectsTeachersWindow - ERROR - خطأ غير متوقع في تحميل الأساتذة: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'
2025-06-04 20:43:41 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 850, in load_teachers
    self.teachers_table.setRowCount(len(teachers))
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'

2025-06-04 20:43:44 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:44:28 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:45:03 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: محمد البقالي, المادة: التربية الإسلامية, القسم: قسم / 03
2025-06-04 20:45:03 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:45:03 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:45:03 - SubjectsTeachersWindow - DEBUG - تم العثور على 3 أستاذ في قاعدة البيانات
2025-06-04 20:45:03 - SubjectsTeachersWindow - ERROR - خطأ غير متوقع في تحميل الأساتذة: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'
2025-06-04 20:45:03 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 850, in load_teachers
    self.teachers_table.setRowCount(len(teachers))
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'

2025-06-04 20:45:06 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: محمد البقالي
2025-06-04 20:45:19 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 4
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:45:23 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - تم العثور على 3 أستاذ في قاعدة البيانات
2025-06-04 20:45:23 - SubjectsTeachersWindow - ERROR - خطأ غير متوقع في تحميل الأساتذة: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'
2025-06-04 20:45:23 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 850, in load_teachers
    self.teachers_table.setRowCount(len(teachers))
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'teachers_table'

2025-06-04 20:45:25 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:45:26 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 4
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:47:05 - SubjectsTeachersWindow - DEBUG - تم العثور على 3 أستاذ في قاعدة البيانات
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 3 أستاذ
2025-06-04 20:47:05 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:47:42 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: 4444444444, المادة: الإعلاميات, القسم: قسم / 01
2025-06-04 20:47:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:47:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:47:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:47:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:47:42 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: 4444444444
2025-06-04 20:47:51 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:47:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:47:58 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:48:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:48:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:48:11 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:48:13 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:48:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:48:18 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:49:03 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:49:03 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:51:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:51:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:51:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:51:13 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:52:41 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id']
2025-06-04 20:52:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:52:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:52:44 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 20:59:27 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 20:59:29 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 20:59:29 - SubjectsTeachersWindow - ERROR - خطأ في تحميل سجلات الأساتذة: no such column: a.نسبة_الواجبات
2025-06-04 20:59:29 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1378, in load_teachers_registry
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        SELECT
        ^^^^^^
    ...<9 lines>...
        ORDER BY a.تاريخ_الاضافة DESC
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ''')
    ^^^^
sqlite3.OperationalError: no such column: a.نسبة_الواجبات

2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - إضافة عمود نسبة_الواجبات إلى جدول الأساتذة
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - تم إضافة عمود نسبة_الواجبات بنجاح
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 5
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 20:59:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 4 أستاذ في قاعدة البيانات
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 4 أستاذ
2025-06-04 20:59:31 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 20:59:52 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: مممممم, المادة: الإعلاميات, القسم: قسم / 01, نسبة الواجبات: 20%
2025-06-04 20:59:52 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 20:59:52 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 20:59:52 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 20:59:52 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 20:59:52 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: مممممم
2025-06-04 21:00:06 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:00:33 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 6
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:00:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 21:00:34 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:00:52 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 6
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:00:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 21:00:57 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 6
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:02:26 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 21:02:26 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:02:39 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:03:00 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 6
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:03:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 21:03:01 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:03:07 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:12:45 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:12:46 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'
2025-06-04 21:12:46 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 220, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1350, in setup_teachers_registry_tab
    delete_btn.clicked.connect(self.delete_teachers_registry)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'. Did you mean: 'filter_teachers_registry'?

2025-06-04 21:12:50 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:12:52 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:12:53 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'
2025-06-04 21:12:53 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 220, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1350, in setup_teachers_registry_tab
    delete_btn.clicked.connect(self.delete_teachers_registry)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'. Did you mean: 'filter_teachers_registry'?

2025-06-04 21:12:58 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:13:02 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:13:02 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'
2025-06-04 21:13:02 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 220, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1350, in setup_teachers_registry_tab
    delete_btn.clicked.connect(self.delete_teachers_registry)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'delete_teachers_registry'. Did you mean: 'filter_teachers_registry'?

2025-06-04 21:13:10 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:15:11 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 6
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:15:12 - SubjectsTeachersWindow - DEBUG - تم العثور على 5 أستاذ في قاعدة البيانات
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 5 أستاذ
2025-06-04 21:15:12 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:15:25 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:15:25 - SubjectsTeachersWindow - INFO - العملية: تم حذف سجلات الأساتذة بنجاح - التفاصيل: تم حذف 6 سجل
2025-06-04 21:15:32 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:15:41 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:15:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 21:15:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:16:43 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: محمد الطاهري, المادة: التربية الإسلامية, القسم: قسم / 01, نسبة الواجبات: 50%
2025-06-04 21:16:43 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:16:43 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:16:43 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 21:16:43 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 21:16:43 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: محمد الطاهري
2025-06-04 21:16:47 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:16:55 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:20:20 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:20:21 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 21:20:21 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:20:44 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:20:48 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:20:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 21:20:49 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:21:15 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: حسن الطاهري, المادة: التربية الإسلامية, القسم: قسم / 01, نسبة الواجبات: 30%
2025-06-04 21:21:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:21:15 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:21:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 2 أستاذ في قاعدة البيانات
2025-06-04 21:21:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 2 أستاذ
2025-06-04 21:21:15 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: حسن الطاهري
2025-06-04 21:21:19 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:21:27 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:21:27 - SubjectsTeachersWindow - INFO - العملية: تم حذف سجل الأستاذ بنجاح - التفاصيل: تم حذف سجل الأستاذ: حسن الطاهري
2025-06-04 21:21:31 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:21:31 - SubjectsTeachersWindow - INFO - العملية: تم حذف سجل الأستاذ بنجاح - التفاصيل: تم حذف سجل الأستاذ: محمد الطاهري
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:23:55 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 21:23:55 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:23:59 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:30:14 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:30:15 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 21:30:15 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:32:46 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:37:40 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:37:41 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 21:37:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 21:37:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 21:37:53 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:50:21 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:50:22 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'
2025-06-04 21:50:22 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 514, in setup_teachers_tab
    add_form_layout.addRow(self.create_form_label("اسم الأستاذ:"), self.new_teacher_name_input)
                           ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'

2025-06-04 21:50:26 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:50:38 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:50:39 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'
2025-06-04 21:50:39 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 514, in setup_teachers_tab
    add_form_layout.addRow(self.create_form_label("اسم الأستاذ:"), self.new_teacher_name_input)
                           ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'

2025-06-04 21:50:42 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:50:51 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:50:51 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'
2025-06-04 21:50:51 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 514, in setup_teachers_tab
    add_form_layout.addRow(self.create_form_label("اسم الأستاذ:"), self.new_teacher_name_input)
                           ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'

2025-06-04 21:50:55 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:50:58 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:50:59 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'
2025-06-04 21:50:59 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 514, in setup_teachers_tab
    add_form_layout.addRow(self.create_form_label("اسم الأستاذ:"), self.new_teacher_name_input)
                           ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'create_form_label'

2025-06-04 21:51:03 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 21:54:45 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 21:54:47 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'filter_subject_label' is not defined
2025-06-04 21:54:47 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1195, in setup_teachers_registry_tab
    filter_subject_label.setFont(QFont("Calibri", 13))
    ^^^^^^^^^^^^^^^^^^^^
NameError: name 'filter_subject_label' is not defined

2025-06-04 21:54:51 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:08:21 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:08:23 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'filter_subject_label' is not defined
2025-06-04 22:08:23 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1194, in setup_teachers_registry_tab
    filter_subject_label.setFont(QFont("Calibri", 13))
    ^^^^^^^^^^^^^^^^^^^^
NameError: name 'filter_subject_label' is not defined

2025-06-04 22:08:26 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:08:37 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:08:37 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'filter_subject_label' is not defined
2025-06-04 22:08:37 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1194, in setup_teachers_registry_tab
    filter_subject_label.setFont(QFont("Calibri", 13))
    ^^^^^^^^^^^^^^^^^^^^
NameError: name 'filter_subject_label' is not defined

2025-06-04 22:09:30 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:09:32 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: name 'filter_subject_label' is not defined
2025-06-04 22:09:32 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 219, in setupUI
    self.setup_teachers_registry_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 1194, in setup_teachers_registry_tab
    filter_subject_label.setFont(QFont("Calibri", 13))
    ^^^^^^^^^^^^^^^^^^^^
NameError: name 'filter_subject_label' is not defined

2025-06-04 22:09:36 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:12:03 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:12:05 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:12:05 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:12:05 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:12:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:12:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:12:05 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:12:05 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:12:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:12:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:12:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:12:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:12:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:12:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:12:12 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:12:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:12:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:12:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:12:17 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:12:17 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:12:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:12:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:12:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:12:18 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:12:18 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:12:18 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:12:39 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:12:49 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:12:49 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:13:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:13:39 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:22:54 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:22:55 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'SubjectsTeachersWindow' object has no attribute 'get_groupbox_style'
2025-06-04 22:22:55 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 215, in setupUI
    self.setup_subjects_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 230, in setup_subjects_tab
    add_subject_frame.setStyleSheet(self.get_groupbox_style())
                                    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SubjectsTeachersWindow' object has no attribute 'get_groupbox_style'

2025-06-04 22:23:01 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:24:13 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:24:14 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:24:14 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:24:34 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:25:18 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:25:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:25:20 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:25:26 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:27:59 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:28:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:28:01 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:28:05 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:28:13 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:28:13 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:28:21 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:28:30 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:28:31 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:28:31 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:28:38 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:29:00 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:29:00 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:29:00 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:29:00 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:29:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:29:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:29:00 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:29:00 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:29:00 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:29:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:29:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:29:01 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:29:01 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:29:01 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:29:17 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:29:44 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:29:46 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:29:46 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:29:57 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:31:45 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:31:45 - SubjectsTeachersWindow - CRITICAL - خطأ في تهيئة النافذة: 'QLineEdit' object has no attribute 'setFontQFont'
2025-06-04 22:31:45 - SubjectsTeachersWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 30, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 215, in setupUI
    self.setup_subjects_tab()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri11\sub262_window.py", line 238, in setup_subjects_tab
    self.new_subject_input.setFontQFont("Calibri", 14, QFont.Bold)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QLineEdit' object has no attribute 'setFontQFont'

2025-06-04 22:31:50 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:32:39 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:32:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:32:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:32:49 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:32:55 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:32:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:32:56 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:33:04 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:33:33 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:33:34 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:33:34 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:33:41 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:34:52 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:34:55 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:34:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:34:56 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:35:09 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:36:46 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:36:47 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:36:47 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:37:00 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:38:41 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:38:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:38:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:38:52 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:42:15 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:42:17 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:42:17 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:42:35 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 0
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:42:44 - SubjectsTeachersWindow - DEBUG - تم العثور على 0 أستاذ في قاعدة البيانات
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 0 أستاذ
2025-06-04 22:42:44 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:43:26 - SubjectsTeachersWindow - INFO - العملية: محاولة إضافة أستاذ جديد - التفاصيل: الاسم: محمد الطاهري, المادة: التربية الإسلامية, القسم: قسم / 01, نسبة الواجبات: 20%
2025-06-04 22:43:26 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:43:26 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:43:26 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:43:26 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:43:26 - SubjectsTeachersWindow - INFO - العملية: تمت إضافة الأستاذ بنجاح - التفاصيل: محمد الطاهري
2025-06-04 22:43:32 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:47:20 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:47:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:47:22 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:47:32 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:50:18 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:50:19 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:50:19 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:50:30 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:52:35 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:52:39 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:52:39 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:52:48 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:53:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:53:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:53:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:53:13 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:53:19 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:53:20 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:53:20 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:53:27 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:57:34 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:57:37 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:57:37 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:57:45 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 22:57:55 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 22:57:56 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 22:57:56 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 22:58:33 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 23:01:31 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:01:32 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:01:32 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:02:24 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:02:29 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:02:29 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:02:32 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 23:07:05 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:07:07 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:07:07 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:07:20 - SubjectsTeachersWindow - INFO - العملية: بدء حفظ بيانات الأساتذة في جدول منفصل
2025-06-04 23:07:20 - SubjectsTeachersWindow - INFO - العملية: تم حفظ بيانات الأساتذة بنجاح - التفاصيل: تم حفظ 1 سجل أستاذ في جدول المواد والأقسام
2025-06-04 23:07:24 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:07:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:07:38 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:08:36 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:08:36 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:10:06 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:10:06 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:13:22 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:13:22 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:14:10 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:14:10 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:17:53 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:17:53 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:18:11 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:18:11 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:27:58 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:27:58 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:29:40 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:29:42 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:29:42 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:29:46 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
2025-06-04 23:41:56 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: c:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:41:57 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:41:57 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:42:37 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: C:\Users\<USER>\Desktop\taheri11\data.db
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.42.0
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:42:38 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:42:38 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:53:32 - SubjectsTeachersWindow - INFO - بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: بدء إعداد قاعدة البيانات - التفاصيل: مسار قاعدة البيانات: data.db
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - إصدار SQLite: 3.49.1
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد الدراسية
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأقسام
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول الأساتذة
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إنشاء جدول المواد والأقسام
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إضافة 19 مادة افتراضية
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - إضافة 50 قسم افتراضي
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - إحصائيات قاعدة البيانات - المواد: 19, الأقسام: 50, الأساتذة: 1
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل إعداد قاعدة البيانات بنجاح
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل المواد الدراسية
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 19 مادة في قاعدة البيانات
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل المواد بنجاح - التفاصيل: تم تحميل 19 مادة
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأقسام
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 50 قسم في قاعدة البيانات
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأقسام بنجاح - التفاصيل: تم تحميل 50 قسم
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: بدء تحميل الأساتذة
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - أعمدة جدول الأساتذة: ['id', 'اسم_الاستاذ', 'مادة_id', 'تاريخ_الاضافة', 'قسم_id', 'نسبة_الواجبات']
2025-06-04 23:53:33 - SubjectsTeachersWindow - DEBUG - تم العثور على 1 أستاذ في قاعدة البيانات
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - العملية: اكتمل تحميل الأساتذة بنجاح - التفاصيل: تم تحميل 1 أستاذ
2025-06-04 23:53:33 - SubjectsTeachersWindow - INFO - تم تحميل النافذة بنجاح
2025-06-04 23:53:45 - SubjectsTeachersWindow - INFO - العملية: إغلاق النافذة
