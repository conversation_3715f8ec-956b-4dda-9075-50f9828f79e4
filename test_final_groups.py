#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def test_filter_groups():
    """اختبار مجموعات التصفية (من جدول_البيانات)"""
    print("🔍 اختبار مجموعات التصفية")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # محاكاة وظيفة load_filter_options
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 عدد المجموعات في التصفية: {len(groups)}")
        print("📋 مجموعات التصفية (من جدول_البيانات):")
        for i, group in enumerate(groups, 1):
            print(f"   {i}. {group}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مجموعات التصفية: {e}")

def test_bulk_edit_groups():
    """اختبار مجموعات التعديل الجماعي (من جدول_المجموعات)"""
    print("\n🔄 اختبار مجموعات التعديل الجماعي")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول المجموعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المجموعات'")
        table_check = cursor.fetchone()
        
        if not table_check:
            print("❌ جدول المجموعات غير موجود")
            print("📋 النتيجة: القائمة ستحتوي على 'جدول المجموعات غير موجود'")
            conn.close()
            return
        
        # محاكاة وظيفة load_groups_to_combo
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 عدد المجموعات في التعديل الجماعي: {len(groups)}")
        print("📋 مجموعات التعديل الجماعي (من جدول_المجموعات):")
        print("   1. اختر المجموعة الجديدة")
        for i, group in enumerate(groups, 2):
            print(f"   {i}. {group}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مجموعات التعديل الجماعي: {e}")

def update_groups_table_with_correct_data():
    """تحديث جدول المجموعات بالبيانات الصحيحة"""
    print("\n🔧 تحديث جدول المجموعات بالبيانات الصحيحة")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # حذف البيانات القديمة
        print("🗑️ حذف البيانات القديمة...")
        cursor.execute("DELETE FROM جدول_المجموعات")
        
        # إدراج البيانات الصحيحة (من الصورة الثانية)
        correct_groups = [
            ("مجموعة مغلقة", "مجموعة مغلقة"),
            ("مجموعة مفتوحة", "مجموعة مفتوحة"),
            ("مجموعة الدراسات", "مجموعة الدراسات"),
            ("مجموعة خاصة", "مجموعة خاصة")
        ]
        
        print("📝 إدراج البيانات الصحيحة...")
        for group_name, description in correct_groups:
            cursor.execute("""
                INSERT INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)
                VALUES (?, ?)
            """, (group_name, description))
            print(f"   ✅ تم إدراج: {group_name}")
        
        conn.commit()
        conn.close()
        print("✅ تم تحديث جدول المجموعات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث جدول المجموعات: {e}")

def compare_sources():
    """مقارنة مصادر البيانات"""
    print("\n📊 مقارنة مصادر البيانات")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # مجموعات من جدول_البيانات (للتصفية)
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_البيانات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        filter_groups = [row[0] for row in cursor.fetchall()]
        
        # مجموعات من جدول_المجموعات (للتعديل الجماعي)
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        bulk_edit_groups = [row[0] for row in cursor.fetchall()]
        
        print("🔍 مجموعات التصفية (جدول_البيانات):")
        for group in filter_groups:
            print(f"   • {group}")
        
        print("\n🔄 مجموعات التعديل الجماعي (جدول_المجموعات):")
        for group in bulk_edit_groups:
            print(f"   • {group}")
        
        print(f"\n📈 إحصائيات:")
        print(f"   - مجموعات التصفية: {len(filter_groups)}")
        print(f"   - مجموعات التعديل الجماعي: {len(bulk_edit_groups)}")
        
        # التحقق من التطابق
        if set(filter_groups) == set(bulk_edit_groups):
            print("   ✅ المجموعات متطابقة")
        else:
            print("   ⚠️ المجموعات مختلفة")
            print("   🔍 المجموعات الموجودة في التصفية فقط:")
            for group in set(filter_groups) - set(bulk_edit_groups):
                print(f"      - {group}")
            print("   🔍 المجموعات الموجودة في التعديل الجماعي فقط:")
            for group in set(bulk_edit_groups) - set(filter_groups):
                print(f"      - {group}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في المقارنة: {e}")

if __name__ == "__main__":
    print("🧪 اختبار شامل لمصادر بيانات المجموعات")
    print("=" * 60)
    
    # تحديث جدول المجموعات أولاً
    update_groups_table_with_correct_data()
    
    # اختبار مصادر البيانات
    test_filter_groups()
    test_bulk_edit_groups()
    
    # مقارنة المصادر
    compare_sources()
