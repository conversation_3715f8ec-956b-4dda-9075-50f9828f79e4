#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_fixed_archived_window():
    """اختبار النافذة المحسنة بعد إصلاح مشاكل التخطيط"""
    print("🎨 اختبار النافذة المحسنة بعد إصلاح التخطيط")
    print("=" * 60)
    
    try:
        # فحص وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            print("💡 قم بتشغيل التطبيق الرئيسي أولاً لإنشاء قاعدة البيانات")
            return
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # اختبار استيراد النافذة
        print("\n🔧 اختبار استيراد النافذة المحسنة:")
        try:
            from archived_accounts_window import ArchivedAccountsWindow
            print("   ✅ تم استيراد النافذة بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
            return
        
        # إنشاء التطبيق
        print("\n🖥️ إنشاء التطبيق:")
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        print("   ✅ تم إنشاء التطبيق")
        
        # إنشاء النافذة
        print("\n🏗️ إنشاء النافذة:")
        try:
            window = ArchivedAccountsWindow()
            print("   ✅ تم إنشاء النافذة بنجاح")
            
            # فحص التحسينات الجديدة
            print("\n🔍 فحص التحسينات الجديدة:")
            
            # فحص اتجاه RTL
            if window.layoutDirection() == 2:  # Qt.RightToLeft = 2
                print("   ✅ اتجاه RTL مطبق بشكل صحيح")
            else:
                print("   ⚠️ اتجاه RTL غير مطبق")
            
            # فحص حجم النافذة
            size = window.size()
            print(f"   📐 حجم النافذة: {size.width()} x {size.height()}")
            
            # فحص الجدول المحسن
            if hasattr(window, 'table'):
                print(f"   ✅ الجدول موجود - عدد الأعمدة: {window.table.columnCount()}")
                
                # فحص عرض الأعمدة
                column_widths = []
                for i in range(window.table.columnCount()):
                    width = window.table.columnWidth(i)
                    column_widths.append(width)
                print(f"   📏 عرض الأعمدة: {column_widths}")
                
                # فحص ارتفاع الجدول
                table_height = window.table.minimumHeight()
                print(f"   📏 ارتفاع الجدول الأدنى: {table_height}px")
            else:
                print("   ❌ الجدول غير موجود")
            
            # فحص عناصر التحكم
            controls_found = 0
            controls_list = [
                ('month_combo', 'قائمة الشهور'),
                ('year_spin', 'مربع السنة'),
                ('force_update_check', 'خيار إعادة الترحيل'),
                ('search_box', 'مربع البحث')
            ]
            
            for control_name, control_desc in controls_list:
                if hasattr(window, control_name):
                    controls_found += 1
                    control = getattr(window, control_name)
                    
                    # فحص خصائص العنصر
                    if hasattr(control, 'width'):
                        width = control.width()
                        print(f"   ✅ {control_desc}: موجود - العرض: {width}px")
                    else:
                        print(f"   ✅ {control_desc}: موجود")
                else:
                    print(f"   ❌ {control_desc}: مفقود")
            
            print(f"   📊 عناصر التحكم الموجودة: {controls_found}/{len(controls_list)}")
            
            # فحص لوحة الإحصائيات
            stats_labels = [
                'total_archived_label', 'total_months_label', 
                'total_amount_label', 'db_size_label'
            ]
            stats_found = sum(1 for label in stats_labels if hasattr(window, label))
            print(f"   📊 لوحة الإحصائيات: {stats_found}/{len(stats_labels)} عنصر موجود")
            
            # فحص شريط المعلومات
            info_labels = ['db_info', 'last_archive_info']
            info_found = sum(1 for label in info_labels if hasattr(window, label))
            print(f"   ℹ️ شريط المعلومات: {info_found}/{len(info_labels)} عنصر موجود")
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء النافذة: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # اختبار تحميل البيانات
        print("\n📊 اختبار تحميل البيانات:")
        try:
            window.load_archived_months()
            row_count = window.table.rowCount()
            print(f"   ✅ تم تحميل البيانات - عدد الصفوف: {row_count}")
            
            if row_count > 0:
                print("   📝 عينة من البيانات:")
                for row in range(min(3, row_count)):
                    month_item = window.table.item(row, 0)
                    year_item = window.table.item(row, 1)
                    count_item = window.table.item(row, 2)
                    
                    if month_item and year_item and count_item:
                        print(f"      {row+1}. {month_item.text()}/{year_item.text()} - {count_item.text()} سجل")
            else:
                print("   ⚠️ لا توجد بيانات مرحلة")
                
        except Exception as e:
            print(f"   ❌ خطأ في تحميل البيانات: {e}")
        
        # اختبار الوظائف المحسنة
        print("\n🔧 اختبار الوظائف المحسنة:")
        
        # اختبار تحديث الإحصائيات
        try:
            window.update_stats_panel()
            print("   ✅ تحديث لوحة الإحصائيات يعمل")
        except Exception as e:
            print(f"   ❌ خطأ في تحديث الإحصائيات: {e}")
        
        # اختبار تحديث شريط المعلومات
        try:
            window.update_info_bar()
            print("   ✅ تحديث شريط المعلومات يعمل")
        except Exception as e:
            print(f"   ❌ خطأ في تحديث شريط المعلومات: {e}")
        
        # اختبار البحث
        try:
            if hasattr(window, 'search_box'):
                window.search_box.setText("يناير")
                window.filter_table()
                print("   ✅ وظيفة البحث تعمل")
                
                # إعادة تعيين البحث
                window.search_box.setText("")
                window.filter_table()
            else:
                print("   ⚠️ مربع البحث غير موجود")
        except Exception as e:
            print(f"   ❌ خطأ في وظيفة البحث: {e}")
        
        # عرض النافذة للاختبار البصري
        print(f"\n🖼️ عرض النافذة للاختبار البصري:")
        print("   💡 ستظهر النافذة لمدة 15 ثانية للمراجعة البصرية")
        print("   🎯 تحقق من:")
        print("      - ترتيب العناصر من اليمين لليسار")
        print("      - تناسق الألوان والخطوط")
        print("      - عدم وجود تشتت في العناصر")
        print("      - وضوح النصوص والأيقونات")
        
        window.show()
        
        # تشغيل حلقة الأحداث لفترة أطول
        import time
        start_time = time.time()
        while time.time() - start_time < 15:  # 15 ثانية
            app.processEvents()
            time.sleep(0.1)
        
        window.close()
        
        print("   ✅ تم إغلاق النافذة")
        
        # النتيجة النهائية
        print(f"\n🎯 النتيجة النهائية:")
        print("=" * 50)
        print("✅ تم إصلاح مشاكل التخطيط")
        print("✅ اتجاه RTL مطبق بشكل صحيح")
        print("✅ العناصر منظمة ومتناسقة")
        print("✅ الخطوط والألوان محسنة")
        print("✅ أحجام العناصر مناسبة")
        
        print(f"\n💡 التحسينات المطبقة:")
        print("   🎨 تطبيق اتجاه RTL للنافذة والعناصر")
        print("   📐 تحسين أحجام وأبعاد العناصر")
        print("   🎯 تقليل المساحات الفارغة")
        print("   📝 تحسين الخطوط والنصوص")
        print("   🌈 تناسق الألوان والثيم")
        print("   📱 تحسين التخطيط العام")
        
        print(f"\n🚀 للاستخدام:")
        print("   python archived_accounts_window.py")
        print("   أو من النافذة الرئيسية: زر 'إدارة الحسابات المرحلة'")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_archived_window()
