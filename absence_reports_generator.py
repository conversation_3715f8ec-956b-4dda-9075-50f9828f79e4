#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مولد التقارير الورقية للغياب
- تقرير الغياب الشهري
- تقرير الغياب السنوي  
- تقرير الغياب حسب التلميذ
"""

import os
import sys
import sqlite3
from datetime import datetime
from fpdf import FPDF

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class AbsenceReportPDF(FPDF):
    def __init__(self):
        super().__init__('P', 'mm', 'A4')
        self.set_margins(10, 10, 10)
        self.set_auto_page_break(auto=True, margin=10)
        
        # فحص توفر خط Calibri
        self.calibri_available = False
        try:
            self.add_font('Calibri', '', 'calibri.ttf', uni=True)
            self.add_font('Calibri', 'B', 'calibrib.ttf', uni=True)
            self.calibri_available = True
        except:
            pass
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 12)
        else:
            self.set_font('Arial', '', 12)

    def ar_text(self, text):
        """تحويل النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT:
            return str(text)
        
        try:
            if isinstance(text, (int, float)):
                return str(text)
            
            text = str(text)
            if not text.strip():
                return text
            
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return str(text)

    def header(self):
        """رأس الصفحة"""
        if self.calibri_available:
            self.set_font('Calibri', 'B', 16)
        else:
            self.set_font('Arial', 'B', 16)
        
        self.cell(0, 10, self.ar_text('تقارير الغياب'), 0, 1, 'C')
        self.ln(5)

    def footer(self):
        """تذييل الصفحة"""
        self.set_y(-15)
        if self.calibri_available:
            self.set_font('Calibri', '', 10)
        else:
            self.set_font('Arial', '', 10)
        
        self.cell(0, 10, f'{self.page_no()}', 0, 0, 'C')

def generate_monthly_absence_report(year, month, section=None, db_path="data.db"):
    """إنشاء تقرير الغياب الشهري"""
    try:
        print(f"📊 إنشاء تقرير الغياب الشهري: {month}/{year}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # بناء الاستعلام
        where_clause = "WHERE year = ? AND month = ?"
        params = [year, month]
        
        if section and section != "جميع الأقسام":
            where_clause += " AND section = ?"
            params.append(section)
        
        cursor.execute(f"""
            SELECT student_name, student_code, section, 
                   total_sessions, absent_sessions, attendance_rate
            FROM monthly_absence_stats 
            {where_clause}
            ORDER BY section, student_name
        """, params)
        
        data = cursor.fetchall()
        conn.close()
        
        if not data:
            return False, None, "لا توجد بيانات للفترة المحددة"
        
        # إنشاء PDF
        pdf = AbsenceReportPDF()
        pdf.add_page()
        
        # العنوان
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 14)
        else:
            pdf.set_font('Arial', 'B', 14)
        
        title = f"تقرير الغياب الشهري - {month}/{year}"
        if section and section != "جميع الأقسام":
            title += f" - {section}"
        
        pdf.cell(0, 10, pdf.ar_text(title), 0, 1, 'C')
        pdf.ln(5)
        
        # رؤوس الجدول
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 12)
        else:
            pdf.set_font('Arial', 'B', 12)
        
        headers = ['اسم التلميذ', 'الرمز', 'القسم', 'إجمالي الحصص', 'الحصص المتغيب عنها', 'نسبة الحضور%']
        col_widths = [50, 25, 30, 25, 35, 25]
        
        for i, header in enumerate(headers):
            pdf.cell(col_widths[i], 10, pdf.ar_text(header), 1, 0, 'C')
        pdf.ln()
        
        # البيانات
        if pdf.calibri_available:
            pdf.set_font('Calibri', '', 10)
        else:
            pdf.set_font('Arial', '', 10)
        
        for row in data:
            for i, cell in enumerate(row):
                pdf.cell(col_widths[i], 8, pdf.ar_text(str(cell)), 1, 0, 'C')
            pdf.ln()
        
        # الإحصائيات
        pdf.ln(5)
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 12)
        else:
            pdf.set_font('Arial', 'B', 12)
        
        total_students = len(data)
        total_absent_sessions = sum(row[4] for row in data)
        avg_attendance = sum(row[5] for row in data) / total_students if total_students > 0 else 0
        
        pdf.cell(0, 8, pdf.ar_text(f"إجمالي التلاميذ: {total_students}"), 0, 1)
        pdf.cell(0, 8, pdf.ar_text(f"إجمالي الحصص المتغيب عنها: {total_absent_sessions}"), 0, 1)
        pdf.cell(0, 8, pdf.ar_text(f"متوسط نسبة الحضور: {avg_attendance:.2f}%"), 0, 1)
        
        return pdf, data, "تم إنشاء التقرير بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير الشهري: {e}")
        return False, None, f"خطأ: {str(e)}"

def generate_yearly_absence_report(year, section=None, db_path="data.db"):
    """إنشاء تقرير الغياب السنوي"""
    try:
        print(f"📈 إنشاء تقرير الغياب السنوي: {year}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # بناء الاستعلام
        where_clause = "WHERE year = ?"
        params = [year]
        
        if section and section != "جميع الأقسام":
            where_clause += " AND section = ?"
            params.append(section)
        
        cursor.execute(f"""
            SELECT student_name, student_code, section, 
                   total_sessions, absent_sessions, attendance_rate
            FROM yearly_absence_stats 
            {where_clause}
            ORDER BY section, student_name
        """, params)
        
        data = cursor.fetchall()
        conn.close()
        
        if not data:
            return False, None, "لا توجد بيانات للسنة المحددة"
        
        # إنشاء PDF (مشابه للتقرير الشهري)
        pdf = AbsenceReportPDF()
        pdf.add_page()
        
        # العنوان
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 14)
        else:
            pdf.set_font('Arial', 'B', 14)
        
        title = f"تقرير الغياب السنوي - {year}"
        if section and section != "جميع الأقسام":
            title += f" - {section}"
        
        pdf.cell(0, 10, pdf.ar_text(title), 0, 1, 'C')
        pdf.ln(5)
        
        # رؤوس الجدول والبيانات (نفس الطريقة)
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 12)
        else:
            pdf.set_font('Arial', 'B', 12)
        
        headers = ['اسم التلميذ', 'الرمز', 'القسم', 'إجمالي الحصص', 'الحصص المتغيب عنها', 'نسبة الحضور%']
        col_widths = [50, 25, 30, 25, 35, 25]
        
        for i, header in enumerate(headers):
            pdf.cell(col_widths[i], 10, pdf.ar_text(header), 1, 0, 'C')
        pdf.ln()
        
        # البيانات
        if pdf.calibri_available:
            pdf.set_font('Calibri', '', 10)
        else:
            pdf.set_font('Arial', '', 10)
        
        for row in data:
            for i, cell in enumerate(row):
                pdf.cell(col_widths[i], 8, pdf.ar_text(str(cell)), 1, 0, 'C')
            pdf.ln()
        
        return pdf, data, "تم إنشاء التقرير بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير السنوي: {e}")
        return False, None, f"خطأ: {str(e)}"

def generate_student_absence_report(student_id, db_path="data.db"):
    """إنشاء تقرير الغياب حسب التلميذ"""
    try:
        print(f"👤 إنشاء تقرير الغياب للتلميذ: {student_id}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات التلميذ
        cursor.execute("""
            SELECT اسم_التلميذ, رمز_التلميذ, القسم
            FROM جدول_البيانات 
            WHERE id = ?
        """, (student_id,))
        
        student_info = cursor.fetchone()
        if not student_info:
            return False, None, "التلميذ غير موجود"
        
        # جلب سجلات الغياب التفصيلية
        cursor.execute("""
            SELECT absence_date, session_1, session_2, session_3, notes
            FROM daily_absence 
            WHERE student_id = ?
            ORDER BY absence_date DESC
        """, (student_id,))
        
        absence_records = cursor.fetchall()
        
        # جلب الإحصائيات الشهرية
        cursor.execute("""
            SELECT year, month, total_sessions, absent_sessions, attendance_rate
            FROM monthly_absence_stats 
            WHERE student_id = ?
            ORDER BY year DESC, month DESC
        """, (student_id,))
        
        monthly_stats = cursor.fetchall()
        
        conn.close()
        
        # إنشاء PDF
        pdf = AbsenceReportPDF()
        pdf.add_page()
        
        # معلومات التلميذ
        if pdf.calibri_available:
            pdf.set_font('Calibri', 'B', 14)
        else:
            pdf.set_font('Arial', 'B', 14)
        
        pdf.cell(0, 10, pdf.ar_text(f"تقرير الغياب - {student_info[0]}"), 0, 1, 'C')
        pdf.ln(5)
        
        if pdf.calibri_available:
            pdf.set_font('Calibri', '', 12)
        else:
            pdf.set_font('Arial', '', 12)
        
        pdf.cell(0, 8, pdf.ar_text(f"الرمز: {student_info[1]}"), 0, 1)
        pdf.cell(0, 8, pdf.ar_text(f"القسم: {student_info[2]}"), 0, 1)
        pdf.ln(5)
        
        # الإحصائيات الشهرية
        if monthly_stats:
            if pdf.calibri_available:
                pdf.set_font('Calibri', 'B', 12)
            else:
                pdf.set_font('Arial', 'B', 12)
            
            pdf.cell(0, 8, pdf.ar_text("الإحصائيات الشهرية:"), 0, 1)
            
            # رؤوس الجدول
            headers = ['السنة', 'الشهر', 'إجمالي الحصص', 'الحصص المتغيب عنها', 'نسبة الحضور%']
            col_widths = [30, 30, 40, 50, 40]
            
            for i, header in enumerate(headers):
                pdf.cell(col_widths[i], 8, pdf.ar_text(header), 1, 0, 'C')
            pdf.ln()
            
            # البيانات
            if pdf.calibri_available:
                pdf.set_font('Calibri', '', 10)
            else:
                pdf.set_font('Arial', '', 10)
            
            for row in monthly_stats:
                for i, cell in enumerate(row):
                    pdf.cell(col_widths[i], 6, pdf.ar_text(str(cell)), 1, 0, 'C')
                pdf.ln()
        
        return pdf, {'student_info': student_info, 'absence_records': absence_records, 'monthly_stats': monthly_stats}, "تم إنشاء التقرير بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير التلميذ: {e}")
        return False, None, f"خطأ: {str(e)}"

def save_and_open_report(pdf, report_type, filename_suffix):
    """حفظ وفتح التقرير"""
    try:
        # إنشاء مجلد التقارير
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الغياب الورقية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"تقرير_{report_type}_{filename_suffix}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)
        
        # حفظ الملف
        pdf.output(output_path)
        print(f"✅ تم حفظ التقرير: {output_path}")
        
        # فتح الملف
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
        except Exception as e:
            print(f"⚠️ تم إنشاء التقرير ولكن تعذر فتحه: {e}")
        
        return True, output_path
        
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير: {e}")
        return False, None

if __name__ == "__main__":
    # اختبار التقارير
    print("🚀 اختبار مولد التقارير...")
    
    # تقرير شهري
    pdf, data, msg = generate_monthly_absence_report(2024, 12)
    if pdf:
        save_and_open_report(pdf, "شهري", "ديسمبر_2024")
    
    print("✅ انتهاء الاختبار")
