#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_fix_current_year():
    """اختبار إصلاح خطأ current_year"""
    print("🧪 اختبار إصلاح خطأ current_year")
    print("=" * 40)
    
    try:
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return
        
        # اختبار استيراد الوظائف
        print("1️⃣ اختبار استيراد الوظائف:")
        try:
            from print_section_monthly import print_section_monthly_report
            from print_section_monthly import get_section_info_from_db
            from print_section_monthly import get_monthly_duties_by_section_month
            print("   ✅ تم استيراد جميع الوظائف بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
            return
        
        # جلب بيانات للاختبار
        print("\n2️⃣ جلب بيانات للاختبار:")
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # جلب شهر للاختبار
            cursor.execute("""
                SELECT DISTINCT month, year 
                FROM monthly_duties 
                LIMIT 1
            """)
            
            test_data = cursor.fetchone()
            if test_data:
                test_month, test_year = test_data
                print(f"   📅 شهر الاختبار: {test_month}/{test_year}")
            else:
                test_month, test_year = "يناير", 2024
                print(f"   📅 استخدام شهر افتراضي: {test_month}/{test_year}")
            
            # جلب قسم للاختبار
            cursor.execute("""
                SELECT DISTINCT القسم 
                FROM جدول_البيانات 
                LIMIT 1
            """)
            
            section_data = cursor.fetchone()
            if section_data:
                test_section = section_data[0]
                print(f"   📚 قسم الاختبار: {test_section}")
            else:
                test_section = "قسم / 01"
                print(f"   📚 استخدام قسم افتراضي: {test_section}")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في جلب البيانات: {e}")
            test_month, test_year = "يناير", 2024
            test_section = "قسم / 01"
        
        # اختبار وظيفة get_section_info_from_db
        print(f"\n3️⃣ اختبار get_section_info_from_db:")
        try:
            section_info = get_section_info_from_db('data.db', test_section, test_month, test_year)
            if section_info:
                print("   ✅ تم جلب معلومات القسم بنجاح")
                stats = section_info['student_stats']
                print(f"   📊 إحصائيات: {stats[0]} طالب ({stats[1]} ذكر، {stats[2]} أنثى)")
            else:
                print("   ⚠️ لم يتم جلب معلومات القسم")
        except Exception as e:
            print(f"   ❌ خطأ في get_section_info_from_db: {e}")
        
        # اختبار وظيفة get_monthly_duties_by_section_month
        print(f"\n4️⃣ اختبار get_monthly_duties_by_section_month:")
        try:
            monthly_duties = get_monthly_duties_by_section_month('data.db', test_section, test_month, test_year)
            if monthly_duties:
                print(f"   ✅ تم جلب {len(monthly_duties)} سجل أداءات شهرية")
                if len(monthly_duties) > 0:
                    sample = monthly_duties[0]
                    print(f"   📝 عينة: {sample[0]} - {sample[2]:.2f} درهم")
            else:
                print("   ⚠️ لا توجد أداءات شهرية")
        except Exception as e:
            print(f"   ❌ خطأ في get_monthly_duties_by_section_month: {e}")
        
        # اختبار إنشاء التقرير الكامل
        print(f"\n5️⃣ اختبار إنشاء التقرير الكامل:")
        try:
            print(f"   🔄 إنشاء تقرير لـ {test_section} - {test_month}...")
            
            success, output_path, message = print_section_monthly_report(
                section=test_section,
                month=test_month
            )
            
            if success:
                print("   ✅ تم إنشاء التقرير بنجاح!")
                print(f"   📁 المسار: {output_path}")
                print(f"   💬 الرسالة: {message}")
                
                # فحص الملف
                if output_path and os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"   📊 حجم الملف: {file_size} بايت")
                    
                    if file_size > 5000:  # أكثر من 5KB
                        print("   ✅ الملف يبدو صحيحاً")
                    else:
                        print("   ⚠️ الملف صغير، قد يكون هناك مشكلة")
                else:
                    print("   ⚠️ الملف غير موجود")
            else:
                print(f"   ❌ فشل في إنشاء التقرير: {message}")
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء التقرير: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n🎯 انتهى الاختبار!")
        print("✅ تم إصلاح خطأ current_year")
        print("📊 التقرير الشهري يعمل الآن بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fix_current_year()
