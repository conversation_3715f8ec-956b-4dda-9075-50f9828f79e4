#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إصلاح قاعدة البيانات لإضافة عمود sessions
"""

import sqlite3
import os

def fix_database():
    """إصلاح قاعدة البيانات"""
    try:
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود: data.db")
            return False
        
        print("🔧 بدء إصلاح قاعدة البيانات...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول absence_records
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='absence_records'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("📋 إنشاء جدول absence_records...")
            cursor.execute("""
                CREATE TABLE absence_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    student_name TEXT,
                    student_code TEXT,
                    absence_date TEXT,
                    section TEXT,
                    sessions TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
                )
            """)
            print("✅ تم إنشاء جدول absence_records")
        else:
            print("✅ جدول absence_records موجود")
            
            # التحقق من وجود عمود sessions
            cursor.execute("PRAGMA table_info(absence_records)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'sessions' not in column_names:
                print("🔧 إضافة عمود sessions...")
                cursor.execute("ALTER TABLE absence_records ADD COLUMN sessions TEXT")
                print("✅ تم إضافة عمود sessions")
            else:
                print("✅ عمود sessions موجود")
        
        # إنشاء الفهارس
        print("📊 إنشاء الفهارس...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_date ON absence_records(absence_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_student_id ON absence_records(student_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_section ON absence_records(section)")
        
        conn.commit()
        conn.close()
        
        print("✅ تم إصلاح قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\n🔍 اختبار قاعدة البيانات...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # اختبار جدول البيانات
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
        students_count = cursor.fetchone()[0]
        print(f"📊 عدد التلاميذ: {students_count}")
        
        # اختبار جدول الغياب
        cursor.execute("SELECT COUNT(*) FROM absence_records")
        absence_count = cursor.fetchone()[0]
        print(f"📊 عدد سجلات الغياب: {absence_count}")
        
        # اختبار أعمدة جدول الغياب
        cursor.execute("PRAGMA table_info(absence_records)")
        columns = cursor.fetchall()
        print(f"📋 أعمدة جدول الغياب: {[col[1] for col in columns]}")
        
        conn.close()
        
        print("✅ اختبار قاعدة البيانات نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح قاعدة البيانات...")
    print("=" * 50)
    
    # إصلاح قاعدة البيانات
    fix_ok = fix_database()
    
    if fix_ok:
        # اختبار قاعدة البيانات
        test_ok = test_database()
        
        if test_ok:
            print("\n🎯 تم إصلاح قاعدة البيانات بنجاح!")
            print("\n🚀 يمكنك الآن تشغيل النافذة:")
            print("   python attendance_processing_window.py")
        else:
            print("\n⚠️ فشل في اختبار قاعدة البيانات")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")

if __name__ == "__main__":
    main()
