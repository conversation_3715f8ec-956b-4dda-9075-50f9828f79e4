#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_report_with_archive():
    """اختبار التقرير مع الترحيل التلقائي"""
    print("🧪 اختبار التقرير مع الترحيل التلقائي")
    print("=" * 50)
    
    try:
        # فحص وجود البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # جلب شهر للاختبار
        cursor.execute("""
            SELECT DISTINCT month, year 
            FROM monthly_duties 
            LIMIT 1
        """)
        
        test_data = cursor.fetchone()
        if not test_data:
            print("❌ لا توجد بيانات في monthly_duties للاختبار")
            conn.close()
            return
        
        test_month, test_year = test_data
        test_section = "قسم / 01"
        
        print(f"🎯 اختبار التقرير:")
        print(f"   📅 الشهر: {test_month}/{test_year}")
        print(f"   📚 القسم: {test_section}")
        
        conn.close()
        
        # اختبار وظيفة جلب البيانات
        print(f"\n📊 اختبار جلب البيانات:")
        try:
            from print_section_monthly import get_monthly_duties_by_section_month
            
            # هذا سيقوم بالترحيل التلقائي إذا لم تكن البيانات مرحلة
            report_data = get_monthly_duties_by_section_month('data.db', test_section, test_month, test_year)
            
            if report_data:
                print(f"   ✅ تم جلب {len(report_data)} سجل")
                
                # عرض عينة من البيانات
                print(f"   📝 عينة من البيانات:")
                for i, record in enumerate(report_data[:3], 1):
                    print(f"      {i}. {record[0]} - {record[2]:.2f} درهم - {record[5]}")
                    if len(record) > 8:  # إذا كان هناك عمود اسم الأستاذ
                        print(f"         أستاذ: {record[8] or 'غير محدد'}")
                
                if len(report_data) > 3:
                    print(f"      ... و {len(report_data) - 3} سجل آخر")
                
                # اختبار إنشاء التقرير الكامل
                print(f"\n📋 اختبار إنشاء التقرير الكامل:")
                try:
                    from print_section_monthly import print_section_monthly_report
                    
                    success, output_path, message = print_section_monthly_report(
                        section=test_section,
                        month=test_month
                    )
                    
                    if success:
                        print(f"   ✅ تم إنشاء التقرير بنجاح!")
                        print(f"   📁 المسار: {output_path}")
                        print(f"   💬 الرسالة: {message}")
                        
                        # فحص وجود الملف
                        if os.path.exists(output_path):
                            file_size = os.path.getsize(output_path)
                            print(f"   📊 حجم الملف: {file_size} بايت")
                        else:
                            print(f"   ⚠️ الملف غير موجود في المسار المحدد")
                    else:
                        print(f"   ❌ فشل في إنشاء التقرير: {message}")
                        
                except Exception as report_error:
                    print(f"   ❌ خطأ في إنشاء التقرير: {report_error}")
                
            else:
                print(f"   ⚠️ لا توجد بيانات للقسم {test_section}")
                
        except ImportError as import_error:
            print(f"   ❌ خطأ في الاستيراد: {import_error}")
        except Exception as e:
            print(f"   ❌ خطأ في جلب البيانات: {e}")
        
        # فحص البيانات المرحلة
        print(f"\n🔍 فحص البيانات المرحلة:")
        try:
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # فحص وجود الجدول
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='الحسابات_المرحلة'
            """)
            
            if cursor.fetchone():
                # فحص البيانات المرحلة
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM الحسابات_المرحلة 
                    WHERE month = ? AND year = ?
                """, (test_month, test_year))
                
                archived_count = cursor.fetchone()[0]
                print(f"   📊 عدد السجلات المرحلة لـ {test_month}/{test_year}: {archived_count}")
                
                if archived_count > 0:
                    # فحص الأقسام المرحلة
                    cursor.execute("""
                        SELECT القسم, COUNT(*) 
                        FROM الحسابات_المرحلة 
                        WHERE month = ? AND year = ?
                        GROUP BY القسم
                        ORDER BY القسم
                    """, (test_month, test_year))
                    
                    sections = cursor.fetchall()
                    print(f"   📋 الأقسام المرحلة:")
                    for section, count in sections:
                        print(f"      - {section}: {count} طالب")
                
            else:
                print(f"   ⚠️ جدول الحسابات المرحلة غير موجود")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص البيانات المرحلة: {e}")
        
        print(f"\n🎯 انتهى الاختبار!")
        print("💡 إذا نجح الاختبار، فالتقرير يعتمد الآن على البيانات المرحلة")
        print("🔄 الترحيل يتم تلقائياً عند إنشاء التقرير")
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")

if __name__ == "__main__":
    test_report_with_archive()
