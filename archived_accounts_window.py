#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
from archived_accounts_manager import ArchivedAccountsManager

class ArchivedAccountsWindow(QMainWindow):
    """نافذة إدارة الحسابات المرحلة"""
    
    def __init__(self):
        super().__init__()
        self.manager = ArchivedAccountsManager()
        self.init_ui()
        self.load_archived_months()
    
    def init_ui(self):
        """إعداد واجهة المستخدم المحسنة مع تخطيط صحيح"""
        self.setWindowTitle("🏦 إدارة الحسابات المرحلة - نظام احترافي")
        self.setGeometry(100, 30, 1200, 700)
        self.setWindowIcon(QIcon("icon.png"))

        # تطبيق اتجاه RTL للنافذة
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق ثيم احترافي مع خط Calibri
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QWidget {
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QGroupBox {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: white;
                margin-bottom: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 8px 0 8px;
                background-color: white;
                font-family: 'Calibri', 'Arial', 'Tahoma';
               
                font-weight: bold;
            }
            QPushButton {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
                min-height: 30px;
            }
            QComboBox, QSpinBox, QLineEdit {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                padding: 6px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus, QSpinBox:focus, QLineEdit:focus {
                border-color: #3498db;
            }
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QCheckBox {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                
                font-weight: bold;
                color: #e74c3c;
            }
            QLabel {
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
        """)

        # إعداد النافذة بدون تخطيط رئيسي معقد
        self.setLayoutDirection(Qt.RightToLeft)

        # العنوان الرئيسي مباشرة
        self.title_label = QLabel("🏦 إدارة الحسابات المرحلة", self)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                padding: 18px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
            }
        """)
        self.title_label.setGeometry(15, 15, 1170, 60)

        # شريط المعلومات السريعة مباشرة
        self.create_info_bar_direct()

        # منطقة العمليات مباشرة
        self.create_operations_area_direct()

        # منطقة الجدول مباشرة
        self.create_table_area_direct()

    def create_info_bar_direct(self):
        """إنشاء شريط المعلومات مباشرة"""
        self.info_widget = QWidget(self)
        self.info_widget.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #d5dbdb;
            }
        """)
        self.info_widget.setGeometry(15, 85, 1170, 50)

        # معلومات النظام
        self.system_info = QLabel("📊 نظام الحسابات المرحلة نشط", self.info_widget)
        self.system_info.setFont(QFont("Calibri", 16, QFont.Bold))
        self.system_info.setStyleSheet("color: #27ae60;")
        self.system_info.setGeometry(15, 3, 250, 40)

        # معلومات قاعدة البيانات
        self.db_info = QLabel("🗄️ جاري تحميل...", self.info_widget)
        self.db_info.setFont(QFont("Calibri", 13, QFont.Bold))
        self.db_info.setStyleSheet("color: #7f8c8d;")
        self.db_info.setGeometry(400, 3, 300, 40)

        # معلومات آخر ترحيل
        self.last_archive_info = QLabel("🕒 جاري التحقق...", self.info_widget)
        self.last_archive_info.setFont(QFont("Calibri", 13, QFont.Bold))
        self.last_archive_info.setStyleSheet("color: #7f8c8d;")
        self.last_archive_info.setGeometry(800, 3, 350, 40)

        # تحديث المعلومات
        self.update_info_bar()

    def create_operations_area_direct(self):
        """إنشاء منطقة العمليات مباشرة"""
        # قسم الترحيل
        archive_group = QGroupBox("", self)
        archive_group.setGeometry(15, 140, 580, 220)

        # اختيار الشهر
        month_label = QLabel("الشهر:", archive_group)
        month_label.setFont(QFont("Calibri", 16, QFont.Bold))
        month_label.setGeometry(20, 40, 60, 25)

        self.month_combo = QComboBox(archive_group)
        self.month_combo.addItems([
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ])
        self.month_combo.setCurrentText(self.get_current_month())
        self.month_combo.setGeometry(90, 40, 100, 25)

        # اختيار السنة
        year_label = QLabel("السنة:", archive_group)
        year_label.setFont(QFont("Calibri", 16, QFont.Bold))
        year_label.setGeometry(210, 40, 50, 25)

        self.year_spin = QSpinBox(archive_group)
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(datetime.now().year)
        self.year_spin.setGeometry(270, 40, 80, 25)

        # خيار إعادة الترحيل
        self.force_update_check = QCheckBox("إعادة ترحيل (حذف الموجود)", archive_group)
        self.force_update_check.setStyleSheet("color: #e74c3c; font-weight: bold;")
        self.force_update_check.setGeometry(20, 80, 200, 25)

        # زر الترحيل
        archive_btn = QPushButton("🔄 ترحيل الشهر", archive_group)
        archive_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
        """)
        archive_btn.setGeometry(20, 120, 150, 40)
        archive_btn.clicked.connect(self.archive_month)

        # زر ترحيل السنة كاملة
        archive_year_btn = QPushButton("", archive_group)
        archive_year_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
            QPushButton:pressed {
                background-color: #2980b9;
            }
        """)
        archive_year_btn.setGeometry(190, 120, 180, 40)
        archive_year_btn.clicked.connect(self.archive_full_year)

        # قسم الإحصائيات السريعة
        stats_group = QGroupBox("", self)
        stats_group.setGeometry(605, 140, 580, 220)
        self.create_stats_panel_direct(stats_group)
        
    def create_table_area_direct(self):
        """إنشاء منطقة الجدول مباشرة"""
        # جدول الشهور المرحلة
        table_group = QGroupBox("  ", self)
        table_group.setGeometry(15, 350, 1170, 300)

        # أزرار الإدارة مباشرة
        refresh_btn = QPushButton("🔄 تحديث", table_group)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.setGeometry(20, 35, 80, 30)
        refresh_btn.clicked.connect(self.load_archived_months)

        delete_btn = QPushButton("🗑️ حذف المحدد", table_group)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.setGeometry(110, 35, 100, 30)
        delete_btn.clicked.connect(self.delete_selected_month)

        # زر تصدير البيانات
        export_btn = QPushButton("📤 تصدير", table_group)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_btn.setGeometry(220, 35, 100, 30)
        export_btn.clicked.connect(self.export_archived_data)

        # زر تنظيف البيانات القديمة
        cleanup_btn = QPushButton("🧹 تنظيف", table_group)
        cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8590c;
            }
        """)
        cleanup_btn.setGeometry(310, 35, 80, 30)
        cleanup_btn.clicked.connect(self.cleanup_old_data)

        # مربع البحث مباشر
        search_label = QLabel("🔍 بحث:", table_group)
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_label.setGeometry(900, 35, 60, 30)

        self.search_box = QLineEdit(table_group)
        self.search_box.setPlaceholderText("ابحث في الشهور المرحلة...")
        self.search_box.setGeometry(970, 35, 180, 30)
        self.search_box.textChanged.connect(self.filter_table)
        
        # الجدول مباشرة
        self.table = QTableWidget(table_group)
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "الشهر", "السنة", "عدد السجلات", "إجمالي المطلوب", "إجمالي المحصل",
            "نسبة التحصيل", "تاريخ الترحيل", "حالة البيانات"
        ])
        self.table.setGeometry(20, 75, 1130, 330)

        # تنسيق الجدول المحسن
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setSortingEnabled(True)

        # تعيين عرض الأعمدة محسن
        self.table.setColumnWidth(0, 90)   # الشهر
        self.table.setColumnWidth(1, 70)   # السنة
        self.table.setColumnWidth(2, 100)  # عدد السجلات
        self.table.setColumnWidth(3, 120)  # إجمالي المطلوب
        self.table.setColumnWidth(4, 120)  # إجمالي المحصل
        self.table.setColumnWidth(5, 100)  # نسبة التحصيل
        self.table.setColumnWidth(6, 120)  # تاريخ الترحيل
        self.table.setColumnWidth(7, 100)  # حالة البيانات

        # إضافة قائمة سياق للجدول
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز - نظام الحسابات المرحلة")

        # تحميل البيانات
        self.load_archived_months()

    def create_stats_panel_direct(self, stats_group):
        """إنشاء لوحة الإحصائيات مباشرة"""
        # إحصائيات عامة مباشرة
        self.total_archived_label = QLabel("📊 إجمالي السجلات: جاري التحميل...", stats_group)
        self.total_archived_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_archived_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #e9ecef;
            }
        """)
        self.total_archived_label.setGeometry(20, 40, 540, 35)

        self.total_months_label = QLabel("📅 عدد الشهور: جاري التحميل...", stats_group)
        self.total_months_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_months_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #e9ecef;
            }
        """)
        self.total_months_label.setGeometry(20, 80, 540, 35)

        self.total_amount_label = QLabel("💰 إجمالي المبالغ: جاري التحميل...", stats_group)
        self.total_amount_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_amount_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #e9ecef;
            }
        """)
        self.total_amount_label.setGeometry(20, 120, 540, 35)

        self.db_size_label = QLabel("💾 حجم قاعدة البيانات: جاري التحميل...", stats_group)
        self.db_size_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.db_size_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 4px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #e9ecef;
            }
        """)
        self.db_size_label.setGeometry(20, 160, 540, 35)

        # تحديث الإحصائيات
        self.update_stats_panel()
    
    def get_current_month(self):
        """جلب الشهر الحالي بالعربية"""
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        return months[datetime.now().month - 1]
    
    def archive_month(self):
        """ترحيل شهر جديد"""
        month = self.month_combo.currentText()
        year = self.year_spin.value()
        force_update = self.force_update_check.isChecked()
        
        # تأكيد العملية
        if force_update:
            reply = QMessageBox.question(
                self, "تأكيد إعادة الترحيل",
                f"هل أنت متأكد من إعادة ترحيل {month}/{year}؟\n"
                "سيتم حذف البيانات المرحلة السابقة!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return
        
        # تنفيذ الترحيل
        self.status_bar.showMessage(f"جاري ترحيل {month}/{year}...")
        QApplication.processEvents()
        
        try:
            success, message = self.manager.archive_monthly_accounts(month, year, force_update)
            
            if success:
                QMessageBox.information(self, "نجح الترحيل", f"✅ {message}")
                self.load_archived_months()  # تحديث الجدول
                self.status_bar.showMessage(f"تم ترحيل {month}/{year} بنجاح")
            else:
                QMessageBox.warning(self, "فشل الترحيل", f"❌ {message}")
                self.status_bar.showMessage("فشل في الترحيل")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الترحيل:\n{str(e)}")
            self.status_bar.showMessage("خطأ في الترحيل")
    
    def load_archived_months(self):
        """تحميل الشهور المرحلة في الجدول المحسن"""
        try:
            # جلب البيانات المحسنة
            import sqlite3
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # استعلام محسن للحصول على معلومات أكثر
            cursor.execute("""
                SELECT
                    month,
                    year,
                    COUNT(*) as record_count,
                    SUM(amount_required) as total_required,
                    SUM(amount_paid) as total_paid,
                    MIN(ترحيل_date) as first_archive,
                    MAX(ترحيل_date) as last_archive
                FROM الحسابات_المرحلة
                GROUP BY month, year
                ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1
                        WHEN 'فبراير' THEN 2
                        WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4
                        WHEN 'مايو' THEN 5
                        WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7
                        WHEN 'أغسطس' THEN 8
                        WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10
                        WHEN 'نوفمبر' THEN 11
                        WHEN 'ديسمبر' THEN 12
                        ELSE 13
                    END DESC
            """)

            archived_months = cursor.fetchall()
            conn.close()

            self.table.setRowCount(len(archived_months))

            for row, month_data in enumerate(archived_months):
                month, year, record_count, total_required, total_paid, first_archive, last_archive = month_data

                # حساب نسبة التحصيل
                collection_rate = (total_paid / total_required * 100) if total_required and total_required > 0 else 0

                # الشهر
                month_item = QTableWidgetItem(month)
                month_item.setTextAlignment(Qt.AlignCenter)
                month_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 0, month_item)

                # السنة
                year_item = QTableWidgetItem(str(year))
                year_item.setTextAlignment(Qt.AlignCenter)
                year_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 1, year_item)

                # عدد السجلات
                count_item = QTableWidgetItem(f"{record_count:,}")
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setBackground(QColor("#e8f5e8"))
                self.table.setItem(row, 2, count_item)

                # إجمالي المطلوب
                required_item = QTableWidgetItem(f"{total_required:,.2f}" if total_required else "0.00")
                required_item.setTextAlignment(Qt.AlignCenter)
                required_item.setBackground(QColor("#fff3cd"))
                self.table.setItem(row, 3, required_item)

                # إجمالي المحصل
                paid_item = QTableWidgetItem(f"{total_paid:,.2f}" if total_paid else "0.00")
                paid_item.setTextAlignment(Qt.AlignCenter)
                paid_item.setBackground(QColor("#d4edda"))
                self.table.setItem(row, 4, paid_item)

                # نسبة التحصيل
                rate_item = QTableWidgetItem(f"{collection_rate:.1f}%")
                rate_item.setTextAlignment(Qt.AlignCenter)

                # تلوين حسب نسبة التحصيل
                if collection_rate >= 90:
                    rate_item.setBackground(QColor("#d4edda"))  # أخضر
                elif collection_rate >= 70:
                    rate_item.setBackground(QColor("#fff3cd"))  # أصفر
                else:
                    rate_item.setBackground(QColor("#f8d7da"))  # أحمر

                self.table.setItem(row, 5, rate_item)

                # تاريخ الترحيل
                first_date = first_archive.split()[0] if first_archive else "غير محدد"
                first_item = QTableWidgetItem(first_date)
                first_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 6, first_item)

                # حالة البيانات
                status = "مكتمل" if record_count > 0 else "فارغ"
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)

                if status == "مكتمل":
                    status_item.setBackground(QColor("#d4edda"))
                    status_item.setForeground(QColor("#155724"))
                else:
                    status_item.setBackground(QColor("#f8d7da"))
                    status_item.setForeground(QColor("#721c24"))

                self.table.setItem(row, 7, status_item)

            self.status_bar.showMessage(f"تم تحميل {len(archived_months)} شهر مرحل")

            # تحديث المعلومات والإحصائيات
            self.update_info_bar()
            self.update_stats_panel()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
            # في حالة عدم وجود الجدول، عرض جدول فارغ
            self.table.setRowCount(0)
            self.status_bar.showMessage("لا توجد بيانات مرحلة")
    
    def delete_selected_month(self):
        """حذف الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شهر للحذف")
            return
        
        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())
        record_count = self.table.item(current_row, 2).text()
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف ترحيل {month}/{year}؟\n"
            f"سيتم حذف {record_count} سجل نهائياً!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success, message = self.manager.delete_archived_month(month, year)
                
                if success:
                    QMessageBox.information(self, "تم الحذف", f"✅ {message}")
                    self.load_archived_months()  # تحديث الجدول
                    self.status_bar.showMessage(f"تم حذف {month}/{year}")
                else:
                    QMessageBox.warning(self, "فشل الحذف", f"❌ {message}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في الحذف:\n{str(e)}")

    def create_info_bar(self, main_layout):
        """إنشاء شريط المعلومات السريعة المحسن"""
        info_widget = QWidget()
        info_widget.setMaximumHeight(50)
        info_widget.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border-radius: 8px;
                padding: 8px;
                border: 1px solid #d5dbdb;
            }
        """)
        info_layout = QHBoxLayout(info_widget)
        info_layout.setSpacing(5)
        info_layout.setContentsMargins(15, 8, 15, 8)

        # معلومات النظام
        system_info = QLabel("📊 نظام الحسابات المرحلة نشط")
        system_info.setFont(QFont("Calibri", 15, QFont.Bold))
        system_info.setStyleSheet("""
            font-weight: bold;
            color: #27ae60;
        """)

        # معلومات قاعدة البيانات
        self.db_info = QLabel("🗄️ جاري تحميل...")
        self.db_info.setFont(QFont("Calibri", 13, QFont.Bold))
        self.db_info.setStyleSheet("""
            color: #7f8c8d;
        """)

        # معلومات آخر ترحيل
        self.last_archive_info = QLabel("🕒 جاري التحقق...")
        self.last_archive_info.setFont(QFont("Calibri", 13, QFont.Bold))
        self.last_archive_info.setStyleSheet("""
            color: #7f8c8d;
        """)

        info_layout.addWidget(system_info)
        info_layout.addStretch()
        info_layout.addWidget(self.db_info)
        info_layout.addStretch()
        info_layout.addWidget(self.last_archive_info)

        main_layout.addWidget(info_widget)

        # تحديث المعلومات
        self.update_info_bar()

    def create_stats_panel(self, stats_group):
        """إنشاء لوحة الإحصائيات المحسنة"""
        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setSpacing(5)
        stats_layout.setContentsMargins(15, 20, 15, 15)

        # إحصائيات عامة محسنة
        self.total_archived_label = QLabel("📊 إجمالي السجلات: جاري التحميل...")
        self.total_months_label = QLabel("📅 عدد الشهور: جاري التحميل...")
        self.total_amount_label = QLabel("💰 إجمالي المبالغ: جاري التحميل...")
        self.db_size_label = QLabel("💾 حجم قاعدة البيانات: جاري التحميل...")

        # تنسيق التسميات مع خط Calibri
        for label in [self.total_archived_label, self.total_months_label,
                     self.total_amount_label, self.db_size_label]:
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    padding: 8px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    margin: 1px;
                    border: 1px solid #e9ecef;
                }
            """)
            label.setWordWrap(True)

        stats_layout.addWidget(self.total_archived_label)
        stats_layout.addWidget(self.total_months_label)
        stats_layout.addWidget(self.total_amount_label)
        stats_layout.addWidget(self.db_size_label)
        stats_layout.addStretch()

        # تحديث الإحصائيات
        self.update_stats_panel()

    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        try:
            # معلومات قاعدة البيانات
            if os.path.exists('data.db'):
                db_size = os.path.getsize('data.db')
                db_size_mb = db_size / (1024 * 1024)
                self.db_info.setText(f"🗄️ قاعدة البيانات: {db_size_mb:.2f} MB")
            else:
                self.db_info.setText("🗄️ قاعدة البيانات: غير موجودة")

            # آخر ترحيل
            archived_months = self.manager.get_archived_months()
            if archived_months:
                last_month = archived_months[0]
                self.last_archive_info.setText(f"🕒 آخر ترحيل: {last_month[0]}/{last_month[1]}")
            else:
                self.last_archive_info.setText("🕒 آخر ترحيل: لا يوجد")

        except Exception as e:
            print(f"خطأ في تحديث شريط المعلومات: {e}")

    def update_stats_panel(self):
        """تحديث لوحة الإحصائيات"""
        try:
            import sqlite3
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # إجمالي السجلات المرحلة
            try:
                cursor.execute("SELECT COUNT(*) FROM الحسابات_المرحلة")
                total_records = cursor.fetchone()[0]
                self.total_archived_label.setText(f"📊 إجمالي السجلات المرحلة: {total_records:,}")
            except:
                self.total_archived_label.setText("📊 إجمالي السجلات المرحلة: 0")

            # عدد الشهور المرحلة
            try:
                cursor.execute("SELECT COUNT(DISTINCT month || '/' || year) FROM الحسابات_المرحلة")
                total_months = cursor.fetchone()[0]
                self.total_months_label.setText(f"📅 عدد الشهور المرحلة: {total_months}")
            except:
                self.total_months_label.setText("📅 عدد الشهور المرحلة: 0")

            # إجمالي المبالغ المرحلة
            try:
                cursor.execute("SELECT SUM(amount_paid) FROM الحسابات_المرحلة")
                total_amount = cursor.fetchone()[0] or 0
                self.total_amount_label.setText(f"💰 إجمالي المبالغ المرحلة: {total_amount:,.2f} درهم")
            except:
                self.total_amount_label.setText("💰 إجمالي المبالغ المرحلة: 0.00 درهم")

            # حجم قاعدة البيانات
            if os.path.exists('data.db'):
                db_size = os.path.getsize('data.db')
                db_size_mb = db_size / (1024 * 1024)
                self.db_size_label.setText(f"💾 حجم قاعدة البيانات: {db_size_mb:.2f} MB")
            else:
                self.db_size_label.setText("💾 حجم قاعدة البيانات: غير متاح")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def archive_full_year(self):
        """ترحيل السنة كاملة"""
        year = self.year_spin.value()
        force_update = self.force_update_check.isChecked()

        # تأكيد العملية
        reply = QMessageBox.question(
            self, "تأكيد ترحيل السنة",
            f"هل أنت متأكد من ترحيل جميع شهور السنة {year}؟\n"
            f"هذه العملية قد تستغرق وقتاً طويلاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # تنفيذ الترحيل
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]

        success_count = 0
        total_months = len(months)

        # شريط التقدم
        progress = QProgressDialog(f"جاري ترحيل السنة {year}...", "إلغاء", 0, total_months, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()

        for i, month in enumerate(months):
            if progress.wasCanceled():
                break

            progress.setLabelText(f"جاري ترحيل {month} {year}...")
            progress.setValue(i)
            QApplication.processEvents()

            try:
                success, message = self.manager.archive_monthly_accounts(month, year, force_update)
                if success:
                    success_count += 1
            except Exception as e:
                print(f"خطأ في ترحيل {month}: {e}")

        progress.setValue(total_months)
        progress.close()

        # عرض النتيجة
        QMessageBox.information(
            self, "انتهى ترحيل السنة",
            f"تم ترحيل {success_count} من {total_months} شهر بنجاح للسنة {year}"
        )

        self.load_archived_months()
        self.update_stats_panel()

    def filter_table(self):
        """تصفية الجدول حسب النص المدخل"""
        search_text = self.search_box.text().lower()

        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # إجراءات القائمة
        view_details_action = menu.addAction("👁️ عرض التفاصيل")
        menu.addSeparator()
        re_archive_action = menu.addAction("🔄 إعادة ترحيل")
        export_month_action = menu.addAction("📤 تصدير هذا الشهر")
        menu.addSeparator()
        delete_action = menu.addAction("🗑️ حذف")

        # تنفيذ الإجراء المحدد
        action = menu.exec_(self.table.mapToGlobal(position))

        if action == view_details_action:
            self.view_month_details()
        elif action == re_archive_action:
            self.re_archive_selected_month()
        elif action == export_month_action:
            self.export_selected_month()
        elif action == delete_action:
            self.delete_selected_month()

    def view_month_details(self):
        """عرض تفاصيل الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())

        # نافذة تفاصيل الشهر
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle(f"تفاصيل {month}/{year}")
        details_dialog.setGeometry(200, 200, 800, 600)

        layout = QVBoxLayout(details_dialog)

        # معلومات الشهر
        info_label = QLabel(f"📊 تفاصيل الشهر: {month}/{year}")
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; padding: 10px;")
        layout.addWidget(info_label)

        # جدول التفاصيل
        details_table = QTableWidget()
        # هنا يمكن إضافة تفاصيل أكثر من قاعدة البيانات
        layout.addWidget(details_table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(details_dialog.close)
        layout.addWidget(close_btn)

        details_dialog.exec_()

    def re_archive_selected_month(self):
        """إعادة ترحيل الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())

        # تأكيد إعادة الترحيل
        reply = QMessageBox.question(
            self, "تأكيد إعادة الترحيل",
            f"هل أنت متأكد من إعادة ترحيل {month}/{year}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success, message = self.manager.archive_monthly_accounts(month, year, force_update=True)

                if success:
                    QMessageBox.information(self, "نجح إعادة الترحيل", f"✅ {message}")
                    self.load_archived_months()
                    self.update_stats_panel()
                else:
                    QMessageBox.warning(self, "فشل إعادة الترحيل", f"❌ {message}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في إعادة الترحيل:\n{str(e)}")

    def export_selected_month(self):
        """تصدير الشهر المحدد"""
        QMessageBox.information(self, "قريباً", "ميزة التصدير ستكون متاحة قريباً")

    def export_archived_data(self):
        """تصدير جميع البيانات المرحلة"""
        QMessageBox.information(self, "قريباً", "ميزة التصدير ستكون متاحة قريباً")

    def cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        QMessageBox.information(self, "قريباً", "ميزة التنظيف ستكون متاحة قريباً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    window = ArchivedAccountsWindow()
    window.show()

    sys.exit(app.exec_())
