#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox, QPushButton,
    QGroupBox, QFormLayout, QSpinBox, QLineEdit, QRadioButton, QScrollArea,
    QFrame, QMessageBox, QApplication, QWidget, QStyle
)
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt
import sqlite3

class RoomAssignmentDialog(QDialog):
    """نافذة توزيع المترشحين على القاعات"""

    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        self.parent = parent
        self.db_path = db_path
        self.available_levels = []
        self.level_checkboxes = []
        self.room_assignments = {}
        self.total_students = 0

        # متغيرات لتتبع حالة المراحل
        self.levels_selected = False
        self.rooms_configured = False
        self.calculation_done = False

        # استيراد الإعدادات من النافذة الأم إذا كانت متوفرة
        if hasattr(parent, 'max_students_spin'):
            self.max_students = parent.max_students_spin.value()
        else:
            self.max_students = 20

        if hasattr(parent, 'room_name_input'):
            self.room_name = parent.room_name_input.text() or "قاعة"
        else:
            self.room_name = "قاعة"

        if hasattr(parent, 'room_number_spin'):
            self.room_number = parent.room_number_spin.value()
        else:
            self.room_number = 1

        self.initUI()
        self.load_levels()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("توزيع المترشحين على القاعات")
        self.setFixedSize(1200, 700)
        self.setWindowIcon(QIcon("01.ico"))

        # تعيين نمط CSS للنافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                font-family: Calibri;
                font-size: 13pt;
            }
            QLabel[title="true"] {
                font-family: Calibri;
                font-size: 14pt;
                font-weight: bold;
                color: #4a86e8;
                padding: 5px;
                background-color: #e8f0fe;
                border-radius: 5px;
                margin: 0;
                line-height: 30px;
            }
            QPushButton {
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                opacity: 0.9;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QSpinBox {
                font-family: Calibri;
                font-size: 13pt;
                padding: 8px;
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                min-height: 30px;
            }
            QRadioButton, QCheckBox {
                font-family: Calibri;
                font-size: 13pt;
                padding: 5px;
            }
            QGroupBox {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 20px;
                padding-bottom: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: #4a86e8;
                color: white;
                border-radius: 3px;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إنشاء العمود الأيمن (عمود الإعدادات) - 40% من العرض
        settings_column = QVBoxLayout()
        settings_column.setContentsMargins(5, 5, 5, 5)
        settings_column.setSpacing(10)

        # إنشاء العمود الأيسر (عمود تفاصيل الحساب) - 60% من العرض
        details_column = QVBoxLayout()
        details_column.setContentsMargins(5, 5, 5, 5)
        details_column.setSpacing(10)

        # إنشاء ويدجت للعمودين
        settings_widget = QWidget()
        settings_widget.setLayout(settings_column)
        settings_widget.setFixedWidth(int(self.width() * 0.4))

        details_widget = QWidget()
        details_widget.setLayout(details_column)

        # إضافة العمودين إلى التخطيط الرئيسي
        main_layout.addWidget(settings_widget)
        main_layout.addWidget(details_widget)

        # ===== العمود الأيمن (عمود الإعدادات) =====

        # 1. بطاقة تحديد المستويات
        levels_card = QGroupBox("تحديد المستويات")
        levels_card.setFixedSize(350, 250)
        levels_layout = QVBoxLayout(levels_card)
        levels_layout.setContentsMargins(5, 5, 5, 5)
        levels_layout.setSpacing(5)

        # إضافة زر تحديد الكل
        self.select_all_button = QPushButton("تحديد الكل")
        self.select_all_button.setStyleSheet("""
            background-color: #4a86e8;
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.select_all_button.setFixedHeight(30)
        self.select_all_button.clicked.connect(self.toggle_all_levels)
        levels_layout.addWidget(self.select_all_button)

        # إنشاء منطقة تمرير للمستويات
        levels_scroll_area = QScrollArea()
        levels_scroll_area.setWidgetResizable(True)
        levels_scroll_content = QWidget()
        self.levels_grid = QVBoxLayout(levels_scroll_content)
        self.levels_grid.setContentsMargins(5, 5, 5, 5)
        self.levels_grid.setSpacing(2)
        levels_scroll_area.setWidget(levels_scroll_content)
        levels_layout.addWidget(levels_scroll_area)

        # 2. بطاقة إعداد القاعات
        rooms_card = QGroupBox("إعداد القاعات")
        rooms_card.setFixedSize(350, 200)
        rooms_layout = QVBoxLayout(rooms_card)
        rooms_layout.setContentsMargins(5, 5, 5, 5)
        rooms_layout.setSpacing(5)

        # إضافة حقول إعداد القاعات
        # صف 1: أقصى عدد التلاميذ
        max_students_layout = QHBoxLayout()
        max_students_label = QLabel("أقصى عدد التلاميذ:")
        max_students_label.setStyleSheet("font-weight: bold; color: blue;")
        self.max_students_spin = QSpinBox()
        self.max_students_spin.setMinimum(1)
        self.max_students_spin.setMaximum(1000)
        self.max_students_spin.setValue(self.max_students)
        self.max_students_spin.setFixedHeight(30)
        max_students_layout.addWidget(max_students_label)
        max_students_layout.addWidget(self.max_students_spin)
        rooms_layout.addLayout(max_students_layout)

        # صف 2: اسم القاعة
        room_name_layout = QHBoxLayout()
        room_name_label = QLabel("اسم القاعة:")
        room_name_label.setStyleSheet("font-weight: bold; color: blue;")
        self.room_name_input = QLineEdit()
        self.room_name_input.setText(self.room_name)
        self.room_name_input.setFixedHeight(30)
        room_name_layout.addWidget(room_name_label)
        room_name_layout.addWidget(self.room_name_input)
        rooms_layout.addLayout(room_name_layout)

        # صف 3: بداية الترقيم
        room_number_layout = QHBoxLayout()
        room_number_label = QLabel("بداية الترقيم:")
        room_number_label.setStyleSheet("font-weight: bold; color: blue;")
        self.room_number_spin = QSpinBox()
        self.room_number_spin.setMinimum(1)
        self.room_number_spin.setMaximum(1000)
        self.room_number_spin.setValue(self.room_number)
        self.room_number_spin.setFixedHeight(30)
        room_number_layout.addWidget(room_number_label)
        room_number_layout.addWidget(self.room_number_spin)
        rooms_layout.addLayout(room_number_layout)

        # 3. بطاقة طريقة توزيع المترشحين
        distribution_card = QGroupBox("طريقة توزيع المترشحين")
        distribution_card.setFixedSize(350, 200)
        distribution_layout = QVBoxLayout(distribution_card)
        distribution_layout.setContentsMargins(5, 5, 5, 5)
        distribution_layout.setSpacing(5)

        # إضافة خيارات التوزيع
        self.option1_radio = QRadioButton("ملء القاعات إلى سعتها القصوى")
        self.option1_radio.setChecked(True)
        self.option1_radio.setFixedHeight(50)
        self.option1_radio.setStyleSheet("font-weight: bold;")
        distribution_layout.addWidget(self.option1_radio)

        self.option2_radio = QRadioButton("توزيع المترشحين بالتساوي على القاعات")
        self.option2_radio.setFixedHeight(50)
        self.option2_radio.setStyleSheet("font-weight: bold;")
        distribution_layout.addWidget(self.option2_radio)

        # إضافة زر الحساب
        self.calculate_button = QPushButton("احسب")
        self.calculate_button.setStyleSheet("""
            background-color: #4a86e8;
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.calculate_button.setFixedHeight(40)
        self.calculate_button.clicked.connect(self.check_and_calculate)
        distribution_layout.addWidget(self.calculate_button)

        # إضافة البطاقات إلى عمود الإعدادات
        settings_column.addWidget(levels_card)
        settings_column.addWidget(rooms_card)
        settings_column.addWidget(distribution_card)
        settings_column.addStretch(1)

        # ===== العمود الأيسر (عمود تفاصيل الحساب) =====

        # 1. بطاقة المعلومات
        info_card = QGroupBox("معلومات المستويات والمترشحين")
        info_card.setFixedSize(600, 300)
        info_layout = QVBoxLayout(info_card)
        info_layout.setContentsMargins(5, 5, 5, 5)

        # إضافة عنصر نصي للمعلومات
        self.info_text = QLabel("اختر المستويات وانقر على زر 'احسب' لعرض المعلومات")
        self.info_text.setWordWrap(True)
        self.info_text.setAlignment(Qt.AlignRight | Qt.AlignTop)
        self.info_text.setStyleSheet("""
            padding: 5px;
            background-color: white;
            border: 1px solid #cccccc;
            border-radius: 5px;
            font-family: Calibri;
            font-size: 13pt;
            font-weight: bold;
            color: black;
        """)

        # إضافة منطقة تمرير للمعلومات
        info_scroll = QScrollArea()
        info_scroll.setWidgetResizable(True)
        info_scroll.setWidget(self.info_text)
        info_scroll.setStyleSheet("border: none; background-color: transparent;")
        info_layout.addWidget(info_scroll)

        # 2. بطاقة التفاصيل
        details_card = QGroupBox("تفاصيل توزيع القاعات")
        details_card.setFixedSize(600, 300)
        details_layout = QVBoxLayout(details_card)
        details_layout.setContentsMargins(5, 5, 5, 5)

        # إضافة عنصر نصي للتفاصيل
        self.details_text = QLabel("اختر المستويات وانقر على زر 'احسب' لعرض التفاصيل")
        self.details_text.setWordWrap(True)
        self.details_text.setAlignment(Qt.AlignRight | Qt.AlignTop)
        self.details_text.setStyleSheet("""
            padding: 5px;
            background-color: white;
            border: 1px solid #cccccc;
            border-radius: 5px;
            font-family: Calibri;
            font-size: 13pt;
            font-weight: bold;
            color: black;
        """)

        # إضافة منطقة تمرير للتفاصيل
        details_scroll = QScrollArea()
        details_scroll.setWidgetResizable(True)
        details_scroll.setWidget(self.details_text)
        details_scroll.setStyleSheet("border: none; background-color: transparent;")
        details_layout.addWidget(details_scroll)

        # إضافة أزرار التنفيذ والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(5, 5, 5, 5)
        buttons_layout.setSpacing(10)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            background-color: #e06666;
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.clicked.connect(self.reject)

        self.execute_button = QPushButton("تنفيذ")
        self.execute_button.setStyleSheet("""
            background-color: #6aa84f;
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.execute_button.setFixedHeight(40)
        self.execute_button.setEnabled(False)
        self.execute_button.clicked.connect(self.check_and_execute)

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.execute_button)

        # إضافة البطاقات والأزرار إلى عمود التفاصيل
        details_column.addWidget(info_card)
        details_column.addWidget(details_card)
        details_column.addLayout(buttons_layout)
        details_column.addStretch(1)

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.setLayoutDirection(Qt.RightToLeft)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            self.show_custom_message("خطأ", f"فشل الاتصال بقاعدة البيانات: {e}", "error")
            return None

    def load_levels(self):
        """تحميل المستويات المتاحة من قاعدة البيانات"""
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # التحقق من وجود أرقام امتحان
            cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE رقم_الامتحان IS NOT NULL AND رقم_الامتحان != ''")
            exam_numbers_count = cursor.fetchone()[0]

            if exam_numbers_count == 0:
                self.show_custom_message("تنبيه", "يجب إضافة أرقام الامتحان أولاً قبل ترقيم القاعات.", "warning")
                conn.close()
                self.reject()
                return

            # الحصول على المستويات المتاحة
            cursor.execute("SELECT DISTINCT المستوى FROM امتحانات WHERE المستوى IS NOT NULL AND المستوى != '' ORDER BY المستوى")
            self.available_levels = [row[0] for row in cursor.fetchall()]

            if not self.available_levels:
                self.show_custom_message("تنبيه", "لا توجد مستويات متاحة في قاعدة البيانات.", "warning")
                conn.close()
                self.reject()
                return

            # إنشاء بطاقات المستويات باستخدام QWidget مع QCheckBox بداخله
            for level in self.available_levels:
                # إنشاء بطاقة المستوى
                level_card = QWidget()
                level_card.setFixedSize(300, 40)
                level_card.setStyleSheet("""
                    background-color: #e8f0fe;
                    border: 1px solid #4a86e8;
                    border-radius: 5px;
                """)

                # إنشاء تخطيط أفقي للبطاقة
                card_layout = QHBoxLayout(level_card)
                card_layout.setContentsMargins(10, 0, 10, 0)
                card_layout.setSpacing(5)

                # إنشاء مربع الاختيار
                checkbox = QCheckBox(level)
                checkbox.setFont(QFont("Calibri", 14, QFont.Bold))
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: blue;
                        background-color: transparent;
                        border: none;
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                    }
                """)

                # إضافة مربع الاختيار إلى البطاقة
                card_layout.addWidget(checkbox)

                # إضافة البطاقة إلى القائمة
                self.levels_grid.addWidget(level_card)
                self.level_checkboxes.append(checkbox)

            conn.close()

        except Exception as e:
            self.show_custom_message("خطأ", f"فشل في تحميل المستويات: {e}", "error")
            if conn:
                conn.close()

    def toggle_all_levels(self):
        """تحديد أو إلغاء تحديد جميع المستويات"""
        # التحقق من حالة أول مربع اختيار
        if self.level_checkboxes and not self.level_checkboxes[0].isChecked():
            # تحديد الكل
            for checkbox in self.level_checkboxes:
                checkbox.setChecked(True)
            self.select_all_button.setText("إلغاء تحديد الكل")
            self.levels_selected = True
        else:
            # إلغاء تحديد الكل
            for checkbox in self.level_checkboxes:
                checkbox.setChecked(False)
            self.select_all_button.setText("تحديد الكل")
            self.levels_selected = False

        # إعادة تعيين حالة المراحل اللاحقة
        self.calculation_done = False
        self.execute_button.setEnabled(False)

    def show_custom_message(self, title, message, icon_type="warning"):
        """عرض رسالة مخصصة بنفس نمط sub100_window.py"""
        message_dialog = QDialog(self)
        message_dialog.setWindowTitle(title)
        message_dialog.setFixedSize(450, 250)
        message_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            message_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تحديد ألوان النافذة حسب نوع الرسالة
        if icon_type == "warning":
            bg_color = "#fff8f0"
            border_color = "#f39c12"
            title_color = "#d35400"
            button_bg = "#f39c12"
            button_hover_bg = "#e67e22"
        elif icon_type == "error":
            bg_color = "#fff0f0"
            border_color = "#e74c3c"
            title_color = "#c0392b"
            button_bg = "#e74c3c"
            button_hover_bg = "#c0392b"
        elif icon_type == "info":
            bg_color = "#f0f8ff"
            border_color = "#3498db"
            title_color = "#2980b9"
            button_bg = "#3498db"
            button_hover_bg = "#2980b9"
        else:  # success
            bg_color = "#f0fff0"
            border_color = "#2ecc71"
            title_color = "#27ae60"
            button_bg = "#2ecc71"
            button_hover_bg = "#27ae60"

        # تنسيق النافذة
        message_dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {bg_color};
                border: 2px solid {border_color};
                border-radius: 10px;
            }}
            QLabel {{
                color: #333333;
                font-weight: bold;
            }}
            QLabel#message_label {{
                background-color: white;
                border: 1px solid {border_color};
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }}
            QPushButton {{
                background-color: {button_bg};
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {button_hover_bg};
                border: 2px solid {button_bg};
            }}
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(message_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة مناسبة
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # استخدام أيقونة قياسية
            if icon_type == "warning":
                icon = QStyle.standardPixmap(QStyle.SP_MessageBoxWarning, QStyle.StyleOptionButton(), self)
            elif icon_type == "error":
                icon = QStyle.standardPixmap(QStyle.SP_MessageBoxCritical, QStyle.StyleOptionButton(), self)
            elif icon_type == "info":
                icon = QStyle.standardPixmap(QStyle.SP_MessageBoxInformation, QStyle.StyleOptionButton(), self)
            else:  # success
                icon = QStyle.standardPixmap(QStyle.SP_MessageBoxInformation, QStyle.StyleOptionButton(), self)

            icon = QPixmap(icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            if icon_type == "warning":
                icon_label.setText("⚠️")
            elif icon_type == "error":
                icon_label.setText("❌")
            elif icon_type == "info":
                icon_label.setText("ℹ️")
            else:  # success
                icon_label.setText("✓")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet(f"color: {border_color};")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {title_color};")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(message_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        return message_dialog.exec_()

    def check_and_calculate(self):
        """التحقق من إكمال المراحل السابقة قبل الحساب"""
        # التحقق من تحديد المستويات
        selected_levels = []
        for i, checkbox in enumerate(self.level_checkboxes):
            if checkbox.isChecked():
                selected_levels.append(self.available_levels[i])

        if not selected_levels:
            self.show_custom_message("تنبيه", "الرجاء تحديد مستوى واحد على الأقل.", "warning")
            return

        self.levels_selected = True

        # التحقق من إعدادات القاعات
        room_name = self.room_name_input.text()
        if not room_name:
            self.show_custom_message("تنبيه", "الرجاء إدخال اسم القاعة.", "warning")
            return

        self.rooms_configured = True

        # إذا تم إكمال جميع المراحل السابقة، قم بالحساب
        self.calculate_assignment()

    def check_and_execute(self):
        """التحقق من إكمال المراحل السابقة قبل التنفيذ"""
        if not self.levels_selected:
            self.show_custom_message("تنبيه", "الرجاء تحديد المستويات أولاً.", "warning")
            return

        if not self.rooms_configured:
            self.show_custom_message("تنبيه", "الرجاء إكمال إعدادات القاعات.", "warning")
            return

        if not self.calculation_done:
            self.show_custom_message("تنبيه", "الرجاء النقر على زر 'احسب' أولاً.", "warning")
            return

        # إذا تم إكمال جميع المراحل السابقة، قم بالتنفيذ
        self.execute_assignment()

    def calculate_assignment(self):
        """حساب توزيع القاعات"""
        # الحصول على المستويات المحددة
        selected_levels = []
        for i, checkbox in enumerate(self.level_checkboxes):
            if checkbox.isChecked():
                selected_levels.append(self.available_levels[i])

        # الحصول على إعدادات القاعات
        max_students = self.max_students_spin.value()
        room_name = self.room_name_input.text()
        room_number = self.room_number_spin.value()

        # الاتصال بقاعدة البيانات
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # جمع معلومات عن المستويات المحددة
            levels_info = []
            total_students = 0

            for level in selected_levels:
                # الحصول على عدد الطلاب في المستوى
                cursor.execute("SELECT COUNT(*) FROM امتحانات WHERE المستوى = ?", (level,))
                student_count = cursor.fetchone()[0]

                levels_info.append({
                    "level": level,
                    "count": student_count
                })

                total_students += student_count

            self.total_students = total_students

            # حساب عدد القاعات المطلوبة
            if self.option1_radio.isChecked():
                # ملء القاعات إلى سعتها القصوى
                num_rooms = (total_students + max_students - 1) // max_students
            else:
                # توزيع متساوٍ
                num_rooms = (total_students + max_students - 1) // max_students

            # إنشاء نص معلومات المستويات
            levels_text = "معلومات المستويات المحددة:\n\n"
            for info in levels_info:
                levels_text += f"المستوى: {info['level']}\n"
                levels_text += f"عدد المترشحين: {info['count']}\n\n"

            levels_text += f"إجمالي عدد المترشحين: {total_students}\n"
            levels_text += f"أقصى عدد في القاعة: {max_students}\n"
            levels_text += f"عدد القاعات المطلوبة: {num_rooms}\n"

            self.info_text.setText(levels_text)

            # حساب توزيع القاعات
            self.room_assignments = {}

            if self.option1_radio.isChecked():
                # ملء القاعات إلى سعتها القصوى
                rooms_text = "تفاصيل توزيع القاعات (ملء القاعات):\n\n"

                remaining_students = total_students
                for i in range(num_rooms):
                    room_id = f"{room_name} {room_number + i}"
                    students_in_room = min(max_students, remaining_students)
                    remaining_students -= students_in_room

                    self.room_assignments[room_id] = students_in_room
                    rooms_text += f"{room_id}: {students_in_room} مترشح\n"
            else:
                # توزيع متساوٍ
                rooms_text = "تفاصيل توزيع القاعات (توزيع متساوٍ):\n\n"

                base_students_per_room = total_students // num_rooms
                extra_students = total_students % num_rooms

                for i in range(num_rooms):
                    room_id = f"{room_name} {room_number + i}"
                    students_in_room = base_students_per_room
                    if i < extra_students:
                        students_in_room += 1

                    self.room_assignments[room_id] = students_in_room
                    rooms_text += f"{room_id}: {students_in_room} مترشح\n"

            self.details_text.setText(rooms_text)

            # تمكين زر التنفيذ وتحديث حالة المراحل
            self.execute_button.setEnabled(True)
            self.calculation_done = True

            conn.close()

        except Exception as e:
            self.show_custom_message("خطأ", f"فشل في حساب توزيع القاعات: {e}", "error")
            if conn:
                conn.close()

    def execute_assignment(self):
        """تنفيذ توزيع القاعات"""
        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد")
        confirm_dialog.setFixedSize(450, 250)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#message_label {
                background-color: white;
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception:
            # استخدام أيقونة قياسية في حالة الفشل
            icon = QStyle.standardPixmap(QStyle.SP_MessageBoxQuestion, QStyle.StyleOptionButton(), self)
            icon = QPixmap(icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(icon)
            header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد توزيع القاعات")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #3498db;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_label = QLabel(f"هل أنت متأكد من توزيع {self.total_students} مترشح على {len(self.room_assignments)} قاعة؟")
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # عرض النافذة وتحقق من النتيجة
        result = confirm_dialog.exec_()
        if result != QDialog.Accepted:
            return

        # الاتصال بقاعدة البيانات
        conn = self.connect_to_database()
        if not conn:
            return

        try:
            cursor = conn.cursor()

            # الحصول على المستويات المحددة
            selected_levels = []
            for i, checkbox in enumerate(self.level_checkboxes):
                if checkbox.isChecked():
                    selected_levels.append(self.available_levels[i])

            # الحصول على الطلاب في المستويات المحددة مرتبين حسب رقم الامتحان
            placeholders = ', '.join(['?' for _ in selected_levels])
            query = f"""
                SELECT id FROM امتحانات
                WHERE المستوى IN ({placeholders})
                ORDER BY CAST(رقم_الامتحان AS INTEGER)
            """
            cursor.execute(query, selected_levels)
            student_ids = [row[0] for row in cursor.fetchall()]

            # توزيع الطلاب على القاعات
            student_index = 0
            for room_id, count in self.room_assignments.items():
                for i in range(count):
                    if student_index < len(student_ids):
                        # تحديث القاعة للطالب
                        cursor.execute(
                            "UPDATE امتحانات SET القاعة = ? WHERE id = ?",
                            (room_id, student_ids[student_index])
                        )
                        student_index += 1

            # حفظ التغييرات
            conn.commit()
            conn.close()

            # حفظ الإعدادات في النافذة الأم
            if hasattr(self.parent, 'max_students_spin'):
                self.parent.max_students_spin.setValue(self.max_students_spin.value())

            if hasattr(self.parent, 'room_name_input'):
                self.parent.room_name_input.setText(self.room_name_input.text())

            if hasattr(self.parent, 'room_number_spin'):
                self.parent.room_number_spin.setValue(self.room_number_spin.value())

            # تحديث الجدول في النافذة الأم
            if hasattr(self.parent, 'refresh_data'):
                self.parent.refresh_data()

            # عرض رسالة نجاح
            self.show_custom_message(
                "تم بنجاح",
                f"تم توزيع {self.total_students} مترشح على {len(self.room_assignments)} قاعة بنجاح.",
                "success"
            )

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            self.show_custom_message("خطأ", f"فشل في تنفيذ توزيع القاعات: {e}", "error")
            if conn:
                conn.rollback()
                conn.close()

# للاختبار المستقل
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    window = RoomAssignmentDialog(db_path="data.db")
    window.show()
    sys.exit(app.exec_())
