#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

def test_updated_interface():
    """اختبار الواجهة المحدثة مع الأزرار الجديدة"""
    
    print("🔍 اختبار الواجهة المحدثة...")
    print("📋 التحديثات المطبقة:")
    print("   ❌ تم إزالة: تحليل البيانات")
    print("   ❌ تم إزالة: إحصائيات") 
    print("   ❌ تم إزالة: تقارير")
    print("   ❌ تم إزالة: إعدادات")
    print("   ✅ تم الاحتفاظ بـ: التسجيل وإعادة التسجيل")
    print("   ✅ تم الاحتفاظ بـ: أداء الواجبات الشهرية")
    print("   ✅ تم الاحتفاظ بـ: طباعة تقرير مفصل")
    print("   ✅ تم الاحتفاظ بـ: تقرير القسم الشهري")
    print("   ✅ تم الاحتفاظ بـ: التعديل الجماعي")
    print("   🆕 تم إضافة: أداء الواجبات الشهرية لمجموعة")
    print()
    
    try:
        # إنشاء تطبيق PyQt5
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # استيراد النافذة الرئيسية
        from sub252_window import DataViewWindow
        
        # إنشاء النافذة
        window = DataViewWindow()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تشغيل النافذة الرئيسية بنجاح!")
        print("🔍 الأزرار المتاحة الآن:")
        print("   1. 📝 التسجيل وإعادة التسجيل")
        print("   2. 💰 أداء الواجبات الشهرية")
        print("   3. 🖨️ طباعة تقرير مفصل")
        print("   4. 📊 تقرير القسم الشهري")
        print("   5. 🔄 التعديل الجماعي")
        print("   6. 📋 أداء الواجبات الشهرية لمجموعة")
        print()
        print("🆕 مميزات الزر الجديد:")
        print("   • أداء الواجبات الشهرية لعدة تلاميذ دفعة واحدة")
        print("   • نموذج إدخال موحد لجميع التلاميذ المحددين")
        print("   • إمكانية اختيار التلاميذ المراد تطبيق الدفع عليهم")
        print("   • تسجيل المبلغ والتاريخ وحالة الدفع")
        print("   • إضافة ملاحظات اختيارية")
        print("   • حفظ البيانات في جدول_الاداءات")
        print()
        print("📋 خطوات الاستخدام:")
        print("   1. اختر مجموعة من قائمة التصفية")
        print("   2. حدد عدة تلاميذ من الجدول")
        print("   3. اضغط على زر 'أداء الواجبات الشهرية لمجموعة'")
        print("   4. أدخل معلومات الدفع:")
        print("      • المبلغ المدفوع (مع العملة)")
        print("      • الشهر المحدد (يناير، فبراير، مارس، إلخ)")
        print("      • تاريخ الدفع")
        print("      • حالة الدفع (مدفوع كاملاً/جزئياً/غير مدفوع)")
        print("      • ملاحظات إضافية (اختياري)")
        print("   5. راجع قائمة التلاميذ وحدد من تريد تطبيق الدفع عليه")
        print("   6. اضغط على 'تسجيل الدفع'")
        print("   7. أكد العملية")
        print()
        print("⚠️ ملاحظات مهمة:")
        print("   • إذا تم تحديد تلميذ واحد فقط، سيتم فتح نافذة الأداء الفردي")
        print("   • يمكن إلغاء تحديد بعض التلاميذ من قائمة التطبيق")
        print("   • يتم حفظ البيانات في جدول_الاداءات")
        print("   • يمكن إضافة ملاحظات مختلفة لكل عملية دفع")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للجدول الجديد"""
    print("\n🗄️ اختبار هيكل قاعدة البيانات...")
    
    try:
        import sqlite3
        import os
        
        if not os.path.exists('data.db'):
            print("⚠️ ملف قاعدة البيانات غير موجود - سيتم إنشاؤه عند أول استخدام")
            return
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول_الاداءات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_الاداءات'")
        if cursor.fetchone():
            print("✅ جدول_الاداءات موجود")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(جدول_الاداءات)")
            columns = cursor.fetchall()
            print("📋 أعمدة جدول_الاداءات:")
            for col in columns:
                print(f"   • {col[1]} ({col[2]})")
        else:
            print("⚠️ جدول_الاداءات غير موجود - سيتم إنشاؤه عند أول استخدام")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    print("=" * 80)
    print("🧪 اختبار الواجهة المحدثة والزر الجديد")
    print("=" * 80)
    
    # اختبار هيكل قاعدة البيانات
    test_database_structure()
    
    # اختبار الواجهة
    test_updated_interface()
